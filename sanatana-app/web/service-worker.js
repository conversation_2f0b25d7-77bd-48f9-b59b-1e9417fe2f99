// Service Worker for Sanatana Media App
const CACHE_NAME = 'sanatana-app-cache-v1';

// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/static/js/main.bundle.js',
  '/static/css/main.css',
  '/favicon.ico',
  '/manifest.json',
  '/assets/icon.png',
  '/assets/splash-icon.png',
  '/assets/adaptive-icon.png',
];

// Assets that should be cached as they're fetched
const CACHE_EXTENSIONS = [
  '.js',
  '.css',
  '.png',
  '.jpg',
  '.jpeg',
  '.svg',
  '.gif',
  '.woff',
  '.woff2',
  '.ttf',
  '.eot',
  '.ico',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker...');
  
  // Skip waiting to activate immediately
  self.skipWaiting();
  
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log('[Service Worker] Caching static assets');
      return cache.addAll(STATIC_ASSETS);
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker...');
  
  // Claim clients to control all open tabs
  event.waitUntil(self.clients.claim());
  
  // Clean up old caches
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('[Service Worker] Removing old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Helper function to determine if a request should be cached
const shouldCache = (url) => {
  // Don't cache API requests
  if (url.includes('/api/') || 
      url.includes('service.sanatanamedia.com') || 
      url.includes('jobstatus.sanatanamedia.com') ||
      url.includes('ytdown.sanatanamedia.com')) {
    return false;
  }
  
  // Cache static assets by extension
  const extension = url.split('.').pop().toLowerCase();
  if (CACHE_EXTENSIONS.includes(`.${extension}`)) {
    return true;
  }
  
  // Cache specific paths
  if (STATIC_ASSETS.includes(new URL(url).pathname)) {
    return true;
  }
  
  return false;
};

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') return;
  
  // Handle the fetch event
  event.respondWith(
    caches.match(event.request).then((cachedResponse) => {
      // Return cached response if available
      if (cachedResponse) {
        return cachedResponse;
      }
      
      // Otherwise fetch from network
      return fetch(event.request)
        .then((response) => {
          // Don't cache non-successful responses
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }
          
          // Check if this response should be cached
          const url = event.request.url;
          if (shouldCache(url)) {
            // Clone the response as it can only be consumed once
            const responseToCache = response.clone();
            
            // Cache the response
            caches.open(CACHE_NAME).then((cache) => {
              cache.put(event.request, responseToCache);
            });
          }
          
          return response;
        })
        .catch((error) => {
          console.error('[Service Worker] Fetch failed:', error);
          
          // For navigation requests, return the offline page
          if (event.request.mode === 'navigate') {
            return caches.match('/');
          }
          
          // Otherwise just propagate the error
          throw error;
        });
    })
  );
});
