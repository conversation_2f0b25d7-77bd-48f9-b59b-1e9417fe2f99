/**
 * This file contains middleware to add cache headers to server responses
 * It should be included in the server configuration
 */

const addCacheHeaders = (req, res, next) => {
  const url = req.url;
  
  // Set default cache control header
  res.setHeader('Cache-Control', 'no-cache');
  
  // Add Vary header to properly handle cached responses
  res.setHeader('Vary', 'Accept-Encoding, User-Agent');
  
  // Cache static assets aggressively
  if (
    url.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)(\?.*)?$/)
  ) {
    // Cache for 1 week
    res.setHeader(
      'Cache-Control',
      'public, max-age=604800, stale-while-revalidate=86400'
    );
  }
  
  // Cache HTML files for a short time
  else if (url.match(/\.(html)$/)) {
    // Cache for 5 minutes
    res.setHeader(
      'Cache-Control',
      'public, max-age=300, stale-while-revalidate=1800'
    );
  }
  
  // Cache API responses for a short time
  else if (url.includes('/api/')) {
    // Cache for 1 minute, but allow stale responses while revalidating
    res.setHeader(
      'Cache-Control',
      'public, max-age=60, stale-while-revalidate=300'
    );
  }
  
  next();
};

module.exports = addCacheHeaders;
