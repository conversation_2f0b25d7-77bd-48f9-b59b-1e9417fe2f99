// Simple Service Worker for development
const CACHE_NAME = 'sanatana-app-dev-cache-v1';

// Install event
self.addEventListener('install', (event) => {
  console.log('[Dev Service Worker] Installing...');
  self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
  console.log('[Dev Service Worker] Activating...');
  event.waitUntil(self.clients.claim());
});

// Fetch event - only cache static assets
self.addEventListener('fetch', (event) => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') return;
  
  // Only cache static assets
  const url = event.request.url;
  const isStaticAsset = /\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/.test(url);
  
  if (!isStaticAsset) {
    return;
  }
  
  // Handle the fetch event for static assets
  event.respondWith(
    caches.match(event.request).then((cachedResponse) => {
      if (cachedResponse) {
        return cachedResponse;
      }
      
      return fetch(event.request)
        .then((response) => {
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }
          
          const responseToCache = response.clone();
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseToCache);
          });
          
          return response;
        });
    })
  );
});
