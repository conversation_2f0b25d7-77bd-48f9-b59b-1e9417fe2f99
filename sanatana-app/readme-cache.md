# Caching System Documentation for Sanatana Media Expo App

This document provides detailed information about the caching mechanisms implemented in the Sanatana Media Expo app, how they affect operations, debugging techniques, and precautions to take.

## Table of Contents

1. [Overview](#overview)
2. [Caching Mechanisms](#caching-mechanisms)
   - [API Response Caching](#api-response-caching)
   - [Icon Preloading](#icon-preloading)
   - [Service Worker Caching](#service-worker-caching)
   - [YouTube Video Info Caching](#youtube-video-info-caching)
3. [How Caching Affects Operations](#how-caching-affects-operations)
4. [Debugging Caching Issues](#debugging-caching-issues)
5. [Disabling Caching](#disabling-caching)
6. [Caching Analysis Tools](#caching-analysis-tools)
7. [Precautions and Best Practices](#precautions-and-best-practices)
8. [Frequently Asked Questions](#frequently-asked-questions)

## Overview

The Sanatana Media Expo app implements several caching mechanisms to improve performance:

1. **API Response Caching**: Stores API responses in localStorage to reduce network requests
2. **Icon Preloading**: Loads all icons at once during app initialization
3. **Service Worker Caching**: Caches static assets for offline use and faster loading
4. **YouTube Video Info Caching**: Caches YouTube video metadata to avoid repeated API calls

These mechanisms significantly improve app performance, especially on repeat visits and when navigating between screens.

## Caching Mechanisms

### API Response Caching

**Location**: `utils/apiCache.js` and `utils/cachedAxios.js`

**Description**:
The app implements a localStorage-based caching system for API responses. This reduces network requests and improves performance for frequently accessed data.

**Key Features**:
- Configurable cache duration (default: 5 minutes)
- Size-limited cache (default: 50 entries)
- Automatic cache expiration
- Cache hit/miss logging

**Example Usage**:
```javascript
// Import the cached axios instance
import cachedAxios from './utils/cachedAxios';

// Make a request with caching
const response = await cachedAxios.get('/api/endpoint');

// Make a request without caching
const response = await cachedAxios.get('/api/endpoint', { enableCache: false });
```

**Configuration**:
You can modify the cache settings in `utils/apiCache.js`:
```javascript
const API_CACHE_KEY = 'sanatana_api_cache';
const MAX_CACHE_SIZE = 50; // Maximum number of cached responses
const DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
```

### Icon Preloading

**Location**: `utils/iconLoader.js`

**Description**:
Icons are preloaded at app startup to prevent sequential loading, which causes the visible delay where icons appear one after another.

**How it works**:
- A list of all used Ionicons is defined in `iconLoader.js`
- All icons are loaded at once during app initialization in `App.js`
- App only renders after icons are loaded

**Example Usage**:
```javascript
import { preloadIcons } from './utils/iconLoader';

// Preload all icons
await preloadIcons();
```

### Service Worker Caching

**Location**: `web/service-worker.js`

**Description**:
A service worker caches static assets (JS, CSS, images) for offline use and faster loading. This only applies to the web version of the app.

**Key Features**:
- Caches static assets on install
- Serves cached assets for faster loading
- Updates cache when new versions are deployed
- Provides offline functionality

**Cache Configuration**:
```javascript
// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/static/js/main.bundle.js',
  '/static/css/main.css',
  '/favicon.ico',
  '/manifest.json',
  '/assets/icon.png',
  '/assets/splash-icon.png',
  '/assets/adaptive-icon.png',
];

// Assets that should be cached as they're fetched
const CACHE_EXTENSIONS = [
  '.js', '.css', '.png', '.jpg', '.jpeg', '.svg', '.gif',
  '.woff', '.woff2', '.ttf', '.eot', '.ico',
];
```

### YouTube Video Info Caching

**Location**: `components/wizard/YouTubeVideoInfoCard.js`

**Description**:
The app caches YouTube video metadata to avoid making repeated API calls when users navigate back and forth in the wizard with the same video URL.

**How it works**:
- Video info is stored in localStorage with the video URL as the key
- The cache stores about 10 recent video URLs
- Cache entries expire after a configurable duration

**Example Usage**:
```javascript
// Check if we have cached data for this URL
const cachedData = getFromYoutubeCache(videoUrl);
if (cachedData) {
  console.log("Using cached YouTube video info for:", videoUrl);
  setVideoInfo(cachedData);
  return;
}

// If no cached data, fetch from API
const response = await fetch(endpoint, {
  headers: {
    'Cache-Control': 'max-age=3600',
    'Pragma': 'cache',
  },
  // Use cache-first strategy
  cache: 'force-cache',
});
```

## How Caching Affects Operations

### Positive Effects

1. **Faster Loading Times**:
   - Reduced network requests
   - Instant loading of previously visited screens
   - Smoother navigation experience

2. **Reduced Server Load**:
   - Fewer API calls for the same data
   - Less bandwidth usage

3. **Offline Capabilities**:
   - Basic app functionality works offline
   - Previously loaded content remains accessible

4. **Better User Experience**:
   - Icons load all at once instead of sequentially
   - No flickering when navigating between screens
   - Reduced waiting times

### Potential Issues

1. **Stale Data**:
   - Cached data might not reflect the latest server state
   - Critical operations might use outdated information

2. **Cache Invalidation Challenges**:
   - After creating/updating resources, cache needs to be invalidated
   - Complex state management between cached and fresh data

3. **Storage Limitations**:
   - localStorage has a size limit (typically 5-10MB)
   - Excessive caching might hit this limit

4. **Debugging Complexity**:
   - Harder to trace issues when data might be coming from cache
   - Behavior differences between fresh installs and repeat visits

## Debugging Caching Issues

### Browser Developer Tools

**Network Tab**:
- Open browser developer tools (F12)
- Go to Network tab
- Look for responses with status code 304 (Not Modified) or (from cache)
- Check "Disable cache" to bypass browser cache during debugging

**Application Tab**:
- Go to Application > Storage > Local Storage
- Look for entries with keys starting with `sanatana_api_cache`
- You can delete these entries to clear the API cache

**Service Workers**:
- Go to Application > Service Workers
- You can unregister service workers here

### In-App Debugging

**Console Logging**:
The caching system logs cache hits and misses:
```
Using cached response for: /api/endpoint
Fetching from network: /api/endpoint
```

**Cache Inspection**:
```javascript
// In browser console
// View all cached API responses
console.log(JSON.parse(localStorage.getItem('sanatana_api_cache')));

// View YouTube video cache
console.log(JSON.parse(localStorage.getItem('youtube_video_cache')));
```

**Cache Clearing Functions**:
```javascript
// Import the cache utilities
import { clearApiCache, clearExpiredApiCache } from './utils/apiCache';

// Clear the entire API cache
clearApiCache();

// Clear only expired entries
clearExpiredApiCache();

// Clear YouTube video cache
localStorage.removeItem('youtube_video_cache');
```

## Disabling Caching

### Disable API Response Caching

**Globally**:
Edit `utils/cachedAxios.js` and set the default enableCache to false:
```javascript
export const createCachedAxios = (options = {}) => {
  const {
    enableCache = false, // Change this to false to disable caching
    cacheDuration = 5 * 60 * 1000,
  } = options;
  // ...
};
```

**Per Request**:
```javascript
// Disable caching for a specific request
const response = await cachedAxios.get('/api/endpoint', { enableCache: false });
```

### Disable Icon Preloading

In `App.js`, comment out the preloadIcons call:
```javascript
async function prepare() {
  try {
    // Comment this line to disable icon preloading
    // const { preloadIcons } = await import('./utils/iconLoader');
    // await preloadIcons();

    // ...
  } catch (e) {
    // ...
  }
}
```

### Disable Service Worker

In `App.js`, comment out the service worker registration:
```javascript
// Comment these lines to disable service worker
// if (Platform.OS === 'web' && 'serviceWorker' in navigator) {
//   window.addEventListener('load', () => {
//     navigator.serviceWorker.register('/service-worker.js')
//       .then(registration => {
//         console.log('Service Worker registered with scope:', registration.scope);
//       })
//       .catch(error => {
//         console.error('Service Worker registration failed:', error);
//       });
//   });
// }
```

### Disable YouTube Video Info Caching

In `components/wizard/YouTubeVideoInfoCard.js`, comment out the cache check:
```javascript
const fetchVideoInfo = async () => {
  try {
    setLoading(true);
    setError(null);

    // Comment these lines to disable YouTube video info caching
    // const cachedData = getFromYoutubeCache(videoUrl);
    // if (cachedData) {
    //   console.log("Using cached YouTube video info for:", videoUrl);
    //   setVideoInfo(cachedData);
    //   setLoading(false);
    //   return;
    // }

    // Fetch from API...
  } catch (error) {
    // ...
  }
};
```

## Caching Analysis Tools

### 1. Browser Developer Tools

- **Chrome Lighthouse**: Analyzes caching effectiveness
  - Open DevTools > Lighthouse tab
  - Run an audit with "Performance" checked

- **Chrome Network Panel**:
  - Filter by "cached" to see cached resources
  - Look at "Size" column to see "(from cache)" or "304"

### 2. Custom Cache Monitoring

Add this code to monitor cache performance:
```javascript
// In App.js or a utility file
window.cacheMonitor = {
  apiCacheHits: 0,
  apiCacheMisses: 0,
  ytCacheHits: 0,
  ytCacheMisses: 0,

  logStats() {
    const apiHitRate = this.apiCacheHits / (this.apiCacheHits + this.apiCacheMisses) * 100 || 0;
    const ytHitRate = this.ytCacheHits / (this.ytCacheHits + this.ytCacheMisses) * 100 || 0;

    console.log(`API Cache: ${this.apiCacheHits} hits, ${this.apiCacheMisses} misses (${apiHitRate.toFixed(2)}% hit rate)`);
    console.log(`YouTube Cache: ${this.ytCacheHits} hits, ${this.ytCacheMisses} misses (${ytHitRate.toFixed(2)}% hit rate)`);
  }
};

// Then in browser console
window.cacheMonitor.logStats();
```

## Precautions and Best Practices

### 1. Data Freshness Considerations

**Critical data that should always be fresh**:
- Job status information
- Authentication status
- User profile data

**Data that can be cached safely**:
- YouTube video metadata
- Static configuration data
- Historical job data

### 2. Cache Invalidation

**When to invalidate cache**:
- After creating/updating/deleting resources
- When user logs out
- When app version changes

**How to invalidate cache**:
```javascript
// Import the cache utilities
import { clearApiCache } from './utils/apiCache';

// After a POST/PUT/DELETE operation
await axios.post('/api/resource');
clearApiCache(); // Clear the entire cache

// Or clear specific entries
localStorage.removeItem('sanatana_api_cache_/api/specific-endpoint');
```

### 3. Testing Considerations

- Always test with both empty and primed caches
- Test with network throttling to see cache benefits
- Verify that critical data is always fresh

## Troubleshooting Common Errors

### 1. `clearExpiredApiCache is not defined` Error

This error occurs when trying to use the `clearExpiredApiCache` function without properly importing it.

**Solution**:
```javascript
// Import apiCache dynamically to avoid circular dependencies
import('./utils/apiCache').then(({ clearExpiredApiCache }) => {
  clearExpiredApiCache();
}).catch(err => {
  console.error('Error clearing expired API cache:', err);
});
```

### 2. Service Worker MIME Type Error

Error: `Failed to register a ServiceWorker: The script has an unsupported MIME type ('text/html')`

This occurs when the service worker file doesn't exist at the specified location or is being served with the wrong MIME type.

**Solutions**:
1. Make sure the service worker file exists in the correct location
2. Use a relative path instead of an absolute path:
   ```javascript
   const swPath = `${window.location.pathname.replace(/\/+$/, '')}/service-worker.js`;
   navigator.serviceWorker.register(swPath);
   ```
3. Only register the service worker in production or localhost:
   ```javascript
   const isLocalhost = window.location.hostname === 'localhost';
   const isProduction = process.env.NODE_ENV === 'production';
   if (isProduction || isLocalhost) {
     // Register service worker
   }
   ```

### 3. `Cannot read properties of undefined (reading 'cachedData')` Error

This error occurs in the cachedAxios error handler when trying to access `error.config.cachedData` but `error.config` is undefined.

**Solution**:
```javascript
// Add proper error checking in the response interceptor
if (axios.isCancel(error) && error.message === 'Request cancelled, using cached data') {
  // Make sure error.config exists before accessing its properties
  if (error.config?.cachedData) {
    // Return the cached data as a successful response
    return Promise.resolve({
      data: error.config.cachedData,
      status: 200,
      statusText: 'OK (Cached)',
      headers: {},
      config: error.config,
      cached: true,
    });
  } else {
    console.error('Cache error: config or cachedData is undefined', error);
    // Continue with the request instead of using cache
    return Promise.reject(new Error('Cache error: config or cachedData is undefined'));
  }
}
```

### 4. Authentication Requests and Caching

Authentication requests should never be cached. For these requests, it's better to use axios directly instead of cachedAxios.

**Solution**:
```javascript
// Import axios directly for authentication requests
const axios = (await import('axios')).default;

const response = await axios.get(
  `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/get_sanatana_app_user`,
  {
    params: { email },
  }
);
```

### 5. Icon Loading Issues

If icons are still loading sequentially despite the preloading mechanism:

**Solutions**:
1. Check browser console for errors in the preloadIcons function
2. Make sure the USED_IONICONS array in iconLoader.js includes all icons used in the app
3. Try using a different preloading approach:
   ```javascript
   // Alternative icon preloading approach
   const preloadIconsAlternative = async () => {
     // Load Ionicons font
     await Font.loadAsync(Ionicons.font);

     // Force render of all icons in a hidden div
     if (typeof document !== 'undefined') {
       const iconNames = ['home', 'settings', 'help-circle', 'person'];
       const div = document.createElement('div');
       div.style.opacity = '0';
       div.style.position = 'absolute';
       div.style.pointerEvents = 'none';

       iconNames.forEach(name => {
         const span = document.createElement('span');
         span.className = `ionicon ionicon-${name}`;
         div.appendChild(span);
       });

       document.body.appendChild(div);
       setTimeout(() => document.body.removeChild(div), 1000);
     }
   };
   ```

## Frequently Asked Questions

### How do I know if something is being cached when it shouldn't be?

1. Check the Network tab in browser developer tools
2. Look for responses with status 304 or "from cache"
3. Verify the Cache-Control headers
4. Use the browser's "Disable cache" option to compare behavior

### How can I force a fresh request for a specific endpoint?

```javascript
// Method 1: Disable cache for this request
const response = await cachedAxios.get('/api/endpoint', { enableCache: false });

// Method 2: Add a timestamp to bust cache
const response = await cachedAxios.get(`/api/endpoint?_t=${Date.now()}`);
```

### What happens if the cache gets corrupted?

The caching system has several safeguards:
1. Try-catch blocks around all cache operations
2. Automatic fallback to network requests if cache read fails
3. Cache entry expiration to prevent stale data
4. Size limits to prevent memory issues

If you suspect cache corruption, you can clear it:
```javascript
localStorage.clear(); // Clear all localStorage
// or
clearApiCache(); // Clear only API cache
```

### How much data is being cached?

The API cache is limited to 50 entries by default (configurable in `apiCache.js`).
Each entry contains:
- The API response data
- Timestamp
- Expiration time

You can check the current cache size:
```javascript
// In browser console
const cacheSize = JSON.stringify(localStorage.getItem('sanatana_api_cache')).length / 1024;
console.log(`Cache size: ${cacheSize.toFixed(2)} KB`);
```

### Does caching work in development mode?

Yes, all caching mechanisms work in development mode, but:
1. Service worker caching only works in production builds
2. Browser cache may be disabled if DevTools is open with "Disable cache" checked
3. React's hot reloading may interfere with some caching behaviors

For testing caching in development:
1. Close DevTools or uncheck "Disable cache"
2. Use incognito/private browsing window
3. Use production build for service worker testing
