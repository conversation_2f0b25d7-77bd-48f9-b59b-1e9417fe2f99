{"name": "sanatana-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --reset-cache --clear", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web --clear --port 5003", "clean": "rm -rf $TMPDIR/metro-* && rm -rf node_modules/.cache"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/datetimepicker": "8.2.0", "@react-native-firebase/app": "^21.12.2", "@react-native-firebase/auth": "^21.12.2", "@react-native-firebase/crashlytics": "^21.12.2", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.3.3", "@react-navigation/material-top-tabs": "^7.2.3", "@react-navigation/native": "^7.0.19", "@react-navigation/native-stack": "^7.3.3", "@react-navigation/stack": "^7.2.3", "@rneui/themed": "^4.0.0-rc.8", "axios": "^1.9.0", "base-64": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "expo": "~52.0.40", "expo-checkbox": "^4.0.1", "expo-clipboard": "^7.0.1", "expo-crypto": "^14.0.2", "expo-dev-client": "~5.0.14", "expo-font": "^13.0.4", "expo-splash-screen": "^0.29.22", "expo-status-bar": "~2.0.1", "expo-web-browser": "^14.0.2", "googleapis": "^148.0.0", "guacamole-common-js": "^1.5.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-elements": "^3.4.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.20.2", "react-native-mmkv": "^3.2.0", "react-native-paper": "^5.13.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "^13.12.5", "react-native-youtube-iframe": "^2.3.0", "tailwindcss": "3.4.17"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}