import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useNavigation, CommonActions } from '@react-navigation/native';
import UploadWizard from '../components/UploadWizard';

/**
 * A screen that directly shows the UploadWizard
 */
const NewUploadScreen = () => {
  const navigation = useNavigation();
  const [showError, setShowError] = useState(false);

  // Try to navigate to Home after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      try {
        // Reset navigation and go to Home
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: 'Home', params: { openWizard: true, timestamp: Date.now() } }],
          })
        );
      } catch (error) {
        console.error('Navigation error:', error);
        setShowError(true);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [navigation]);

  // Just show the wizard directly
  return (
    <View style={{ flex: 1 }}>
      {/* Show the wizard directly */}
      <UploadWizard
        visible={true}
        onClose={() => {
          navigation.navigate('Home');
        }}
      />

      {/* Show error message if navigation fails */}
      {showError && (
        <View style={{
          position: 'absolute',
          bottom: 20,
          left: 20,
          right: 20,
          backgroundColor: 'rgba(255,0,0,0.1)',
          padding: 10,
          borderRadius: 8
        }}>
          <Text style={{ color: 'red' }}>Navigation error. Please try again.</Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('Home')}
            style={{ marginTop: 10, padding: 8, backgroundColor: '#4F46E5', borderRadius: 4 }}
          >
            <Text style={{ color: 'white', textAlign: 'center' }}>Go to Home</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default NewUploadScreen;
