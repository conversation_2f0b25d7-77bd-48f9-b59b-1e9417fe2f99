import React, { useState, useContext, useEffect } from "react";
import PropTypes from "prop-types";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Dimensions,
} from "react-native";
import { Icon } from "@rneui/themed";
import { UserContext } from "../context/UserContext";
import Ionicons from "@expo/vector-icons/Ionicons";
import YoutubeSetup from "../components/YoutubeSetup";
import TiktokSetup from "../components/TiktokSetup";
import BackToWizardButton from "../components/common/BackToWizardButton";
import { useNavigation } from '@react-navigation/native';
import ResponsiveContainer from "../components/layout/ResponsiveContainer";
import AnimatedLogo from "../components/common/AnimatedLogo";
import WatermarkLogo from "../components/common/WatermarkLogo";

const isMobile =  Dimensions.get('window').width < 768;

const MediaPlatforms = [
  {
    id: "youtube",
    title: "YouTube",
    icon: "youtube",
    type: "font-awesome",
    enabled: true,
  },

  {
    id: "tiktok",
    title: "Popular Social Media Platforms",
    icon: "music",
    type: "font-awesome",
    enabled: true,
  },
  {
    id: "facebook",
    title: "Facebook",
    icon: "facebook",
    type: "font-awesome",
    enabled: false,
  },
  {
    id: "instagram",
    title: "Instagram",
    icon: "instagram",
    type: "font-awesome",
    enabled: false,
  },

  {
    id: "twitter",
    title: "X",
    icon: "twitter",
    type: "font-awesome",
    enabled: false,
  },
  {
    id: "bluesky",
    title: "BlueSky",
    icon: "cloud",
    type: "font-awesome",
    enabled: false,
  },
  {
    id: "linkedin",
    title: "LinkedIn",
    icon: "linkedin",
    type: "font-awesome",
    enabled: false,
  },
  {
    id: "discord",
    title: "Discord",
    icon: "discord",
    type: "font-awesome-5",
    enabled: false,
  },
  {
    id: "substack",
    title: "Substack",
    icon: "newspaper",
    type: "font-awesome-5",
    enabled: false,
  },
];

const SetupMedia = ({ route }) => {
  const { user } = useContext(UserContext);
  const [expandedSection, setExpandedSection] = useState(null);
  const navigation = useNavigation();

  useEffect(() => {
    if (!user) {
      console.log("Setup Media: No user");
      return;
    }
    if (user) {
      console.log("Setup Media: User now loaded:", user);
    }
  }, [user]);

  // Handle default_open_id parameter from URL or route params
  useEffect(() => {
    // Check for URL parameters first (for web)
    const urlParams = new URLSearchParams(window.location.search);
    const defaultOpenId = urlParams.get("default_open_id");

    // If not in URL, check route params (for native)
    const routeDefaultOpenId = route?.params?.default_open_id;

    const idToOpen = defaultOpenId || routeDefaultOpenId;

    if (idToOpen) {
      console.log(`SetupMedia: Opening section for ${idToOpen}`);
      // Find the platform that matches the ID
      const platform = MediaPlatforms.find((p) => p.id === idToOpen);

      if (platform?.enabled) {
        // Open the section
        setExpandedSection(idToOpen);

        // Scroll to the section (with a slight delay to ensure rendering)
        setTimeout(() => {
          const element = document.getElementById(`platform-${idToOpen}`);
          if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "start" });
          }
        }, 300);
      }
    }
  }, [route]);

  const toggleSection = (section) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  let cnt = 1
  // Function to handle going back to the wizard
  const handleBackToWizard = () => {


    if (!isMobile) {
      // For web, navigate to the home page with a parameter to open the wizard
      window.location.href = "/Home?openWizard=true";
    } else {
      // For native, use navigation with params
      try {

       navigation.navigate('Home', {
        openWizard: "true",
                  });

      } catch (error) {
        console.error("Navigation error:", error);
      }
    }
    cnt++
  };

  // Check if we came from the wizard
  const cameFromWizard =
    route?.params?.default_open_id ||
    new URLSearchParams(window.location.search).get("default_open_id");

  return (
    <SafeAreaView style={{ flex: 1 }} className="bg-gray-100">
      <View className="flex-1 bg-white">
        {/* Show Back to Wizard button only if we came from the wizard */}
        {cameFromWizard && <BackToWizardButton onPress={handleBackToWizard} />}
        {/* Add padding to the top to make room for the button */}
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            paddingBottom: 20,
            paddingTop: cameFromWizard ? 80 : 4
          }}
          showsVerticalScrollIndicator={false}
        >
          <ResponsiveContainer className="p-4 relative">
            {/* Animated logo in the top-right corner, positioned to avoid chevron */}
            <View style={{
              position: 'absolute',
              top: isMobile ? 20 : 17, // 3px lower on mobile
              right: 65,
              zIndex: 10
            }}>
              <AnimatedLogo
                size={isMobile ? 40 : 60}
                animation="hueShift"
                backgroundColor="transparent"
              />
            </View>
            <View style={{
              position: 'absolute',
              top: isMobile ? 98 : 95, // 3px lower on mobile
              right: 65,
              zIndex: 10
            }}>
              <AnimatedLogo
                size={isMobile ? 40 : 60}
                animation="hueShift"
                backgroundColor="transparent"
              />
            </View>
            {/* Background watermark logo */}
            <WatermarkLogo
              size={300}
              opacity={0.03}
              position="center"
              style={{ position: 'absolute', width: '100%', height: '100%', zIndex: -1 }}
            />

            {MediaPlatforms.filter((dest) => dest.enabled === true).map(
              (dest) => (
                <View
                  key={dest.id}
                  id={`platform-${dest.id}`}
                  className={`mb-4 border rounded-lg overflow-hidden ${
                    dest.enabled ? "border-green-600" : "border-gray-300"
                  }`}
                >
                  <TouchableOpacity
                    onPress={() => toggleSection(dest.id)}
                    className={`flex-row justify-between items-center p-4 ${
                      dest.enabled
                        ? expandedSection === dest.id
                          ? "bg-green-600"
                          : "bg-sky-300"
                        : "bg-gray-100"
                    }`}
                  >
                    <View className="flex-row justify-start ">
                      <Icon
                        name={dest.icon}
                        type={dest.type || "font-awesome"}
                        size={24}
                        color={
                          dest.enabled
                            ? expandedSection === dest.id
                              ? "#FFFFFF"
                              : "#166534"
                            : "#9CA3AF"
                        }
                      />
                      <Text
                        className={`text-lg font-semibold mx-10 ${
                          expandedSection === dest.id && dest.enabled
                            ? "text-white"
                            : "text-green-600"
                        }`}
                      >
                        {dest.title}
                      </Text>
                    </View>
                    <Ionicons
                      name={
                        expandedSection === dest.id ? "arrow-up" : "arrow-down"
                      }
                      size={24}
                      color={
                        dest.enabled
                          ? expandedSection === dest.id
                            ? "#FFFFFF"
                            : "#166534"
                          : "#9CA3AF"
                      }
                    />
                  </TouchableOpacity>

                  {expandedSection === dest.id && (
                    <View className="p-1">
                      {dest.id === "youtube" ? (
                        <YoutubeSetup onBackToWizard={handleBackToWizard} />
                      ) : dest.id === "tiktok" ? (
                        <TiktokSetup onBackToWizard={handleBackToWizard} />
                      ) : (
                        <Text className="text-gray-600">
                          Setup for {dest.title} coming soon...
                        </Text>
                      )}
                    </View>
                  )}
                </View>
              )
            )}
            {MediaPlatforms.filter((dest) => dest.enabled === false).map(
              (dest) => (
                <View
                  key={dest.id}
                  id={`platform-${dest.id}`}
                  className="mb-4 border border-gray-300 rounded-lg overflow-hidden"
                >
                  <TouchableOpacity
                    onPress={() => toggleSection(dest.id)}
                    className="flex-row justify-start items-center p-4 bg-gray-50"
                  >
                    {" "}
                    <Icon
                      name={dest.icon}
                      type={dest.type || "font-awesome"}
                      size={24}
                      color={
                        dest.enabled
                          ? expandedSection === dest.id
                            ? "#3B82F6"
                            : "#4B5563"
                          : "#9CA3AF"
                      }
                    />
                    <Text className="text-lg font-semibold mx-10">
                      {dest.title}
                    </Text>
                  </TouchableOpacity>
                </View>
              )
            )}
          </ResponsiveContainer>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

// Add PropTypes validation
SetupMedia.propTypes = {
  route: PropTypes.shape({
    params: PropTypes.shape({
      default_open_id: PropTypes.string,
    }),
  }),
};

// Default props
SetupMedia.defaultProps = {
  route: { params: {} },
};

export default SetupMedia;
