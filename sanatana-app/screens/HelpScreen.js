import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Linking,
  ActivityIndicator
} from 'react-native';
import { Icon } from '@rneui/themed';
import ResponsiveContainer from '../components/layout/ResponsiveContainer';
import AudienceShowcase from '../components/AudienceShowCase';
import YoutubeTutorialCard from '../components/home/<USER>';
import { getPlatformTutorials } from '../utils/tutorialUtils';

const windowHeight = Dimensions.get("window").height;

// FAQ data
const faqSections = [
  {
    title: "Getting Started",
    faqs: [
      {
        question: "What is Sanatana Media?",
        answer: "Sanatana Media is a powerful social media automation platform that helps you share your content across multiple platforms with just a few clicks. Our service streamlines the process of uploading videos to various social media channels, saving you time and effort."
      },
      {
        question: "How do I start using Sanatana Media?",
        answer: "To get started, simply click on the 'Start New Upload' button on the Home screen. Our step-by-step wizard will guide you through the process of selecting your content source, choosing destinations, customizing your post, and scheduling your upload."
      },
      {
        question: "Is Sanatana Media free to use?",
        answer: "Yes! During our beta phase, all services are completely free. Beta users will also receive lifetime free services and significant discounts on future premium features. Join now to lock in these benefits!"
      },
      {
        question: "What platforms can I upload to?",
        answer: "Currently, we support YouTube (both regular videos and Shorts). We're actively working on adding support for TikTok, Instagram, Facebook, Twitter, and other popular platforms in the near future."
      },
      {
        question: "Do I need to create an account?",
        answer: "Yes, you need to sign in with your Google account to use Sanatana Media. This allows us to securely manage your uploads and provide personalized services."
      }
    ]
  },
  {
    title: "YouTube Authorization",
    faqs: [
      {
        question: "Is it safe to authorize Sanatana Media to access my YouTube account?",
        answer: "Absolutely! Sanatana Media uses Google's official OAuth 2.0 authorization process, which is secure and doesn't give us access to your Google password. We can only perform the specific actions you authorize, such as uploading videos to your YouTube channel. You can revoke this access at any time through your Google account settings."
      },
      {
        question: "Why do I need to upload YouTube API credentials?",
        answer: "YouTube requires API credentials (OAuth client ID) to allow applications like ours to upload videos on your behalf. These credentials are specific to your Google account and ensure that uploads are properly attributed to you. Our tutorial videos show you exactly how to create and upload these credentials."
      },
      {
        question: "Can I use multiple YouTube channels with Sanatana Media?",
        answer: "Yes! You can authorize multiple YouTube channels from different Google accounts. This allows you to manage and upload content to all your channels from a single Sanatana Media account."
      },
      {
        question: "Will Sanatana Media have access to delete my videos or change my account settings?",
        answer: "No. The permissions we request are limited to uploading videos and managing playlists. We cannot delete your existing videos, change your account settings, or access your personal Google data."
      },
      {
        question: "What happens if I revoke Sanatana Media's access to my YouTube account?",
        answer: "If you revoke access, Sanatana Media will no longer be able to upload videos to your YouTube channel. Any scheduled uploads that haven't been processed will fail. You can always re-authorize access later if you wish to continue using our services."
      }
    ]
  },
  {
    title: "Upload Process",
    faqs: [
      {
        question: "How do I upload a video using Sanatana Media?",
        answer: "Our upload wizard makes it simple: 1) Select your content source (e.g., YouTube URL), 2) Choose your destination platforms, 3) Customize your title, description, and hashtags if desired, 4) Schedule your upload or post immediately, 5) Confirm and submit. You can track the status of your upload in the Job Status screen."
      },
      {
        question: "Can I schedule uploads for a later time?",
        answer: "Yes! You can schedule uploads for any future date and time. This is perfect for planning your content calendar and ensuring consistent posting across your social media channels."
      },
      {
        question: "What happens if an upload fails?",
        answer: "If an upload fails, you'll see the specific error in the Job Status screen. Common issues include network problems, authorization errors, or content restrictions. Failed jobs can be automatically retried based on the error type."
      },
      {
        question: "Can I upload the same video to multiple platforms at once?",
        answer: "Yes! This is one of the core features of Sanatana Media. You can select multiple destination platforms for your content and upload to all of them simultaneously or on a schedule you define."
      },
      {
        question: "Is there a limit to how many videos I can upload?",
        answer: "During the beta phase, there are no strict limits on the number of uploads. However, each platform has its own rate limits and quotas. For example, YouTube limits the number of uploads per day based on your channel's standing."
      }
    ]
  },
  {
    title: "Content Management",
    faqs: [
      {
        question: "Can I use content from other creators?",
        answer: "You should only upload content that you have the rights to use. This includes your own original content or content with appropriate licenses that permit redistribution. Always respect copyright laws and platform terms of service."
      },
      {
        question: "Can I customize the title and description for each platform?",
        answer: "Yes! You can use the original title and description from the source, create custom ones, or let our system generate optimized text for each platform. This flexibility allows you to tailor your content for different audiences."
      },
      {
        question: "Does Sanatana Media support hashtags?",
        answer: "Yes, you can add custom hashtags to your uploads. We also offer an AI-powered hashtag suggestion feature that recommends relevant tags based on your content to maximize visibility."
      },
      {
        question: "What video formats are supported?",
        answer: "We support all major video formats including MP4, MOV, AVI, and more. Our system automatically handles any necessary format conversions required by the destination platforms."
      },
      {
        question: "Is there a maximum video length or size?",
        answer: "The maximum length and size depend on the destination platform. For example, YouTube Shorts must be under 60 seconds, while regular YouTube videos can be up to 12 hours (for verified accounts). Our system will notify you if your content exceeds platform-specific limits."
      }
    ]
  },
  {
    title: "Job Status",
    faqs: [
      {
        question: "How do I check the status of my uploads?",
        answer: "Navigate to the Job Status screen to see all your uploads. Each job card shows the current status, destination platforms, and detailed history. You can also expand the cards to see more information."
      },
      {
        question: "What do the different job statuses mean?",
        answer: "Common statuses include: 'Created' (job submitted), 'Processing' (download/upload in progress), 'Completed' (successfully uploaded), 'Failed' (error occurred), 'Scheduled' (waiting for scheduled time), and 'Retrying' (attempting again after failure)."
      },
      {
        question: "Can I cancel an upload after it's submitted?",
        answer: "Currently, we don't support canceling jobs that are already in progress. However, you can delete scheduled jobs that haven't started processing yet."
      },
      {
        question: "What happens if I reach my YouTube upload limit?",
        answer: "If you reach your YouTube upload limit, the job will be automatically rescheduled for the next day. You'll see the status 'Youtube_uploadLimitExceeded' and the new scheduled time in the job history."
      },
      {
        question: "How long are job records kept?",
        answer: "Job records are currently kept indefinitely, allowing you to track your upload history over time. You can access completed, failed, and scheduled jobs from the Job Status screen."
      }
    ]
  },
  {
    title: "Technical Questions",
    faqs: [
      {
        question: "What browsers are supported?",
        answer: "Sanatana Media works best on modern browsers like Chrome, Firefox, Safari, and Edge. For the best experience, we recommend using the latest version of Chrome or Firefox."
      },
      {
        question: "Is there a mobile app available?",
        answer: "Our web application is fully responsive and works well on mobile devices. While we don't have a dedicated mobile app yet, you can add our website to your home screen for an app-like experience."
      },
      {
        question: "How secure is my data with Sanatana Media?",
        answer: "We take security seriously. All communications are encrypted using HTTPS, and we don't store your passwords. OAuth tokens are securely stored and used only for the specific actions you authorize."
      },
      {
        question: "What happens if I lose internet connection during an upload?",
        answer: "Our system is designed to handle interruptions. If you lose connection during the submission process, your draft will be saved. If a job is already being processed, our servers will continue the upload independently of your connection."
      },
      {
        question: "Does Sanatana Media work in all countries?",
        answer: "Yes, Sanatana Media is available worldwide. However, access to certain platforms may be restricted in some countries due to local regulations or platform availability."
      }
    ]
  },
  {
    title: "Pricing and Plans",
    faqs: [
      {
        question: "How much does Sanatana Media cost?",
        answer: "During our beta phase, all services are completely free! Beta users will also receive lifetime free services and significant discounts on future premium features."
      },
      {
        question: "Will there be different pricing tiers in the future?",
        answer: "Yes, we plan to introduce different pricing tiers with varying features and upload limits. However, beta users will always have access to special pricing and benefits."
      },
      {
        question: "Is there a limit to how many accounts I can connect?",
        answer: "During the beta phase, there's no limit to the number of social media accounts you can connect to Sanatana Media."
      },
      {
        question: "Do you offer refunds?",
        answer: "Since our service is currently free during the beta phase, there's no need for refunds. When we introduce paid plans, we'll have a clear refund policy in place."
      },
      {
        question: "Are there any hidden fees?",
        answer: "No, there are no hidden fees. We believe in transparent pricing and will clearly communicate any costs associated with our services when we introduce paid plans."
      }
    ]
  }
];

// Contact information
const contactInfo = {
  email: "<EMAIL>",
  telegram: "https://t.me/sanatanamedia",
  whatsapp: "https://chat.whatsapp.com/BMk2BzrFAj9KjhjcdW14YD",
  helpWebsite: "https://www.sanatanamedia.com/Help"
};

const HelpScreen = () => {
  const [tutorials, setTutorials] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedSection, setExpandedSection] = useState(null);
  const [expandedFaq, setExpandedFaq] = useState({});

  // Fetch tutorials when component mounts
  useEffect(() => {
    const fetchTutorials = async () => {
      try {
        setLoading(true);
        // Generate a timestamp to force fresh data
        const timestamp = Date.now();
        console.log(`HelpScreen: Fetching fresh tutorials data with timestamp ${timestamp}`);
        const platformTutorials = await getPlatformTutorials(timestamp);
        setTutorials(platformTutorials);
      } catch (error) {
        console.error('Error fetching tutorials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTutorials();
  }, []);

  // Toggle FAQ expansion
  const toggleFaq = (sectionTitle, faqQuestion) => {
    setExpandedFaq(prev => {
      const key = `${sectionTitle}-${faqQuestion}`;
      return {
        ...prev,
        [key]: !prev[key]
      };
    });
  };

  // Toggle section expansion
  const toggleSection = (sectionTitle) => {
    if (sectionTitle === 'Beneficiaries') return; // Beneficiaries section doesn't close

    // For Contact section, don't allow it to be closed completely
    if (sectionTitle === 'Contact') {
      // If it's already expanded, keep it expanded
      return;
    }

    setExpandedSection(expandedSection === sectionTitle ? null : sectionTitle);
  };

  return (
    <SafeAreaView style={{ height: windowHeight }} className="bg-gray-100">
      <ScrollView>
        <ResponsiveContainer className='p-4 pb-32'>
          {/* Header */}
          <View className='mb-6'>
            <Text className='text-3xl font-bold mb-2 text-indigo-800'>Help Center</Text>
            <Text className='text-lg text-gray-600'>
              Welcome to Sanatana Media's Help Center! Find answers to common questions and learn how to make the most of our platform.
            </Text>
          </View>

          {/* Beneficiaries Section - Always open */}
          <View className='mb-8'>
            <TouchableOpacity
              onPress={() => toggleSection('Beneficiaries')}
              className='flex-row justify-between items-center bg-indigo-100 p-4 rounded-t-lg border-l-4 border-indigo-600'
            >
              <Text className='text-xl font-bold text-indigo-800'>Who Benefits from Sanatana Media?</Text>
              <Icon name="people" type="ionicon" size={24} color="#4F46E5" />
            </TouchableOpacity>

            <View className='bg-white p-4 rounded-b-lg shadow-md mb-4'>
              <Text className='text-gray-700 mb-4'>
                Sanatana Media helps a wide range of users automate their social media presence. Explore who benefits most from our platform:
              </Text>
              <AudienceShowcase />
            </View>
          </View>

          {/* Tutorials Section */}
          <View className='mb-8'>
            <TouchableOpacity
              onPress={() => toggleSection('Tutorials')}
              className={`flex-row justify-between items-center p-4 rounded-t-lg border-l-4 ${expandedSection === 'Tutorials' ? 'bg-blue-100 border-blue-600' : 'bg-gray-100 border-gray-300'}`}
            >
              <Text className={`text-xl font-bold ${expandedSection === 'Tutorials' ? 'text-blue-800' : 'text-gray-800'}`}>Tutorial Videos</Text>
              <Icon
                name={expandedSection === 'Tutorials' ? "chevron-up" : "chevron-down"}
                type="font-awesome"
                size={20}
                color={expandedSection === 'Tutorials' ? "#2563EB" : "#4B5563"}
              />
            </TouchableOpacity>

            {expandedSection === 'Tutorials' && (
              <View className='bg-white p-4 rounded-b-lg shadow-md mb-4'>
                {loading && (
                  <View className='items-center justify-center py-8'>
                    <ActivityIndicator size="large" color="#4F46E5" />
                    <Text className='text-gray-600 mt-4'>Loading tutorials...</Text>
                  </View>
                )}
                {!loading && tutorials.length > 0 && (
                  <ScrollView horizontal={false}>
                    {tutorials.map((tutorial) => (
                      <YoutubeTutorialCard
                        key={tutorial.name}
                        title={tutorial.name}
                        youtubeUrl={tutorial.url}
                        tutorialName={tutorial.name}
                        caption={`Learn about ${tutorial.name.split(' - ')[0]}`}
                      />
                    ))}
                  </ScrollView>
                )}
                {!loading && tutorials.length === 0 && (
                  <Text className='text-gray-600 text-center py-4'>No tutorials available. Please check back later.</Text>
                )}
              </View>
            )}
          </View>

          {/* FAQ Sections */}
          {faqSections.map((section) => (
            <View key={section.title} className='mb-6'>
              <TouchableOpacity
                onPress={() => toggleSection(section.title)}
                className={`flex-row justify-between items-center p-4 rounded-t-lg border-l-4 ${expandedSection === section.title ? 'bg-green-100 border-green-600' : 'bg-gray-100 border-gray-300'}`}
              >
                <Text className={`text-xl font-bold ${expandedSection === section.title ? 'text-green-800' : 'text-gray-800'}`}>{section.title}</Text>
                <Icon
                  name={expandedSection === section.title ? "chevron-up" : "chevron-down"}
                  type="font-awesome"
                  size={20}
                  color={expandedSection === section.title ? "#059669" : "#4B5563"}
                />
              </TouchableOpacity>

              {expandedSection === section.title && (
                <View className='bg-white rounded-b-lg shadow-md'>
                  {section.faqs.map((faq) => {
                    const isExpanded = expandedFaq[`${section.title}-${faq.question}`];
                    return (
                      <View key={faq.question} className='border-b border-gray-200 last:border-b-0'>
                        <TouchableOpacity
                          onPress={() => toggleFaq(section.title, faq.question)}
                          className='p-4 flex-row justify-between items-center'
                        >
                          <Text className='text-lg font-medium text-gray-800 flex-1 pr-4'>{faq.question}</Text>
                          <Icon
                            name={isExpanded ? "minus" : "plus"}
                            type="font-awesome"
                            size={16}
                            color="#4B5563"
                          />
                        </TouchableOpacity>

                        {isExpanded && (
                          <View className='px-4 pb-4 pt-1'>
                            <Text className='text-gray-600 leading-relaxed'>{faq.answer}</Text>
                          </View>
                        )}
                      </View>
                    );
                  })}
                </View>
              )}
            </View>
          ))}

          {/* Contact and Support Section - Always visible */}
          <View className='mb-24 pb-12'>
            <View
              className='flex-row justify-between items-center p-4 rounded-t-lg border-l-4 bg-purple-100 border-purple-600'
            >
              <Text className='text-xl font-bold text-purple-800'>Contact and Support</Text>
              <Icon
                name="headset"
                type="font-awesome-5"
                size={20}
                color="#7C3AED"
              />
            </View>

            {/* Always visible content */}
              <View className='bg-white p-4 pb-16 rounded-b-lg shadow-md'>
                <Text className='text-gray-700 mb-4'>
                  Our support team is limited in size, but we are committed to providing assistance as quickly as possible through all available platforms. We typically respond within 24-48 hours. Here are the ways you can reach us:
                </Text>

                <View className='bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4'>
                  <Text className='text-yellow-800'>
                    <Text className='font-bold'>Note:</Text> For the fastest response, we recommend joining our Telegram or WhatsApp groups where our team and community members can help answer your questions.
                  </Text>
                </View>

                <View className='space-y-4'>
                  {/* Email */}
                  <TouchableOpacity
                    onPress={() => Linking.openURL(`mailto:${contactInfo.email}`)}
                    className='flex-row items-center p-3 bg-gray-50 rounded-lg'
                  >
                    <Icon name="envelope" type="font-awesome" size={20} color="#4F46E5" style={{ marginRight: 12 }} />
                    <View>
                      <Text className='text-gray-800 font-medium'>Email Us</Text>
                      <Text className='text-blue-600'>{contactInfo.email}</Text>
                    </View>
                  </TouchableOpacity>

                  {/* Telegram */}
                  <TouchableOpacity
                    onPress={() => Linking.openURL(contactInfo.telegram)}
                    className='flex-row items-center p-3 bg-gray-50 rounded-lg'
                  >
                    <Icon name="paper-plane" type="font-awesome" size={20} color="#0088cc" style={{ marginRight: 12 }} />
                    <View>
                      <Text className='text-gray-800 font-medium'>Join Our Telegram Group</Text>
                      <Text className='text-blue-600'>t.me/sanatanamedia</Text>
                    </View>
                  </TouchableOpacity>

                  {/* WhatsApp */}
                  <TouchableOpacity
                    onPress={() => Linking.openURL(contactInfo.whatsapp)}
                    className='flex-row items-center p-3 bg-gray-50 rounded-lg'
                  >
                    <Icon name="whatsapp" type="font-awesome" size={20} color="#25D366" style={{ marginRight: 12 }} />
                    <View>
                      <Text className='text-gray-800 font-medium'>Join Our WhatsApp Group</Text>
                      <Text className='text-blue-600'>WhatsApp Support Group</Text>
                    </View>
                  </TouchableOpacity>

                  {/* Help Website */}
                  <TouchableOpacity
                    onPress={() => Linking.openURL(contactInfo.helpWebsite)}
                    className='flex-row items-center p-3 bg-gray-50 rounded-lg'
                  >
                    <Icon name="question-circle" type="font-awesome" size={20} color="#059669" style={{ marginRight: 12 }} />
                    <View>
                      <Text className='text-gray-800 font-medium'>Help and Tutorials Website</Text>
                      <Text className='text-blue-600'>www.sanatanamedia.com/Help</Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
          </View>

          {/* Terms of Service Section */}
          <View className='mb-24 pb-12'>
            <View
              className='flex-row justify-between items-center p-4 rounded-t-lg border-l-4 bg-blue-100 border-blue-600'
            >
              <Text className='text-xl font-bold text-blue-800'>Terms of Service</Text>
              <Icon
                name="gavel"
                type="font-awesome-5"
                size={20}
                color="#2563EB"
              />
            </View>

            <View className='bg-white p-4 pb-16 rounded-b-lg shadow-md'>
              <Text className='text-gray-700 mb-4'>
                By using Sanatana Media's services, you agree to comply with and be bound by the following terms and conditions. Please read these terms carefully before using our platform.
              </Text>

              <View className='space-y-4'>
                <View className='border-b border-gray-200 pb-4'>
                  <Text className='text-lg font-semibold text-gray-800 mb-2'>1. Acceptance of Terms</Text>
                  <Text className='text-gray-600'>
                    By accessing or using Sanatana Media's services, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our services.
                  </Text>
                </View>

                <View className='border-b border-gray-200 pb-4'>
                  <Text className='text-lg font-semibold text-gray-800 mb-2'>2. User Responsibilities</Text>
                  <Text className='text-gray-600'>
                    You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You agree to use our services only for lawful purposes and in accordance with these Terms. You must not use our services to upload, distribute, or otherwise publish any content that is illegal, harmful, threatening, abusive, harassing, defamatory, or otherwise objectionable.
                  </Text>
                </View>

                <View className='border-b border-gray-200 pb-4'>
                  <Text className='text-lg font-semibold text-gray-800 mb-2'>3. Privacy and Data Protection</Text>
                  <Text className='text-gray-600'>
                    Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your personal information. By using our services, you consent to our collection and use of your data as described in our Privacy Policy.
                  </Text>
                </View>

                <View className='border-b border-gray-200 pb-4'>
                  <Text className='text-lg font-semibold text-gray-800 mb-2'>4. Service Modifications</Text>
                  <Text className='text-gray-600'>
                    Sanatana Media reserves the right to modify, suspend, or discontinue any part of our services at any time without prior notice. We will not be liable to you or any third party for any modification, suspension, or discontinuation of our services.
                  </Text>
                </View>

                <View>
                  <Text className='text-lg font-semibold text-gray-800 mb-2'>5. Limitation of Liability</Text>
                  <Text className='text-gray-600'>
                    Sanatana Media and its affiliates shall not be liable for any indirect, incidental, special, consequential, or punitive damages arising out of or relating to your use of our services. In no event shall our total liability exceed the amount paid by you, if any, for accessing our services.
                  </Text>
                </View>
              </View>

              <Text className='text-gray-700 mt-6 italic'>
                Last updated: June 15, 2023. For the complete Terms of Service, please contact our support team.
              </Text>
            </View>
          </View>
        </ResponsiveContainer>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HelpScreen;
