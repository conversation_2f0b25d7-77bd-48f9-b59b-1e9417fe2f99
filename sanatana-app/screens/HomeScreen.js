import React, { useContext, useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  ImageBackground,
} from "react-native";
import { UserContext } from "../context/UserContext";
import { Icon } from "@rneui/themed";
import UploadWizard from "../components/UploadWizard";
import PropTypes from "prop-types";
import BetaAnnouncementCard from "../components/home/<USER>";
import ResponsiveContainer from "../components/layout/ResponsiveContainer";
import TutorialButton from "../components/home/<USER>";
import AnimatedSocialShareButton from "../components/AnimatedSocialShareButton";

const windowHeight = Dimensions.get("window").height;
const windowWidth = Dimensions.get("window").width;
const isMobile = windowWidth < 768;

export default function HomeScreen({ route }) {
  const { user } = useContext(UserContext);
  const [showWizard, setShowWizard] = useState(false);

  // Check for openWizard parameter in URL or route params
  useEffect(() => {
    // For web, check URL parameters
    if (typeof window !== "undefined") {
      const urlParams = new URLSearchParams(window.location.search);
      const shouldOpenWizard = urlParams.get("openWizard");

      if (shouldOpenWizard === "true") {
        setShowWizard(true);
      }
    }

    // For native, check route params
    if (route?.params?.openWizard) {
      setShowWizard(true);
    }
  }, [route]);

  // This effect runs when the component mounts or when route.params changes
  useEffect(() => {
    // Check if we're coming from the NewUpload tab
    if (route?.params?.openWizard) {
      console.log('Opening wizard from NewUpload tab', route?.params?.timestamp);
      // Use a small timeout to ensure the component is fully mounted
      setTimeout(() => {
        setShowWizard(true);
      }, 300); // Increased timeout for better reliability
    }
  }, [route?.params?.openWizard, route?.params?.timestamp]);

  // Log when the component mounts or updates
  useEffect(() => {
    console.log('HomeScreen rendered, showWizard:', showWizard);
  }, [showWizard]);

  return (
    <SafeAreaView style={{ height: windowHeight }} className="bg-gray-100">
      {user ? (
        <View className="flex-1">
          {!showWizard ? (
            <View className="flex-1">
              {/* Promotional Message removed as requested */}
              <ScrollView className="flex-1">
                <ResponsiveContainer>
                  <View className="p-5">
                    {/* Beta Announcement Card */}
                    <BetaAnnouncementCard
                      title="Sanatana Media Beta"
                    />

                    <ImageBackground
                      source={require('../assets/logo-bg-faded.png')}
                      resizeMode="contain"
                      style={{
                        width: '100%',
                        backgroundColor: 'white',
                        borderRadius: 16,
                        overflow: 'hidden'
                      }}
                      imageStyle={{
                        opacity: 0.05,
                        alignSelf: 'center'
                      }}
                    >
                      <View className="p-6 rounded-2xl shadow-lg items-center">
                        <Text
                          className="font-bold text-center mb-4"
                          style={{
                            fontSize: 20, // Reduced size
                            background: 'linear-gradient(90deg, #3B82F6 0%, #8B5CF6 50%, #EC4899 100%)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            backgroundClip: 'text',
                            textFillColor: 'transparent'
                          }}
                        >
                          Ready to Automate Your Social Media?
                        </Text>

                        {/* Tutorial Button */}
                        <TutorialButton />

                        {/* Start New Upload button - only on desktop */}
                        {!isMobile && (
                          <TouchableOpacity
                            onPress={() => setShowWizard(true)}
                            className="bg-blue-600 px-8 py-3 rounded-lg flex-row items-center mt-6 w-full justify-center"
                          >
                            <Icon
                              name="rocket"
                              type="font-awesome"
                              size={18}
                              color="#FFFFFF"
                              style={{ marginRight: 8 }}
                            />
                            <Text className="text-white font-bold">
                              Start New Upload
                            </Text>
                          </TouchableOpacity>
                        )}
                      </View>
                    </ImageBackground>
                  </View>
                </ResponsiveContainer>
              </ScrollView>

              {/* Animated Social Share Button - only on mobile */}
              {isMobile && (
                <View
                  style={{
                    position: 'absolute',
                    bottom: 80, // Position above tab bar
                    left: 0,
                    right: 0,
                    width: '100%', // Full screen width
                    height: 120,
                    paddingHorizontal: 20, // Add some padding on the sides
                    zIndex: 10,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.3,
                    shadowRadius: 4,
                    elevation: 5
                  }}
                >
                  <AnimatedSocialShareButton
                    onPress={() => setShowWizard(true)}
                  />
                </View>
              )}
            </View>
          ) : (
            <View className="absolute inset-0 flex items-center" style={{ paddingTop: isMobile ? 0 : 20, paddingHorizontal: 16, paddingBottom: 16 }}>
              <ResponsiveContainer>
                <View style={{ height: isMobile ? '100%' : '90vh', maxHeight: isMobile ? windowHeight - 20 : '90vh' }} className="bg-white rounded-2xl shadow-xl overflow-hidden">
                  <UploadWizard onClose={() => setShowWizard(false)} />
                </View>
              </ResponsiveContainer>
            </View>
          )}
        </View>
      ) : (
        <View className="flex-1 justify-center items-center p-5">
          <Text className="text-lg text-gray-600">
            Please log in to continue.
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
}

// Add PropTypes validation
HomeScreen.propTypes = {
  route: PropTypes.shape({
    params: PropTypes.shape({
      openWizard: PropTypes.bool,
      timestamp: PropTypes.number,
    }),
  }),
};

// Default props
HomeScreen.defaultProps = {
  route: { params: {} },
};
