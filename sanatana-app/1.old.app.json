{"expo": {"name": "sanatana-app", "slug": "sanatana-app", "plugins": ["@react-native-google-signin/google-signin"], "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true}, "android": {"googleServicesFile": "./google-services.json", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.nagubadianil.sanatanaapp"}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro", "config": {"devServer": "https://b7j82901-5003.use.devtunnels.ms"}}, "extra": {"eas": {"projectId": "2842c46c-7427-49b4-88a1-0fdce588a03a"}}}}