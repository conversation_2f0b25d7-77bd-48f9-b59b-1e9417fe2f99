import fs from 'fs';

function determineDomainsFile() {
  const cwd = process.cwd();
  if (cwd.toLowerCase().includes('socialmediaupload_dev')) {
    return 'sanatana_domains.dev.json';
  } else {
    return 'sanatana_domains.json';
  }
}
const GLOBAL_DOMAINS_FILE = determineDomainsFile();

const globalDomainsPath = `./config/${GLOBAL_DOMAINS_FILE}`; // Updated location
let globalDomains = {};

try {
  const rawData = fs.readFileSync(globalDomainsPath, 'utf8');
  globalDomains = JSON.parse(rawData);
} catch (error) {
  console.error("Failed to load global ports file:", error);
}



export default {
    "expo": {
        "name": "sanatana-app",
        "slug": "sanatana-app",
        "plugins": ["@react-native-google-signin/google-signin"],
        "version": "1.0.0",
        "orientation": "portrait",
        "icon": "./assets/icon.png",
        "userInterfaceStyle": "light",
        "newArchEnabled": true,
        "splash": {
          "image": "./assets/splash-icon.png",
          "resizeMode": "contain",
          "backgroundColor": "#ffffff"
        },
        "ios": {
          "supportsTablet": true
        },
        "android": {
          "googleServicesFile" : "./google-services.json",
          "adaptiveIcon": {
            "foregroundImage": "./assets/adaptive-icon.png",
            "backgroundColor": "#ffffff"
          },
          "package": "com.nagubadianil.sanatanaapp"
        },
        "web": {
          "favicon": "./assets/favicon.png",
          
            "bundler": "metro",
            "config": {
            "devServer": "https://www.sanatanamedia.com"
          }
        },
        "extra": {
          "eas": {
            "projectId": "2842c46c-7427-49b4-88a1-0fdce588a03a"
          },
          SANATANA_SERVICE_DOMAIN: 
          globalDomains.SANATANA_SERVICE_DOMAIN || "http://service.sanatanamedia.com",
          SANATANA_SIGNIN_DOMAIN: 
          globalDomains.SANATANA_SIGNIN_DOMAIN || "https://signin.sanatanamedia.com",
          SANATANA_APP_DOMAIN: 
          globalDomains.SANATANA_APP_DOMAIN || "https://www.sanatanamedia.com",
          SANATANA_FILES_DOMAIN:  
          globalDomains.SANATANA_FILES_DOMAIN || "https://files.sanatanamedia.com",
          SANATANA_JOBSTATUS_DOMAIN: 
          globalDomains.SANATANA_JOBSTATUS_DOMAIN || "https://jobstatus.sanatanamedia.com",
          SANATANA_ADMINDASHBOARD_DOMAIN:  
          globalDomains.SANATANA_ADMINDASHBOARD_DOMAIN || "https://admindashboard.sanatanamedia.com",
          SANATANA_YTDOWN_DOMAIN:  
          globalDomains.SANATANA_YTDOWN_DOMAIN || "https://ytdown.sanatanamedia.com/",
        }
      }
};""