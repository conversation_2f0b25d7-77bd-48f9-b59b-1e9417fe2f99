// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require("expo/metro-config");
const { withNativeWind } = require('nativewind/metro');

const config = getDefaultConfig(__dirname);

module.exports = {
  transformer: {
    assetPlugins: ['expo-asset/tools/hashAssetFiles'],
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true, // Enables Fast Refresh
      },
    }),
  },
  watchFolders: ['./'], // Ensures Metro watches project files properly
  resetCache: true, // Forces cache reset on start
  server: {
    enableHMR: true, // Ensures Hot Module Reloading picks up changes
  },
  ...withNativeWind(config, { input: './global.css' }),
  watchman: false, // **Disables Watchman**
};
