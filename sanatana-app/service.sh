#!/bin/bash
export WATCHMAN_DISABLE=true

# Get lowercase version of current directory path
current_path=$(pwd | tr '[:upper:]' '[:lower:]')

# Check if the lowercase path contains 'socialmediaupload_dev'
npm run clean

if [[ "$current_path" == *"socialmediaupload_dev"* ]]; then
    npx expo start --reset-cache --clear --web --port 6003
else
    npx expo start --reset-cache --clear --web --port 5003
fi

