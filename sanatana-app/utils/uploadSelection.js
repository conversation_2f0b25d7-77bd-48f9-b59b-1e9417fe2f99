import Constants from "expo-constants";
/**
 * Uploads the selection data to the server
 * 
 * @param {Object} options - Upload options
 * @param {Object} options.wizardData - The wizard data containing all selections
 * @param {string} options.sanatanaEmail - User's Sanatana email
 * @returns {Promise<Object>} - Response with the selection ID
 */
export const uploadSelection = async ({ wizardData, sanatanaEmail }) => {
  try {
    // Convert the wizardData to a string
    const selectionString = JSON.stringify(wizardData);
    
    // Prepare the payload
    const payload = {
      sanatana_email: sanatanaEmail || wizardData.sanatanaEmail || '<EMAIL>',
      selection: selectionString
    };
    
    // Send the request
    const response = await fetch(`${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/upload_selection`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to upload selection');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error uploading selection:', error);
    throw error;
  }
};

/**
 * Gets the status of a selection upload
 * 
 * @param {number} selectionId - The ID of the selection
 * @returns {Promise<Object>} - Selection information
 */
export const getSelectionStatus = async (selectionId) => {
  try {
    const response = await fetch(`${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/get_upload_selection/${selectionId}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get selection status');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error getting selection status:', error);
    throw error;
  }
};

/**
 * Updates the status of a selection
 * 
 * @param {number} selectionId - The ID of the selection
 * @param {string} status - The new status
 * @returns {Promise<Object>} - Response with success message
 */
export const updateSelectionStatus = async (selectionId, status) => {
  try {
    const response = await fetch(`${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/update_upload_selection_status/${selectionId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update selection status');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error updating selection status:', error);
    throw error;
  }
};

/**
 * Gets all selections for a user
 * 
 * @param {string} sanatanaEmail - User's Sanatana email
 * @param {number} limit - Maximum number of records to return (default: 100)
 * @param {number} offset - Number of records to skip (default: 0)
 * @returns {Promise<Object>} - Response with selections and total count
 */
export const getUserSelections = async (sanatanaEmail, limit = 100, offset = 0) => {
  try {
    const response = await fetch(
      `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/get_user_upload_selections?sanatana_email=${encodeURIComponent(sanatanaEmail)}&limit=${limit}&offset=${offset}`
    );
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get user selections');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error getting user selections:', error);
    throw error;
  }
};
