/**
 * Utility functions for caching API responses
 * to avoid repeated network requests for the same endpoints
 */

const API_CACHE_KEY = 'sanatana_api_cache';
const MAX_CACHE_SIZE = 50; // Store up to 50 API responses
const DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Get the API cache from localStorage
 * @returns {Object} The cache object with URLs as keys and API responses as values
 */
export const getApiCache = () => {
  if (typeof window === 'undefined') return {};
  
  try {
    const cacheString = localStorage.getItem(API_CACHE_KEY);
    if (!cacheString) return {};
    
    return JSON.parse(cacheString);
  } catch (error) {
    console.error('Error retrieving API cache:', error);
    return {};
  }
};

/**
 * Save API response to the cache
 * @param {string} url - The API URL (including query parameters)
 * @param {Object} data - The API response data
 * @param {number} duration - Cache duration in milliseconds (default: 5 minutes)
 */
export const saveToApiCache = (url, data, duration = DEFAULT_CACHE_DURATION) => {
  if (typeof window === 'undefined' || !url || !data) return;
  
  try {
    const cache = getApiCache();
    const expiresAt = Date.now() + duration;
    
    // Create a new cache object with the current entry at the front
    const newCache = {
      [url]: {
        data,
        timestamp: Date.now(),
        expiresAt
      },
      ...cache
    };
    
    // Limit cache size to MAX_CACHE_SIZE entries
    const entries = Object.entries(newCache);
    if (entries.length > MAX_CACHE_SIZE) {
      const limitedEntries = entries.slice(0, MAX_CACHE_SIZE);
      const limitedCache = Object.fromEntries(limitedEntries);
      localStorage.setItem(API_CACHE_KEY, JSON.stringify(limitedCache));
    } else {
      localStorage.setItem(API_CACHE_KEY, JSON.stringify(newCache));
    }
  } catch (error) {
    console.error('Error saving to API cache:', error);
  }
};

/**
 * Get API response from the cache if it exists and is not expired
 * @param {string} url - The API URL (including query parameters)
 * @returns {Object|null} The cached API response or null if not found or expired
 */
export const getFromApiCache = (url) => {
  if (typeof window === 'undefined' || !url) return null;
  
  try {
    const cache = getApiCache();
    const cachedEntry = cache[url];
    
    if (!cachedEntry) return null;
    
    // Check if cache is expired
    if (Date.now() > cachedEntry.expiresAt) {
      // Cache is expired, remove it
      const newCache = { ...cache };
      delete newCache[url];
      localStorage.setItem(API_CACHE_KEY, JSON.stringify(newCache));
      return null;
    }
    
    return cachedEntry.data;
  } catch (error) {
    console.error('Error retrieving from API cache:', error);
    return null;
  }
};

/**
 * Clear the entire API cache
 */
export const clearApiCache = () => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(API_CACHE_KEY);
  } catch (error) {
    console.error('Error clearing API cache:', error);
  }
};

/**
 * Clear expired entries from the API cache
 */
export const clearExpiredApiCache = () => {
  if (typeof window === 'undefined') return;
  
  try {
    const cache = getApiCache();
    const now = Date.now();
    let hasExpired = false;
    
    // Check each entry for expiration
    Object.entries(cache).forEach(([url, entry]) => {
      if (now > entry.expiresAt) {
        delete cache[url];
        hasExpired = true;
      }
    });
    
    // Only update storage if we removed something
    if (hasExpired) {
      localStorage.setItem(API_CACHE_KEY, JSON.stringify(cache));
    }
  } catch (error) {
    console.error('Error clearing expired API cache:', error);
  }
};
