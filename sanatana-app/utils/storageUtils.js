// Storage utility functions for saving and retrieving playlist selections

// Storage keys
export const YOUTUBE_SELECTIONS_KEY = "sanatana_youtube_playlist_selections";

/**
 * Save YouTube playlist selections to localStorage
 * @param {Object} selections - Object with destination IDs as keys and selection objects as values
 */
export const saveYoutubeSelections = (selections) => {
  if (typeof window !== "undefined" && selections) {
    try {
      localStorage.setItem(YOUTUBE_SELECTIONS_KEY, JSON.stringify(selections));
    } catch (error) {
      console.error("Error saving YouTube selections to localStorage:", error);
    }
  }
};

/**
 * Load YouTube playlist selections from localStorage
 * @returns {Object|null} - Object with saved selections or null if none found
 */
export const loadYoutubeSelections = () => {
  if (typeof window !== "undefined") {
    try {
      const savedSelections = localStorage.getItem(YOUTUBE_SELECTIONS_KEY);
      return savedSelections ? JSON.parse(savedSelections) : null;
    } catch (error) {
      console.error(
        "Error loading YouTube selections from localStorage:",
        error
      );
      return null;
    }
  }
  return null;
};

/**
 * Find a matching channel in the authorized channels list
 * @param {Array} authorizedChannels - List of authorized channels
 * @param {string} channelName - Channel name to find
 * @returns {Object|null} - Matching channel or null if not found
 */
export const findMatchingChannel = (authorizedChannels, channelName) => {
  if (!authorizedChannels || !channelName) return null;

  return authorizedChannels.find(
    (channel) => channel.channel_name === channelName
  );
};

/**
 * Extract playlist ID from URL
 * @param {string} url - Playlist URL
 * @returns {string|null} - Extracted playlist ID or null if not found
 */
export const extractPlaylistId = (url) => {
  if (!url) return null;

  // Extract playlist ID from URL (format: https://www.youtube.com/playlist?list=PLAYLIST_ID)
  const match = url.match(/[?&]list=([^&]+)/);
  return match ? match[1] : null;
};

/**
 * Find a matching playlist in a channel
 * @param {Object} channel - Channel object
 * @param {string} playlistId - Playlist ID to find
 * @returns {Object|null} - Matching playlist or null if not found
 */
export const findMatchingPlaylist = (channel, playlistId) => {
  if (!channel || !channel.playlists || !playlistId) return null;

  return channel.playlists.find((playlist) => {
    // Check if playlist.id matches directly
    if (playlist.id === playlistId) return true;

    // Check if playlist.url contains the playlistId
    if (playlist.url) {
      const extractedId = extractPlaylistId(playlist.url);
      return extractedId === playlistId;
    }

    return false;
  });
};

/**
 * Create default selections based on destination type
 * @param {string} destId - Destination ID
 * @param {Array} authorizedChannels - List of authorized channels
 * @returns {Object|null} - Default selection object or null if no channels available
 */
export const createDefaultSelection = (destId, authorizedChannels) => {
  if (!authorizedChannels || authorizedChannels.length === 0) {
    console.warn('No authorized channels available for default selection');
    return null;
  }

  const firstChannel = authorizedChannels[0];
  console.log('Using first channel for default selection:', firstChannel.channel_name);

  // For YouTube Shorts, default to "auto" playlist
  if (destId === "youtube_shorts") {
    return {
      type: "auto",
      channel_name: firstChannel.channel_name,
      channel_id: firstChannel.channel_id, // Include channel_id if available
    };
  }

  // For YouTube Videos, default to "none" (no playlist)
  return {
    type: "none",
    channel_name: firstChannel.channel_name,
    channel_id: firstChannel.channel_id, // Include channel_id if available
  };
};
