import axios from 'axios';
import { getFromApiCache, saveToApiCache } from './apiCache';

/**
 * Creates an axios instance with caching capabilities
 * @param {Object} options - Configuration options
 * @param {boolean} options.enableCache - Whether to enable caching (default: true)
 * @param {number} options.cacheDuration - Cache duration in milliseconds (default: 5 minutes)
 * @returns {Object} Axios instance with caching
 */
export const createCachedAxios = (options = {}) => {
  const {
    enableCache = true,
    cacheDuration = 5 * 60 * 1000, // 5 minutes
  } = options;

  // Create a new axios instance
  const instance = axios.create({
    headers: {
      'Cache-Control': 'max-age=3600', // Tell CDNs and browsers to cache for 1 hour
    },
  });

  // Add request interceptor for caching
  instance.interceptors.request.use(
    async (config) => {
      // Check if caching is enabled for this request
      // It can be disabled globally or per-request via config.enableCache
      const shouldCache =
        (enableCache !== false) &&
        (config.enableCache !== false) &&
        (config.method === 'get');

      if (shouldCache) {
        try {
          // Create a unique cache key from the URL and params
          const url = config.url;
          const params = config.params ? JSON.stringify(config.params) : '';
          const cacheKey = `${url}?${params}`;

          // Check if we have a cached response
          const cachedResponse = getFromApiCache(cacheKey);
          if (cachedResponse) {
            // Return cached response
            console.log(`Using cached response for: ${cacheKey}`);

            // Cancel the request and return cached data
            const source = axios.CancelToken.source();
            config.cancelToken = source.token;
            source.cancel('Request cancelled, using cached data');

            // Attach cached data to config for the response interceptor
            config.cachedData = cachedResponse;
          }
        } catch (cacheError) {
          // If there's an error with the cache, log it but continue with the request
          console.error('Cache error in request interceptor:', cacheError);
        }
      }

      return config;
    },
    (error) => Promise.reject(error instanceof Error ? error : new Error(String(error)))
  );

  // Add response interceptor for caching
  instance.interceptors.response.use(
    (response) => {
      try {
        // Check if caching is enabled for this response
        const shouldCache =
          (enableCache !== false) &&
          (response.config?.enableCache !== false) &&
          (response.config?.method === 'get');

        if (shouldCache && response.config) {
          // Create a unique cache key from the URL and params
          const url = response.config.url;
          const params = response.config.params ? JSON.stringify(response.config.params) : '';
          const cacheKey = `${url}?${params}`;

          // Save response to cache
          saveToApiCache(cacheKey, response.data, cacheDuration);
        }
      } catch (cacheError) {
        // If there's an error with the cache, log it but return the response anyway
        console.error('Cache error in response interceptor:', cacheError);
      }

      return response;
    },
    (error) => {
      try {
        // If the request was cancelled because we're using cached data
        if (axios.isCancel(error) && error.message === 'Request cancelled, using cached data') {
          // Make sure error.config exists before accessing its properties
          if (error.config?.cachedData) {
            // Return the cached data as a successful response
            return Promise.resolve({
              data: error.config.cachedData,
              status: 200,
              statusText: 'OK (Cached)',
              headers: {},
              config: error.config,
              cached: true,
            });
          } else {
            console.error('Cache error: config or cachedData is undefined', error);
            // Continue with the request instead of using cache
            return Promise.reject(new Error('Cache error: config or cachedData is undefined'));
          }
        }

        // For all other errors, ensure we're returning an Error object
        return Promise.reject(error instanceof Error ? error : new Error(String(error)));
      } catch (handlerError) {
        // If there's an error in our error handler, log it and return a generic error
        console.error('Error in response error interceptor:', handlerError);
        return Promise.reject(new Error('Error processing response'));
      }
    }
  );

  return instance;
};

// Create a default cached axios instance
const cachedAxios = createCachedAxios();

export default cachedAxios;
