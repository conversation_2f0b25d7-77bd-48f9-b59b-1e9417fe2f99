import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import Constants from "expo-constants";
/**
 * Uploads a file to the Sanatana Files Service with chunking support
 * 
 * @param {Object} options - Upload options
 * @param {string} options.uri - URI of the file to upload
 * @param {string} options.sanatanaEmail - User's Sanatana email
 * @param {string} options.filename - Name of the file
 * @param {number} options.chunkSize - Size of each chunk in bytes (default: 1MB)
 * @param {Function} options.onProgress - Callback for upload progress (receives percentage)
 * @param {Function} options.onSuccess - Callback for successful upload (receives upload ID and DB ID)
 * @param {Function} options.onError - Callback for upload errors (receives error message)
 * @returns {Object} - Upload controller with abort method
 */
export const uploadFile = async ({
  uri,
  sanatanaEmail,
  filename,
  chunkSize = 1024 * 1024, // 1MB chunks by default
  onProgress = () => {},
  onSuccess = () => {},
  onError = () => {},
}) => {
  // Create an AbortController to allow cancelling the upload
  const controller = new AbortController();
  let uploadId = null;
  let dbId = null;

  try {
    // Get file info
    const fileInfo = await FileSystem.getInfoAsync(uri);
    if (!fileInfo.exists) {
      throw new Error('File does not exist');
    }

    const fileSize = fileInfo.size;
    const totalChunks = Math.ceil(fileSize / chunkSize);
    
    console.log(`Starting upload of ${filename} (${fileSize} bytes) in ${totalChunks} chunks`);
    
    // Upload each chunk
    for (let chunk = 0; chunk < totalChunks; chunk++) {
      // Check if upload has been aborted
      if (controller.signal.aborted) {
        throw new Error('Upload aborted');
      }

      // Calculate chunk boundaries
      const start = chunk * chunkSize;
      const end = Math.min(start + chunkSize, fileSize);
      
      // Read the chunk
      let chunkData;
      if (Platform.OS === 'web') {
        // For web, we need to use a different approach
        const response = await fetch(uri);
        const blob = await response.blob();
        const slicedBlob = blob.slice(start, end);
        chunkData = slicedBlob;
      } else {
        // For native platforms, use FileSystem
        chunkData = await FileSystem.readAsStringAsync(uri, {
          encoding: FileSystem.EncodingType.Base64,
          position: start,
          length: end - start,
        });
      }

      // Create form data for the chunk
      const formData = new FormData();
      formData.append('sanatana_email', sanatanaEmail);
      formData.append('chunk', chunk.toString());
      formData.append('chunks', totalChunks.toString());
      formData.append('filename', filename);
      
      // Add upload_id if we have one (for chunks after the first)
      if (uploadId) {
        formData.append('upload_id', uploadId);
      }

      // Append the file chunk
      if (Platform.OS === 'web') {
        formData.append('file', chunkData, filename);
      } else {
        // Convert base64 to blob for native platforms
        const fileBlob = {
          uri: `data:application/octet-stream;base64,${chunkData}`,
          name: filename,
          type: 'application/octet-stream',
        };
        formData.append('file', fileBlob);
      }

      // Upload the chunk
      const response = await fetch(`${Constants.expoConfig.extra.SANATANA_FILES_DOMAIN}/upload_file`, {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const data = await response.json();
      
      // Store the upload_id from the first chunk response
      if (chunk === 0) {
        uploadId = data.upload_id;
        dbId = data.db_id;
      }

      // Calculate and report progress
      const progress = ((chunk + 1) / totalChunks) * 100;
      onProgress(progress);
    }

    // Start polling for upload progress until complete
    await pollUploadProgress(uploadId, onProgress);

    // Upload complete
    onSuccess({ uploadId, dbId });
    return { uploadId, dbId };

  } catch (error) {
    if (error.message !== 'Upload aborted') {
      onError(error.message);
    }
    throw error;
  }

  // Return controller to allow aborting the upload
  return {
    abort: () => controller.abort(),
    uploadId,
    dbId,
  };
};

/**
 * Polls the upload progress until complete
 * 
 * @param {string} uploadId - ID of the upload to check
 * @param {Function} onProgress - Callback for progress updates
 * @returns {Promise<void>}
 */
const pollUploadProgress = async (uploadId, onProgress) => {
  let complete = false;
  let attempts = 0;
  const maxAttempts = 60; // 5 minutes (5s interval)
  
  while (!complete && attempts < maxAttempts) {
    try {
      const response = await fetch(`${Constants.expoConfig.extra.SANATANA_FILES_DOMAIN}/upload_progress/${uploadId}`);
      
      if (!response.ok) {
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
        continue;
      }
      
      const data = await response.json();
      
      // Update progress
      onProgress(data.progress);
      
      // Check if complete
      if (data.status === 'Complete') {
        complete = true;
      } else if (data.status === 'Failed') {
        throw new Error(data.error || 'Upload failed');
      } else {
        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
        attempts++;
      }
    } catch (error) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
    }
  }
  
  if (!complete) {
    throw new Error('Upload timed out');
  }
};

/**
 * Retries a failed upload
 * 
 * @param {string} uploadId - ID of the upload to retry
 * @returns {Promise<boolean>} - Whether the retry was successful
 */
export const retryUpload = async (uploadId) => {
  try {
    const response = await fetch(`${Constants.expoConfig.extra.SANATANA_FILES_DOMAIN}/retry_upload/${uploadId}`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Retry failed');
    }
    
    return true;
  } catch (error) {
    console.error('Error retrying upload:', error);
    return false;
  }
};

/**
 * Gets the current progress of an upload
 * 
 * @param {string} uploadId - ID of the upload to check
 * @returns {Promise<Object>} - Upload progress information
 */
export const getUploadProgress = async (uploadId) => {
  try {
    const response = await fetch(`${Constants.expoConfig.extra.SANATANA_FILES_DOMAIN}/upload_progress/${uploadId}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get upload progress');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error getting upload progress:', error);
    throw error;
  }
};
