import Constants from 'expo-constants';
import { Platform, Dimensions } from 'react-native';

// Determine if the device is mobile
const windowWidth = Dimensions.get('window').width;
const isMobile = Platform.OS === 'android' || Platform.OS === 'ios' || windowWidth < 768;

/**
 * Extract YouTube video ID from various URL formats
 * @param {string} url - The YouTube URL
 * @returns {string|null} The video ID or null if not found
 */
const extractYouTubeVideoId = (url) => {
  if (!url) return null;

  // Handle different YouTube URL formats
  let videoId = null;

  // Regular YouTube URL: https://www.youtube.com/watch?v=VIDEO_ID
  // or with additional parameters: https://www.youtube.com/watch?v=VIDEO_ID&feature=youtu.be
  const regularMatch = url.match(/(?:youtube\.com\/watch\?v=)([\w-]+)/);
  if (regularMatch) {
    videoId = regularMatch[1];
  }

  // YouTube Shorts: https://www.youtube.com/shorts/VIDEO_ID
  const shortsMatch = url.match(/(?:youtube\.com\/shorts\/)([\w-]+)/);
  if (!videoId && shortsMatch) {
    videoId = shortsMatch[1];
  }

  // Youtu.be short URL: https://youtu.be/VIDEO_ID
  const shortUrlMatch = url.match(/(?:youtu\.be\/)([\w-]+)/);
  if (!videoId && shortUrlMatch) {
    videoId = shortUrlMatch[1];
  }

  // YouTube embed URL: https://www.youtube.com/embed/VIDEO_ID
  const embedMatch = url.match(/(?:youtube\.com\/embed\/)([\w-]+)/);
  if (!videoId && embedMatch) {
    videoId = embedMatch[1];
  }

  // YouTube playlist URL with video: https://www.youtube.com/watch?v=VIDEO_ID&list=PLAYLIST_ID
  // or https://www.youtube.com/playlist?list=PLAYLIST_ID
  const playlistMatch = url.match(/(?:youtube\.com\/watch\?v=)([\w-]+)(?:.*list=)([\w-]+)/);
  if (!videoId && playlistMatch) {
    videoId = playlistMatch[1]; // Use the video ID, not the playlist ID
  }

  return videoId;
};

/**
 * Convert any YouTube URL to an embeddable URL
 * @param {string} url - The YouTube URL
 * @returns {string} The embeddable YouTube URL
 */
const convertToEmbeddableYouTubeUrl = (url) => {
  if (!url) return '';

  // If it's already an embed URL, return it
  if (url.includes('youtube.com/embed/')) {
    return url;
  }

  // Extract the video ID
  const videoId = extractYouTubeVideoId(url);
  if (!videoId) {
    console.warn(`Could not extract video ID from URL: ${url}`);
    return url; // Return original URL if we can't extract the video ID
  }

  // Check if it's a playlist URL
  const playlistMatch = url.match(/(?:list=)([\w-]+)/);
  if (playlistMatch) {
    const playlistId = playlistMatch[1];
    return `https://www.youtube.com/embed/${videoId}?list=${playlistId}`;
  }

  // Return standard embed URL
  return `https://www.youtube.com/embed/${videoId}`;
};

/**
 * Get the YouTube download service URL
 * @returns {string} The service URL
 */
const getYouTubeDownloadServiceUrl = () => {
  return Constants.expoConfig?.extra?.SANATANA_YTDOWN_DOMAIN;
};

/**
 * Fetch tutorials from the server
 * @returns {Promise<Array>} Array of tutorial objects
 */
export const fetchTutorials = async () => {
  try {
    // Always fetch fresh data from the server
    console.log('Fetching fresh tutorials data from server');

    // Get service URL
    const serviceUrl = getYouTubeDownloadServiceUrl();
    if (!serviceUrl) {
      console.error('YouTube download service URL is undefined');
      throw new Error('Service configuration missing');
    }

    // Fetch tutorials from server
    console.log(`Fetching tutorials from ${serviceUrl}/get_tutorials`);
    const response = await fetch(`${serviceUrl}/get_tutorials`);

    if (!response.ok) {
      throw new Error(`Failed to fetch tutorials: ${response.status} ${response.statusText}`);
    }

    const tutorials = await response.json();
    console.log('Successfully fetched fresh tutorials data');
    return tutorials;
  } catch (error) {
    console.error('Error fetching tutorials:', error);
    // Return empty array on error
    return [];
  }
};

/**
 * Get a tutorial URL by name and target platform
 * @param {string} name - The tutorial name
 * @param {string} fallbackUrl - Fallback URL if tutorial not found
 * @param {number} [timestamp] - Optional timestamp to force fresh data
 * @returns {Promise<string>} The tutorial URL
 */
export const getTutorialUrl = async (name, fallbackUrl, timestamp) => {
  try {
    console.log(`Getting tutorial URL for ${name}${timestamp ? ` with timestamp ${timestamp}` : ''}`);
    // Always fetch fresh tutorials
    const tutorials = await fetchTutorials();
    const target = isMobile ? 'mobile' : 'desktop';

    // Find the tutorial that matches the name and target
    const tutorial = tutorials.find(t =>
      t.name.toLowerCase().includes(name.toLowerCase()) &&
      t.target === target
    );

    // Get the URL from the tutorial or use the fallback
    const youtubeUrl = tutorial?.url || fallbackUrl;
    console.log(`Found tutorial URL for ${name}: ${youtubeUrl}`);

    // Convert to embeddable URL and add timestamp to prevent caching
    const embeddableUrl = convertToEmbeddableYouTubeUrl(youtubeUrl);

    // Add a cache-busting parameter if a timestamp was provided
    if (timestamp) {
      // Check if the URL already has parameters
      const hasParams = embeddableUrl.includes('?');
      // Add the timestamp as a parameter
      return `${embeddableUrl}${hasParams ? '&' : '?'}t=${timestamp}`;
    }

    return embeddableUrl;
  } catch (error) {
    console.error(`Error getting tutorial URL for ${name}:`, error);
    return convertToEmbeddableYouTubeUrl(fallbackUrl);
  }
};

/**
 * Get all tutorials for the current platform (mobile or desktop)
 * @param {number} [timestamp] - Optional timestamp to force fresh data
 * @returns {Promise<Array>} Array of tutorial objects for the current platform
 */
export const getPlatformTutorials = async (timestamp) => {
  try {
    console.log(`Getting platform tutorials${timestamp ? ` with timestamp ${timestamp}` : ''}`);
    // Always fetch fresh tutorials
    const tutorials = await fetchTutorials();
    const target = isMobile ? 'mobile' : 'desktop';

    // Filter tutorials for the current platform
    const platformTutorials = tutorials.filter(t => t.target === target);

    // Convert all URLs to embeddable format and add timestamp if provided
    return platformTutorials.map(tutorial => {
      const embeddableUrl = convertToEmbeddableYouTubeUrl(tutorial.url);

      // Add a cache-busting parameter if a timestamp was provided
      if (timestamp) {
        // Check if the URL already has parameters
        const hasParams = embeddableUrl.includes('?');
        // Add the timestamp as a parameter
        return {
          ...tutorial,
          url: `${embeddableUrl}${hasParams ? '&' : '?'}t=${timestamp}`
        };
      }

      return {
        ...tutorial,
        url: embeddableUrl
      };
    });
  } catch (error) {
    console.error('Error getting platform tutorials:', error);
    return [];
  }
};

/**
 * Export the URL conversion function for direct use
 */
export const convertYouTubeUrl = convertToEmbeddableYouTubeUrl;
