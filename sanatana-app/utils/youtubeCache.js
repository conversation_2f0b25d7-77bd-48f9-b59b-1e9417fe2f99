/**
 * Utility functions for caching YouTube video information
 * to avoid repeated API calls for the same URLs
 */

const CACHE_KEY = 'sanatana_youtube_info_cache';
const MAX_CACHE_SIZE = 10;

/**
 * Get the YouTube video info cache from localStorage
 * @returns {Object} The cache object with URLs as keys and video info as values
 */
export const getYoutubeCache = () => {
  if (typeof window === 'undefined') return {};
  
  try {
    const cacheString = localStorage.getItem(CACHE_KEY);
    if (!cacheString) return {};
    
    return JSON.parse(cacheString);
  } catch (error) {
    console.error('Error retrieving YouTube cache:', error);
    return {};
  }
};

/**
 * Save video info to the cache
 * @param {string} url - The YouTube video URL
 * @param {Object} videoInfo - The video information object
 */
export const saveToYoutubeCache = (url, videoInfo) => {
  if (typeof window === 'undefined' || !url || !videoInfo) return;
  
  try {
    const cache = getYoutubeCache();
    
    // Create a new cache object with the current entry at the front
    const newCache = {
      [url]: {
        data: videoInfo,
        timestamp: Date.now()
      },
      ...cache
    };
    
    // Limit cache size to MAX_CACHE_SIZE entries
    const entries = Object.entries(newCache);
    if (entries.length > MAX_CACHE_SIZE) {
      const limitedEntries = entries.slice(0, MAX_CACHE_SIZE);
      const limitedCache = Object.fromEntries(limitedEntries);
      localStorage.setItem(CACHE_KEY, JSON.stringify(limitedCache));
    } else {
      localStorage.setItem(CACHE_KEY, JSON.stringify(newCache));
    }
  } catch (error) {
    console.error('Error saving to YouTube cache:', error);
  }
};

/**
 * Get video info from the cache if it exists
 * @param {string} url - The YouTube video URL
 * @returns {Object|null} The cached video info or null if not found
 */
export const getFromYoutubeCache = (url) => {
  if (typeof window === 'undefined' || !url) return null;
  
  try {
    const cache = getYoutubeCache();
    const cachedEntry = cache[url];
    
    if (!cachedEntry) return null;
    
    // Optional: Check if cache is too old (e.g., older than 24 hours)
    // const MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    // if (Date.now() - cachedEntry.timestamp > MAX_AGE) {
    //   return null;
    // }
    
    return cachedEntry.data;
  } catch (error) {
    console.error('Error retrieving from YouTube cache:', error);
    return null;
  }
};

/**
 * Clear the entire YouTube video info cache
 */
export const clearYoutubeCache = () => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(CACHE_KEY);
  } catch (error) {
    console.error('Error clearing YouTube cache:', error);
  }
};
