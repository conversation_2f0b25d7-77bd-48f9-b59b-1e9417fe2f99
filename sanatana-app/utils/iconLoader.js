import * as Font from 'expo-font';
import { Ionicons } from '@expo/vector-icons';

/**
 * List of all Ionicons used in the app
 * This ensures all icons are loaded at once
 */
const USED_IONICONS = [
  'home',
  'settings',
  'help-circle',
  'person',
  'arrow-back',
  'arrow-forward',
  'checkmark',
  'close',
  'add',
  'remove',
  'refresh',
  'search',
  'menu',
  'information-circle',
  'alert-circle',
  'warning',
  'cloud-upload',
  'cloud-download',
  'play',
  'pause',
  'trash',
  'create',
  'share',
  'link',
  'calendar',
  'time',
  'list',
  'grid',
  'options',
  'more',
  'ellipsis-vertical',
  'ellipsis-horizontal',
  'chevron-up',
  'chevron-down',
  'chevron-forward',
  'chevron-back',
  'log-out',
  'log-in',
  'star',
  'heart',
  'bookmark',
  'flag',
  'mail',
  'call',
  'chatbubble',
  'camera',
  'image',
  'videocam',
  'mic',
  'musical-notes',
  'document',
  'folder',
  'cloud',
  'wifi',
  'bluetooth',
  'airplane',
  'location',
  'map',
  'navigate',
  'compass',
  'pin',
  'globe',
  'earth',
  'language',
  'flash',
  'flashlight',
  'battery-full',
  'battery-half',
  'battery-dead',
  'sunny',
  'moon',
  'rainy',
  'snow',
  'thermometer',
  'water',
  'flame',
  'leaf',
  'flower',
  'paw',
  'nutrition',
  'restaurant',
  'cafe',
  'beer',
  'wine',
  'pizza',
  'ice-cream',
  'fitness',
  'bicycle',
  'walk',
  'bus',
  'car',
  'train',
  'airplane-outline',
  'boat',
  'rocket',
  'desktop',
  'laptop',
  'tablet-portrait',
  'phone-portrait',
  'tv',
  'game-controller',
  'headset',
  'radio',
  'hardware-chip',
  'code',
  'terminal',
  'logo-github',
  'logo-twitter',
  'logo-facebook',
  'logo-instagram',
  'logo-youtube',
  'logo-linkedin',
  'logo-google',
  'logo-apple',
  'logo-android',
  'logo-windows',
  'logo-tux',
  'logo-html5',
  'logo-css3',
  'logo-javascript',
  'logo-python',
  'logo-nodejs',
  'logo-npm',
  'logo-react',
  'logo-angular',
  'logo-vue',
  'logo-docker',
  'logo-aws',
  'logo-firebase',
  'logo-wordpress',
  'logo-slack',
  'logo-discord',
  'logo-twitch',
  'logo-steam',
  'logo-playstation',
  'logo-xbox',
  'logo-nintendo-switch',
  'logo-bitcoin',
  'logo-ethereum',
  'logo-usd',
  'logo-euro',
  'logo-yen',
  'logo-pound',
];

/**
 * Preloads all Ionicons used in the app
 * @returns {Promise} A promise that resolves when all icons are loaded
 */
export const preloadIcons = async () => {
  console.log('Preloading all Ionicons...');
  
  try {
    // Load the Ionicons font
    await Font.loadAsync({
      ...Ionicons.font,
    });
    
    // Create a hidden div to preload all icons
    if (typeof document !== 'undefined') {
      const preloadDiv = document.createElement('div');
      preloadDiv.style.position = 'absolute';
      preloadDiv.style.width = '0';
      preloadDiv.style.height = '0';
      preloadDiv.style.overflow = 'hidden';
      preloadDiv.style.opacity = '0';
      
      // Add all icons to the preload div
      USED_IONICONS.forEach(iconName => {
        const iconElement = document.createElement('span');
        iconElement.className = `ionicons ion-${iconName}`;
        preloadDiv.appendChild(iconElement);
      });
      
      // Add the preload div to the body
      document.body.appendChild(preloadDiv);
      
      // Remove the preload div after a short delay
      setTimeout(() => {
        document.body.removeChild(preloadDiv);
      }, 5000);
    }
    
    console.log('All Ionicons preloaded successfully');
    return true;
  } catch (error) {
    console.error('Error preloading Ionicons:', error);
    throw error;
  }
};

export default preloadIcons;
