import React, { useRef, useState, useEffect, useContext } from 'react';
import { View, Button, TouchableOpacity, Text, Dimensions,
   Modal, ActivityIndicator,  Alert, PanResponder, } from 'react-native';
import { FontAwesome } from "@expo/vector-icons";
import PropTypes from "prop-types";
import axios from 'axios';
import { UserContext } from "../context/UserContext";
import ScrollControlsOverlay from './ScrollControlsOverlay';
import { useNavigation } from "@react-navigation/native";

const print = console.log
const MagicApp = ({ platform, account, onClose }) => {
  const { user } = useContext(UserContext);
  const hiddenInputRef = useRef(null);
  const iframeRef = useRef(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [ws, setWs] = useState(null);
  const [display_id, set_display_id] = useState(-1);
  const [iframeUrl, setIframeUrl] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { width, height } = Dimensions.get('window');
  const isMobile = width < 768;

  // WebSocket URL - this would be configured based on your actual service
  const ws_url = 'wss://magic.mediaverse.site/web'; // Replace with your actual WebSocket URL
  const lastTouch = useRef({ x: 0, y: 0 });
  const containerRef = useRef();


  // Wheel event: for scroll detection (React Native Web)
  useEffect(() => {
    const el = containerRef.current;
    if (!el) return;
    const handleTouchStart = (e) => {
      try {
        print("handleTouchStart: BEG")
        if (e.touches.length > 0) {
          lastTouch.current = {
            x: e.touches[0].clientX,
            y: e.touches[0].clientY,
          };
        }
      }
      catch (ex) {
        print("handleTouchStart: EXCEPTION ex:", ex)
      }
    };

    const handleTouchMove = (e) => {
      try {
        print("handleTouchMove: BEG")
        if (e.touches.length > 0) {
          const dx = e.touches[0].clientX - lastTouch.current.x;
          const dy = e.touches[0].clientY - lastTouch.current.y;
          lastTouch.current = {
            x: e.touches[0].clientX,
            y: e.touches[0].clientY,
          };
          //sendToSocket({ type: 'drag', dx, dy });
          send_mouse_event = ('drag', dx, dy, 0, 0)
        }
      }
      catch (ex) {
        print("handleTouchMove: EXCEPTION ex:", ex)
      }
    }

    const handleWheel = (e) => {
      try {
        print("handleWheel: BEG")
        // e.preventDefault(); // Optional: prevent actual scroll

        send_scroll_command(e.deltaX, e.deltaY);
      }
      catch (ex) {
        print("handleWheel: EXCEPTION ex:", ex)
      }

    };

    el.addEventListener('touchstart', handleTouchStart, { passive: true });
    el.addEventListener('touchmove', handleTouchMove, { passive: true });
    el.addEventListener('wheel', handleWheel, { passive: true });

    return () => {
      el.removeEventListener('touchstart', handleTouchStart);
      el.removeEventListener('touchmove', handleTouchMove);
      el.removeEventListener('wheel', handleWheel);
    };


  }, [ws]);

  const send_mouse_event = (mouse_action, dx, dy, absX, absY) => {
    console.log(`send_mouse_event: mouse_action: ${mouse_action}, dx: ${dx}, dy: ${dy},absX: ${absX}, absY: ${absY}`)

    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(
        JSON.stringify({
          type: 'mouse_command',
          display_id,
          mouse_action, dx, dy, absX, absY
        })
      );
    }
  };

  const send_scroll_command = async (deltaX, deltaY) => {
    console.log(`Scroll handler: scroll deltaX: ${deltaX}, deltaY: ${deltaY}`)
    if (ws && ws.readyState === WebSocket.OPEN) {
     
      ws.send(
        JSON.stringify({
          display_id,
          type: 'scroll_command',
          deltaX, deltaY,
        })
      );
    }
  }
///////////////////////////////////////////////////////////////////////////////////



  const connectToMagic_Websocket_Svc = async (display_id) => {

    try {
      // Create WebSocket connection
      const socket = new WebSocket(ws_url);

      socket.onopen = () => {
        console.log('connectToMagic_Websocket_Svc: WebSocket connected');
        // Send initial connection info
        socket.send(JSON.stringify({
          type: 'register_web',
          display_id: display_id,
        }));
      };

      socket.onmessage = (event) => {
        console.log('connectToMagic_Websocket_Svc: Rushi: Magic Server Message :', event.data);
        const data = JSON.parse(event.data);
        if (data.type === "on_focus") {
          focusHiddenInput(data);
        }
        else if (data.type == "disconnect") {
          exit_magicapp(data)
        }
        else if (data.type == "auth_session_expired") {

          socket.send(JSON.stringify({
            type: 'unregister_web',
            display_id: display_id,
          }));

          close_connection()
          exit_magicapp(data)
        }
      };

      socket.onclose = () => {
        console.log('connectToMagic_Websocket_Svc: WebSocket closed');
      };

      socket.onerror = (error) => {
        console.error('connectToMagic_Websocket_Svc: WebSocket error:', error);
      };

      setWs(socket);
      set_display_id(display_id)
      console.log(`connectToMagic_Websocket_Svc: Connected: display_id: ${display_id}; ws_url ${ws_url}`);

    } catch (error) {
      console.error('connectToMagic_Websocket_Svc: EXCCEPTION setting up WebSocket:', error);
    }

  }

  const exit_magicapp = async (data) => {
    console.log("Session expired. Cleaning up and redirecting to SetupMedia.");

    // 1. Clean up resources
    if (ws) {
      try {
        ws.close();
      } catch (e) {
        console.error("Error closing websocket:", e);
      }
    }
    setWs(null);
    set_display_id(-1);
    setIframeUrl('');
    setInputValue('');
    setKeyboardVisible(false);
  
    // 2. Close MagicApp modal
    if (onClose) {
      onClose();
    }

     // 3. Redirect to SetupMedia
    const navigation = useNavigation();
    navigation.navigate('SetupMedia');

  }


  // Fetch connection info from make_link endpoint
  const getConnectionInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('MagicApp: Fetching connection info from service.mediaverse.site/make_link');

      // Make request to get connection info
      const response = await axios.get('https://service.mediaverse.site/make_link', {
        params: {
          username: user?.email || 'guest', // Use user email from context or fallback to guest
          geometry: '485x850',
          timestamp: Date.now() // Add timestamp to prevent caching issues
        }
      });

      const data = response.data;
      console.log('MagicApp: Received connection info:', JSON.stringify(data));

      if (data.status === 'SUCCESS') {
        console.log('MagicApp: Connection info retrieved successfully');
        console.log('MagicApp: Display link:', data.display_link);
        console.log('MagicApp: Display ID:', data.display_id);
        
        // Set the iframe URL to the display_link from the response
        
        await connectToMagic_Websocket_Svc(data.display_id)
       
        setIframeUrl(data.display_link);
        
       
      } else {
        console.error('MagicApp: Failed to get connection info. Status:', data.status);
        setError('Failed to get connection info');
      }
    } catch (error) {
      console.error('MagicApp: Error fetching connection info:', error);
      console.error('MagicApp: Error details:', error.response ? error.response.data : 'No response data');
      setError('Failed to connect to server: ' + (error.message || 'Unknown error'));
    } finally {

        setLoading(false);

    }
  };


  const close_connection = async () => {
    try {
    
      console.log('MagicApp: Fetching connection info from service.mediaverse.site/close_connection');

      // Make request to get connection info
      const response = await axios.get('https://service.mediaverse.site/close_link', {
        params: {
          username: user?.email || 'guest', // Use user email from context or fallback to guest
        }
      }); 

    } catch (error) {
      console.error('MagicApp: close_connection: Error fetching connection info:', error);
      console.error('MagicApp: close_connection: Error details:', error.response ? error.response.data : 'No response data');
      setError('Failed to connect to server: ' + (error.message || 'Unknown error'));
    } finally {


    }
  };


  // Call getConnectionInfo when the component mounts
  useEffect(() => {
    // Get connection info
    getConnectionInfo();
  }, []);  // Empty dependency array means this runs once on mount


  // Handle input changes and forward keystrokes to iframe
  const handleInputChange = (e) => {
    if (!isMobile) return

    const value = e.target.value;
    // setInputValue(value);

    // Get the last character typed (the new character)
    const lastChar = value.slice(-1);

    //Forward the keystroke to the iframe using postMessage
    // const iframe = iframeRef.current?.querySelector('iframe');
    // if (iframe && iframe.contentWindow) {
    try {

      // Also send via WebSocket if connected
      if (ws && ws.readyState === WebSocket.OPEN) {
        console.log(`handleInputChange: on_keystrokes to display_id ${display_id}; char ${lastChar}`)
        ws.send(JSON.stringify({
          display_id: display_id,
          type: 'on_keystrokes',
          key: lastChar,
          keyCode: lastChar.charCodeAt(0)
        }));
      }

      console.log('Sent keystroke to iframe:', lastChar);
    } catch (error) {
      console.error('Error sending message to iframe:', error);
    }
    // }
  };

  // Handle special keys (backspace, enter, etc.)
  const handleKeyDown = (e) => {
    const key = e.key; // e.g., 'Enter', 'Backspace', 'a', etc.
    const keyCode = e.keyCode; // e.g., 13 for Enter
    console.log(`handleKeyDown: on_keystrokes to display_id ${display_id}; key ${key}`);

    // const iframe = iframeRef.current?.querySelector('iframe');
    // if (iframe && iframe.contentWindow) {
    try {
      if (ws && ws.readyState === WebSocket.OPEN) {

        if (keyCode != 229) {
          ws.send(JSON.stringify({
            display_id: display_id,
            type: 'on_keystrokes',
            key: key,
            keyCode: keyCode
          }));
        }
      }

      console.log('handleKeyDown: Sent key to iframe:', key);
    } catch (error) {
      console.error('handleKeyDown: Error sending key to iframe:', error);
    }

    //}
  };

  const focusHiddenInput = (rectData) => {
    if (hiddenInputRef.current) {
      // Toggle keyboard visibility
      const newKeyboardState = true
      setKeyboardVisible(newKeyboardState);
      const el = hiddenInputRef.current;
      if (el) {
        el.style.position = 'absolute';
        el.style.left = `${rectData.x}px`;
        el.style.top = `${rectData.y}px`;
        el.style.width = `${rectData.width}px`;
        el.style.height = `${rectData.height}px`;
        el.style.border = '1px solid yellow';
        el.style.zIndex = '999';
        //el.style.opacity = '0.01'; // Keep keyboard behavior intact
        //el.style.pointerEvents = 'auto'; // Allow input
      }

      // Focus the input to show keyboard
      hiddenInputRef.current.blur();
      hiddenInputRef.current.focus();

      // Scroll the iframe if needed
      if (iframeRef.current) {
        iframeRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    } else {
      // Blur the input to hide keyboard
      hiddenInputRef.current.blur();
    }

  };



  return (
    <Modal
      animationType="slide"
      transparent={false}
      visible={true}
      onRequestClose={onClose}
    >
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }} 
      
     
      >
        <>
        {/* Show loading indicator while fetching URL */}
        {loading && (
          <View style={{ flex: 1, justifyContent: 'center',
             alignItems: 'center',
               top: 0, left: 0, top: 0, bottom: 0,
               width: '100%',
              height: '100%',
               }}>
            <ActivityIndicator size="large" color="#0000ff" />
            <Text style={{ marginTop: 0 }}>Loading social media authorization...</Text>
          </View>
        )}

        {/* Show error message if there was an error */}
        {error && (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
            <Text style={{ color: 'red', textAlign: 'center', marginBottom: 20 }}>{error}</Text>
            <TouchableOpacity
              onPress={getConnectionInfo}
              style={{
                backgroundColor: '#0000ff',
                padding: 10,
                borderRadius: 5,
              }}
            >
              <Text style={{ color: 'white' }}>Try Again</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Input to capture keystrokes */}
        {/*isMobile &&*/ !loading && !error && (
          <input
            ref={hiddenInputRef}
            value={inputValue}
            //onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            keyboardType="default"
            autoCapitalize="none"  // or "sentences", "words", "characters"
            autoCorrect={false}
            style={{
              opacity: 0, // Completely invisible
              position: 'absolute',
              top: 0,
              left: 0,
              width: '0%',
              height: '0%',
              zIndex: -1, // Place below the iframe to allow tapping on the app
              pointerEvents: 'auto' 
            }}
            autoComplete="off"
          />

          )}
          {!loading && !error && (
          <View
            ref={containerRef}
            style={{
              flex: 1,
              width: '100%',
              height: '100%',
              backgroundColor: 'black',
              overflow: 'hidden',
              position: 'relative',
            }}
          >
            {!loading && !error && iframeUrl && (
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  pointerEvents: 'auto', // Let iframe get input
                }}
              >
                <iframe
                  src={iframeUrl}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                  }}
                  title="Embedded App"
                />
              </div>
            )}
          </View>
          )
        }
        { isMobile && !loading && !error && (
            <ScrollControlsOverlay send_scroll_command={send_scroll_command} />
        )}
      
        </>
      </View>
    </Modal>
  );
};

MagicApp.propTypes = {
  platform: PropTypes.string.isRequired,
  account: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default MagicApp;
