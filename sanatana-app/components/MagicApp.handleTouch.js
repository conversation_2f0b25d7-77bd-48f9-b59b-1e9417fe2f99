import React, { useRef, useState, useEffect, useContext } from 'react';
import { View, Button, TouchableOpacity, Text, Dimensions,
   Modal, ActivityIndicator,  Alert , } from 'react-native';
import { FontAwesome } from "@expo/vector-icons";
import PropTypes from "prop-types";
import axios from 'axios';
import { UserContext } from "../context/UserContext";

const MagicApp = ({ platform, account, onClose }) => {
  const { user } = useContext(UserContext);
  const hiddenInputRef = useRef(null);
  const iframeRef = useRef(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [ws, setWs] = useState(null);
  const [display_id, set_display_id] = useState(-1);
  const [iframeUrl, setIframeUrl] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { width, height } = Dimensions.get('window');
  const isMobile = width < 768;

  // WebSocket URL - this would be configured based on your actual service
  const ws_url = 'wss://magic.mediaverse.site/web'; // Replace with your actual WebSocket URL
  const startPos = useRef(null);
  const startPosTouch = useRef(null);
  const gestureRef = useRef(null);

  const handleMouseDown = (e) => {
    try {
      e.preventDefault();
      startPos.current = { x: e.clientX, y: e.clientY };
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    catch(ex){
      print("handleMouseDown: EXCEPTION e:", ex)
    }

  };

  const handleMouseMove = (e) => {
    try {
        if (!startPos.current) return;
        const dx = e.clientX - startPos.current.x;
        const dy = e.clientY - startPos.current.y;
        // socket.current.send(JSON.stringify({
        //   type: 'mouse-drag',
        //   dx,
        //   dy,
        //   absX: e.clientX,
        //   absY: e.clientY,
        // }));

        send_mouse_event('mouse-drag', dx, dy, e.clientX, e.clientY)
    }
    catch(ex){
      print("handleMouseMove: EXCEPTION e:", ex)
    }

  };
  const handleMouseUp = () => {
    try {
      startPos.current = null;
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', handleMouseUp);
        //socket.current.send(JSON.stringify({ type: 'drag-end' }));
        send_mouse_event('drag-end',0,0,0,0)
    }
    catch(ex){
      print("handleMouseUp: EXCEPTION e:", ex)
    }
  
  };

  const handleWheel = (e) => {
    try {
      // socket.current.send(JSON.stringify({
        //   type: 'scroll',
        //   deltaX: e.deltaX,
        //   deltaY: e.deltaY,
        // }));
        
      send_scroll_command(e.deltaX, e.deltaY)
    }
    catch(ex){
      print("handleWheel: EXCEPTION e:", ex)
    }
   
  };

  const handleTouchStart2 = (e) => {
    try {
      const touch = e.touches[0];
        startPosTouch.current = { x: touch.clientX, y: touch.clientY };
    }
    catch(ex){
      print("handleTouchStart: EXCEPTION e:", ex)
    }
  
  };
  
  const handleTouchMove2 = (e) => {
    try {
      if (!startPosTouch.current) return;
      //e.preventDefault(); 
        const touch = e.touches[0];
        const dx = touch.clientX - startPos.current.x;
        const dy = touch.clientY - startPos.current.y;
      
        // socket.current.send(JSON.stringify({
        //   type: 'touch-drag',
        //   dx, dy,
        //   absX: touch.clientX,
        //   absY: touch.clientY,
        // }));
        send_mouse_event('touch-drag', dx, dy, touch.clientX,touch.clientY)
    }
    catch(ex){
      print("handleTouchMove: EXCEPTION e:", ex)
    }
  
  };
  
  const handleTouchEnd2 = () => {
    try {
    // socket.current.send(JSON.stringify({ type: 'touch-end' }));
      send_mouse_event('touch-end',0,0,0,0)
      startPosTouch.current = null;
    }
    catch(ex){
      print("handleTouchEnd: EXCEPTION e:", ex)
    }
  
  };
  useEffect(() => {
    const el = gestureRef.current;
    if (!el) {

      console.log("handleTouch UseEffect: gestureRef.current is null. Returning...")
      return;
    }
    console.log("handleTouch UseEffect: gestureRef.current GOOD. Let setup the touches")
    const handleTouchStart = (e) => {
      try {
        e.preventDefault(); 
        const touch = e.touches[0];
          startPosTouch.current = { x: touch.clientX, y: touch.clientY };
      }
      catch(ex){
        print("handleTouchStart: EXCEPTION e:", ex)
      }
    
    };
    const handleTouchMove = (e) => {
      try {
        e.preventDefault(); 
        if (!startPosTouch.current) return;
      
          const touch = e.touches[0];
          const dx = touch.clientX - startPos.current.x;
          const dy = touch.clientY - startPos.current.y;
        
          // socket.current.send(JSON.stringify({
          //   type: 'touch-drag',
          //   dx, dy,
          //   absX: touch.clientX,
          //   absY: touch.clientY,
          // }));
          send_mouse_event('touch-drag', dx, dy, touch.clientX,touch.clientY)
      }
      catch(ex){
        print("handleTouchMove: EXCEPTION e:", ex)
      }
    
    };

    const handleTouchEnd = () => {
      try {
        e.preventDefault(); 
      // socket.current.send(JSON.stringify({ type: 'touch-end' }));
        send_mouse_event('touch-end',0,0,0,0)
        startPosTouch.current = null;
      }
      catch(ex){
        print("handleTouchEnd: EXCEPTION e:", ex)
      }
    
    };
  
    el.addEventListener('touchstart', handleTouchStart, { passive: false });
  el.addEventListener('touchmove', handleTouchMove, { passive: false });
  el.addEventListener('touchend', handleTouchEnd, { passive: false });
  
    return () => {
      el.removeEventListener('touchstart', handleTouchStart);
      el.removeEventListener('touchmove', handleTouchMove);
      el.removeEventListener('touchend', handleTouchEnd);
    };
  }, [iframeUrl]);


  useEffect(() => {
    const preventContextMenu = (e) => e.preventDefault();
    window.addEventListener('contextmenu', preventContextMenu);
  
    return () => {
      window.removeEventListener('contextmenu', preventContextMenu);
    };
  }, []);

  const send_mouse_event = (mouse_action, dx, dy, absX, absY) => {
    console.log(`send_mouse_event: mouse_action: ${mouse_action}, dx: ${dx}, dy: ${dy},absX: ${absX}, absY: ${absY}`)

    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(
        JSON.stringify({
          type: 'mouse_command',
          display_id,
          mouse_action, dx, dy, absX, absY
        })
      );
    }
  };

  const send_scroll_command = async (deltaX, deltaY) => {
    console.log(`Scroll handler: scroll deltaX: ${deltaX}, deltaY: ${deltaY}`)
    if (ws && ws.readyState === WebSocket.OPEN) {
     
      ws.send(
        JSON.stringify({
          display_id,
          type: 'scroll_command',
          deltaX, deltaY,
        })
      );
    }
  }
///////////////////////////////////////////////////////////////////////////////////
  // Set up message listener for iframe communication
  useEffect(() => {
    const handleIframeMessage = (event) => {
      // Handle messages from the iframe if needed
      console.log('Message from iframe:', event.data);

      // If iframe is ready, we can clear the input to prepare for new input
      if (event.data.type === 'iframe_ready' ||
          event.data.type === 'keystroke_received' ||
          event.data.type === 'keydown_received') {
        setInputValue('');
      }
    };

    window.addEventListener('message', handleIframeMessage);

    return () => {
      window.removeEventListener('message', handleIframeMessage);
    };
  }, []);


  const connectToMagic_Websocket_Svc = async (display_id) => {

    try {
      // Create WebSocket connection
      const socket = new WebSocket(ws_url);

      socket.onopen = () => {
        console.log('connectToMagic_Websocket_Svc: WebSocket connected');
        // Send initial connection info
        socket.send(JSON.stringify({
          type: 'register_web',
          display_id: display_id,
        }));
      };

      socket.onmessage = (event) => {
        console.log('connectToMagic_Websocket_Svc: Rushi: Magic Server Message :', event.data);
        const data = JSON.parse(event.data);
        if (data.type === "on_focus") {
          focusHiddenInput(data);
        }
      };

      socket.onclose = () => {
        console.log('connectToMagic_Websocket_Svc: WebSocket closed');
      };

      socket.onerror = (error) => {
        console.error('connectToMagic_Websocket_Svc: WebSocket error:', error);
      };

      setWs(socket);
      set_display_id(display_id)
      console.log(`connectToMagic_Websocket_Svc: Connected: display_id: ${display_id}; ws_url ${ws_url}`);

    } catch (error) {
      console.error('connectToMagic_Websocket_Svc: EXCCEPTION setting up WebSocket:', error);
    }

  }
  // Fetch connection info from make_link endpoint
  const getConnectionInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('MagicApp: Fetching connection info from service.mediaverse.site/make_link');

      // Make request to get connection info
      const response = await axios.get('https://service.mediaverse.site/make_link', {
        params: {
          username: user?.email || 'guest', // Use user email from context or fallback to guest
          geometry: '480x850',
          timestamp: Date.now() // Add timestamp to prevent caching issues
        }
      });

      const data = response.data;
      console.log('MagicApp: Received connection info:', JSON.stringify(data));

      if (data.status === 'SUCCESS') {
        console.log('MagicApp: Connection info retrieved successfully');
        console.log('MagicApp: Display link:', data.display_link);
        console.log('MagicApp: Display ID:', data.display_id);
        
        // Set the iframe URL to the display_link from the response
        setIframeUrl(data.display_link);
        await connectToMagic_Websocket_Svc(data.display_id)
       
      } else {
        console.error('MagicApp: Failed to get connection info. Status:', data.status);
        setError('Failed to get connection info');
      }
    } catch (error) {
      console.error('MagicApp: Error fetching connection info:', error);
      console.error('MagicApp: Error details:', error.response ? error.response.data : 'No response data');
      setError('Failed to connect to server: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  // Call getConnectionInfo when the component mounts
  useEffect(() => {
    // Get connection info
    getConnectionInfo();
  }, []);  // Empty dependency array means this runs once on mount


  // Handle input changes and forward keystrokes to iframe
  const handleInputChange = (e) => {
    if (!isMobile) return

    const value = e.target.value;
    // setInputValue(value);

    // Get the last character typed (the new character)
    const lastChar = value.slice(-1);

    //Forward the keystroke to the iframe using postMessage
    const iframe = iframeRef.current?.querySelector('iframe');
    if (iframe && iframe.contentWindow) {
      try {
      
        // Also send via WebSocket if connected
        if (ws && ws.readyState === WebSocket.OPEN) {
          console.log(`handleInputChange: on_keystrokes to display_id ${display_id}; char ${lastChar}`)
          ws.send(JSON.stringify({
            display_id: display_id,
            type: 'on_keystrokes',
            key: lastChar,
            keyCode: lastChar.charCodeAt(0)
          }));
        }

        console.log('Sent keystroke to iframe:', lastChar);
      } catch (error) {
        console.error('Error sending message to iframe:', error);
      }
    }
  };

  // Handle special keys (backspace, enter, etc.)
  const handleKeyDown = (e) => {
    const key = e.key; // e.g., 'Enter', 'Backspace', 'a', etc.
    const keyCode = e.keyCode; // e.g., 13 for Enter
    console.log(`handleKeyDown: on_keystrokes to display_id ${display_id}; key ${key}`);

    const iframe = iframeRef.current?.querySelector('iframe');
    if (iframe && iframe.contentWindow) {
      try {
        if (ws && ws.readyState === WebSocket.OPEN) {
          
          if (keyCode != 229) {
            ws.send(JSON.stringify({
              display_id: display_id,
              type: 'on_keystrokes',
              key: key,
              keyCode: keyCode
            }));
        }
        }
  
        console.log('handleKeyDown: Sent key to iframe:', key);
      } catch (error) {
        console.error('handleKeyDown: Error sending key to iframe:', error);
      }
  
    }
  };

  const focusHiddenInput = (rectData) => {
    if (hiddenInputRef.current) {
      // Toggle keyboard visibility
      const newKeyboardState = true
      setKeyboardVisible(newKeyboardState);
      const el = hiddenInputRef.current;
      if (el) {
        el.style.position = 'absolute';
        el.style.left = `${rectData.x}px`;
        el.style.top = `${rectData.y}px`;
        el.style.width = `${rectData.width}px`;
        el.style.height = `${rectData.height}px`;
        el.style.border = '1px solid yellow';
        el.style.zIndex = '999';
        //el.style.opacity = '0.01'; // Keep keyboard behavior intact
        //el.style.pointerEvents = 'auto'; // Allow input
      }
      if (newKeyboardState) {
        // Focus the input to show keyboard
        hiddenInputRef.current.blur();
        hiddenInputRef.current.focus();

        // Scroll the iframe if needed
        if (iframeRef.current) {
          iframeRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      } else {
        // Blur the input to hide keyboard
        hiddenInputRef.current.blur();
      }
    }
  };

  // Determine iframe URL based on the fetched display_link
  const getIframeUrl = () => {
    if (iframeUrl) {
      return iframeUrl;
    }
    // Return empty string if URL is not yet fetched
    return "";
  };

  // Function to inject a script into the iframe to handle postMessage
  const injectIframeScript = () => {
 
  };

  return (
    <Modal
      animationType="slide"
      transparent={false}
      visible={true}
      onRequestClose={onClose}
    >
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }} 
      
     
      >
  
        {/* Show loading indicator while fetching URL */}
        {loading && (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <ActivityIndicator size="large" color="#0000ff" />
            <Text style={{ marginTop: 20 }}>Loading social media authorization...</Text>
          </View>
        )}

        {/* Show error message if there was an error */}
        {error && (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
            <Text style={{ color: 'red', textAlign: 'center', marginBottom: 20 }}>{error}</Text>
            <TouchableOpacity
              onPress={getConnectionInfo}
              style={{
                backgroundColor: '#0000ff',
                padding: 10,
                borderRadius: 5,
              }}
            >
              <Text style={{ color: 'white' }}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Input to capture keystrokes */}
        {/*isMobile &&*/ !loading && !error && (
          <input
            ref={hiddenInputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            keyboardType="default"
            autoCapitalize="none"  // or "sentences", "words", "characters"
            autoCorrect={false}
            style={{
              opacity: 0, // Completely invisible
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              zIndex: -1, // Place below the iframe to allow tapping on the app
              pointerEvents: 'auto' 
            }}
            autoComplete="off"
          />
         
        )}

        {/* Iframe showing VNC session */}
        {!loading && !error && iframeUrl && (<>
      
         <div
           ref={iframeRef}
           style={{
             width: '100%',
             height: '100vh',
             position: 'relative',
             zIndex: 1,
           
           }}
            
         >
           <iframe
             src={iframeUrl}
             style={{
               width: '100%',
               height: '100%',
               border: 'none',
               //pointerEvents: 'auto',
             }}
             title={`${platform} authorization for ${account}`}
             allow="camera; microphone; clipboard-read; clipboard-write"
             sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals"
           />

              </div>
              {/* Transparent gesture layer */}
            <div
              ref={gestureRef}

            // onMouseDown={handleMouseDown}
             // onWheel={handleWheel}
            //  onTouchStart={handleTouchStart2}
            //   onTouchMove={handleTouchMove2}
            //   onTouchEnd={handleTouchEnd2}

              style={{
                position: 'absolute',
                top: 0, left: 0, right: 0, bottom: 0,
                zIndex: 9999,
                pointerEvents: 'auto', // capture gestures
                touchAction: 'none',
                userSelect: 'none',  // prevent text/image selection
                WebkitTouchCallout: 'none', // disable long-press context menu on iOS Safari
                WebkitUserSelect: 'none',
              }}
            />
             
         </>)}

      </View>
    </Modal>
  );
};

MagicApp.propTypes = {
  platform: PropTypes.string.isRequired,
  account: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default MagicApp;
