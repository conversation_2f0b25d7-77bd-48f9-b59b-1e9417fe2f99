import React from 'react';
import { View, Image, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';

/**
 * A component that displays the Sanatana logo as a watermark/background element
 * 
 * @param {Object} props - Component props
 * @param {number} props.size - Size of the logo in pixels (default: 200)
 * @param {number} props.opacity - Opacity of the logo (default: 0.1)
 * @param {string} props.position - Position of the watermark: 'center', 'top-left', 'top-right', 'bottom-left', 'bottom-right' (default: 'center')
 * @param {Object} props.style - Additional style for the container
 */
const WatermarkLogo = ({
  size = 200,
  opacity = 0.1,
  position = 'center',
  style = {},
}) => {
  // Determine position styles
  let positionStyle = {};
  
  switch (position) {
    case 'top-left':
      positionStyle = { top: -size * 0.3, left: -size * 0.3 };
      break;
    case 'top-right':
      positionStyle = { top: -size * 0.3, right: -size * 0.3 };
      break;
    case 'bottom-left':
      positionStyle = { bottom: -size * 0.3, left: -size * 0.3 };
      break;
    case 'bottom-right':
      positionStyle = { bottom: -size * 0.3, right: -size * 0.3 };
      break;
    case 'center':
    default:
      positionStyle = { top: '50%', left: '50%', transform: [{ translateX: -size / 2 }, { translateY: -size / 2 }] };
      break;
  }

  return (
    <View style={[styles.container, style]}>
      <Image
        source={require('../../assets/sanatana_logo.jpg')}
        style={[
          styles.image,
          { width: size, height: size, opacity, borderRadius: size / 2 },
          positionStyle,
        ]}
        resizeMode="contain"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  image: {
    position: 'absolute',
    zIndex: -1,
  },
});

WatermarkLogo.propTypes = {
  size: PropTypes.number,
  opacity: PropTypes.number,
  position: PropTypes.oneOf(['center', 'top-left', 'top-right', 'bottom-left', 'bottom-right']),
  style: PropTypes.object,
};

export default WatermarkLogo;
