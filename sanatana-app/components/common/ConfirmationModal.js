import React from "react";
import { View, Text, TouchableOpacity, Modal } from "react-native";
import PropTypes from "prop-types";
import Animated<PERSON>ogo from "./AnimatedLogo";

/**
 * A custom confirmation modal that replaces the browser's native confirm() dialog
 *
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {function} props.onClose - Function to call when the modal is closed
 * @param {string} props.message - The message to display in the modal
 * @param {function} props.onConfirm - Function to call when the user confirms
 * @param {function} props.onCancel - Function to call when the user cancels
 * @param {string} props.confirmText - Text for the confirm button (default: "Yes")
 * @param {string} props.cancelText - Text for the cancel button (default: "No")
 */
const ConfirmationModal = ({
  visible,
  onClose,
  message,
  onConfirm,
  onCancel,
  confirmText = "Yes",
  cancelText = "No",
}) => {
  return (
    <Modal
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      animationType="fade"
    >
      <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
        <View
          className="bg-white rounded-lg p-6 mx-8 max-w-xs shadow-lg"
          style={{ width: "auto", minWidth: 280, maxWidth: 320 }}
        >
          <View className="flex-row items-center mb-4">
            <AnimatedLogo
              size={40}
              backgroundColor="#DBEAFE"
              animation="hueShift"
              style={{ marginRight: 12 }}
            />
            <Text className="text-gray-800 text-base flex-1">{message}</Text>
          </View>

          <View className="flex-row justify-between space-x-4">
            <TouchableOpacity
              onPress={() => {
                onConfirm();
                onClose();
              }}
              className="mx-6 px-6 py-2 rounded-md bg-gray-200"
            >
              <Text className="text-gray-800  font-medium">{confirmText}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                onCancel();
                onClose();
              }}
              className="mx-6 px-6 py-2 rounded-md bg-gray-200"
            >
              <Text className="text-gray-800 font-medium">{cancelText}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

ConfirmationModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  message: PropTypes.string.isRequired,
  onConfirm: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
};

export default ConfirmationModal;
