import React from "react";
import { TouchableOpacity, Text } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";

/**
 * A button to navigate back to the Upload Wizard
 *
 * @param {Object} props - Component props
 * @param {function} props.onPress - Function to call when the button is pressed
 */
const BackToWizardButton = ({ onPress }) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        position: "absolute",
        top: 10, // Move it higher up
        left: 20, // 20px from the left
        marginRight: 20, // 20px margin to the right
        zIndex: 999, // Ensure it's above everything else
        elevation: 5, // For Android
        backgroundColor: "#8B4513", // Strong brown color
        paddingVertical: 12, // Larger padding
        paddingHorizontal: 20,
        borderRadius: 8,
        flexDirection: "row",
        alignItems: "center",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      }}
    >
      <Icon name="arrow-left" type="font-awesome-5" size={22} color="#FFFFFF" />
      <Text
        style={{
          color: "#FFFFFF",
          fontWeight: "bold",
          marginLeft: 10,
          fontSize: 18,
        }}
      >
        Back to Wizard
      </Text>
    </TouchableOpacity>
  );
};

BackToWizardButton.propTypes = {
  onPress: PropTypes.func.isRequired,
};

export default BackToWizardButton;
