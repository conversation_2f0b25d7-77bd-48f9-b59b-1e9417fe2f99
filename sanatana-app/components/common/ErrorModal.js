import React from "react";
import { View, Text, TouchableOpacity, Modal } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import Animated<PERSON>ogo from "./AnimatedLogo";

/**
 * A custom error modal that displays an error message with an OK button
 *
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {function} props.onClose - Function to call when the modal is closed
 * @param {string} props.message - The message to display in the modal
 * @param {string} props.title - The title of the modal (default: "Error")
 * @param {string} props.buttonText - Text for the button (default: "OK")
 */
const ErrorModal = ({
  visible,
  onClose,
  message,
  title = "Error",
  buttonText = "OK",
}) => {
  return (
    <Modal
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      animationType="fade"
    >
      <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
        <View
          className="bg-white rounded-lg p-6 mx-8 shadow-lg"
          style={{ width: "auto", minWidth: 280, maxWidth: 320 }}
        >
          <View className="flex-row items-center mb-4">
            <View className="flex-row items-center">
              <AnimatedLogo
                size={32}
                backgroundColor="#FEE2E2"
                animation="rotate"
                style={{ marginRight: 8 }}
              />
              <Icon
                name="exclamation-circle"
                type="font-awesome"
                size={24}
                color="#EF4444"
              />
            </View>
            <Text className="text-red-600 text-lg font-medium ml-2">
              {title}
            </Text>
          </View>

          <Text className="text-gray-800 text-base mb-4">{message}</Text>

          <View className="flex-row justify-center">
            <TouchableOpacity
              onPress={onClose}
              className="px-6 py-2 rounded-md bg-blue-600"
            >
              <Text className="text-white font-medium">{buttonText}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

ErrorModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  message: PropTypes.string.isRequired,
  title: PropTypes.string,
  buttonText: PropTypes.string,
};

export default ErrorModal;
