import React from "react";
import { View, Text, TouchableOpacity, Modal } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import Animated<PERSON>ogo from "./AnimatedLogo";

/**
 * A custom alert modal that replaces the browser's native alert() dialog
 *
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {function} props.onClose - Function to call when the modal is closed
 * @param {string} props.message - The message to display in the modal
 * @param {string} props.type - The type of alert: "success", "error", "info", "warning"
 * @param {string} props.buttonText - Text for the button (default: "OK")
 */
const AlertModal = ({
  visible,
  onClose,
  message,
  type = "info",
  buttonText = "OK",
}) => {
  // Define colors based on type
  const getTypeStyles = () => {
    switch (type) {
      case "success":
        return {
          bgColor: "bg-green-100",
          textColor: "text-green-800",
          iconName: "check-circle",
          iconColor: "#10B981",
        };
      case "error":
        return {
          bgColor: "bg-red-100",
          textColor: "text-red-800",
          iconName: "exclamation-circle",
          iconColor: "#EF4444",
        };
      case "warning":
        return {
          bgColor: "bg-yellow-100",
          textColor: "text-yellow-800",
          iconName: "exclamation-triangle",
          iconColor: "#F59E0B",
        };
      case "info":
      default:
        return {
          bgColor: "bg-blue-100",
          textColor: "text-blue-800",
          iconName: "info-circle",
          iconColor: "#3B82F6",
        };
    }
  };

  const typeStyles = getTypeStyles();

  return (
    <Modal
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      animationType="fade"
    >
      <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
        <View
          className={`bg-white rounded-lg p-6 mx-8 shadow-lg ${typeStyles.bgColor}`}
          style={{ width: "auto", minWidth: 280, maxWidth: 320 }}
        >
          <View className="flex-row items-center mb-4">
            <View className="flex-row items-center">
              <AnimatedLogo
                size={32}
                backgroundColor={
                  type === "success"
                    ? "#D1FAE5"
                    : type === "error"
                    ? "#FEE2E2"
                    : type === "warning"
                    ? "#FEF3C7"
                    : "#DBEAFE"
                }
                animation={
                  type === "success"
                    ? "pulse"
                    : type === "error"
                    ? "rotate"
                    : type === "warning"
                    ? "fade"
                    : "hueShift"
                }
                style={{ marginRight: 8 }}
              />
              <Icon
                name={typeStyles.iconName}
                type="font-awesome-5"
                color={typeStyles.iconColor}
                size={24}
              />
            </View>
            <Text
              className={`${typeStyles.textColor} text-lg font-medium ml-2`}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </Text>
          </View>

          <Text className={`${typeStyles.textColor} text-base mb-4`}>
            {message}
          </Text>

          <View className="flex-row justify-center">
            <TouchableOpacity
              onPress={onClose}
              className="px-6 py-2 rounded-md bg-white border border-gray-300"
            >
              <Text className="text-gray-800 font-medium">{buttonText}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

AlertModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  message: PropTypes.string.isRequired,
  type: PropTypes.oneOf(["success", "error", "info", "warning"]),
  buttonText: PropTypes.string,
};

export default AlertModal;
