import React from "react";
import { View, Text, TouchableOpacity, Modal, ActivityIndicator } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import Animated<PERSON>ogo from "./AnimatedLogo";

/**
 * A modal that displays file upload progress
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {number} props.progress - Upload progress percentage (0-100)
 * @param {string} props.error - Error message (if any)
 * @param {function} props.onCancel - Function to call when the upload is canceled
 * @param {function} props.onRetry - Function to call when retry is requested
 * @param {function} props.onClose - Function to call when the modal is closed
 */
const UploadProgressModal = ({
  visible,
  progress,
  error,
  onCancel,
  onRetry,
  onClose,
}) => {
  // Determine if upload is complete
  const isComplete = progress >= 100 && !error;
  
  return (
    <Modal
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      animationType="fade"
    >
      <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
        <View
          className="bg-white rounded-lg p-6 mx-8 shadow-lg"
          style={{ width: "auto", minWidth: 280, maxWidth: 320 }}
        >
          <View className="flex-row items-center mb-4">
            <AnimatedLogo 
              size={32} 
              backgroundColor={error ? "#FEE2E2" : "#DBEAFE"} 
              animation={error ? "rotate" : "pulse"}
              style={{ marginRight: 8 }}
            />
            <Text className={`${error ? "text-red-600" : "text-blue-600"} text-lg font-medium`}>
              {error ? "Upload Error" : isComplete ? "Upload Complete" : "Uploading File"}
            </Text>
          </View>

          {!error && !isComplete && (
            <>
              <View className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                <View 
                  className="bg-blue-600 h-2.5 rounded-full" 
                  style={{ width: `${progress}%` }}
                />
              </View>
              <Text className="text-gray-600 text-sm mb-4">
                {progress.toFixed(1)}% complete
              </Text>
            </>
          )}

          {!error && isComplete && (
            <Text className="text-gray-800 text-base mb-4">
              Your file has been uploaded successfully!
            </Text>
          )}

          {error && (
            <Text className="text-red-600 text-base mb-4">
              {error}
            </Text>
          )}

          <View className="flex-row justify-end">
            {!isComplete && !error && (
              <TouchableOpacity
                onPress={onCancel}
                className="px-4 py-2 rounded-md bg-red-100 mr-2"
              >
                <Text className="text-red-700 font-medium">Cancel</Text>
              </TouchableOpacity>
            )}

            {error && (
              <TouchableOpacity
                onPress={onRetry}
                className="px-4 py-2 rounded-md bg-blue-100 mr-2"
              >
                <Text className="text-blue-700 font-medium">Retry</Text>
              </TouchableOpacity>
            )}

            {(isComplete || error) && (
              <TouchableOpacity
                onPress={onClose}
                className="px-4 py-2 rounded-md bg-blue-600"
              >
                <Text className="text-white font-medium">Close</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

UploadProgressModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  progress: PropTypes.number,
  error: PropTypes.string,
  onCancel: PropTypes.func,
  onRetry: PropTypes.func,
  onClose: PropTypes.func.isRequired,
};

UploadProgressModal.defaultProps = {
  progress: 0,
  error: null,
  onCancel: () => {},
  onRetry: () => {},
};

export default UploadProgressModal;
