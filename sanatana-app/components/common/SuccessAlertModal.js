import React from "react";
import { View, Text, TouchableOpacity, Modal } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import Animated<PERSON>ogo from "./AnimatedLogo";
import { useNavigation } from "@react-navigation/native";

/**
 * A custom success alert modal with two action buttons
 *
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {function} props.onClose - Function to call when the modal is closed
 * @param {string} props.message - The message to display in the modal
 * @param {function} props.onStartNewWizard - Function to call to start a new wizard
 */
const SuccessAlertModal = ({
  visible,
  onClose,
  message,
  onStartNewWizard,
}) => {
  const navigation = useNavigation();

  const handleGoToJobQueue = () => {
    // Close the modal first
    onClose();

    // Call onStartNewWizard to reset the wizard state and close it
    // This ensures the wizard is fully closed when navigating away
    // Note: onStartNewWizard now handles closing the wizard
    onStartNewWizard();

    // Use a timeout to ensure the modal is closed before navigation
    setTimeout(() => {
      // Navigate to the Profile tab
      navigation.navigate('Profile');

      // Use another timeout to ensure the Profile tab is loaded before navigating to JobStatus
      setTimeout(() => {
        navigation.navigate('Profile', { screen: 'JobStatus' });
      }, 100);
    }, 100);
  };

  const handleStartNewWizard = () => {
    // onStartNewWizard now handles closing the wizard
    onStartNewWizard();
  };

  return (
    <Modal
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      animationType="fade"
    >
      <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
        <View
          className="bg-white rounded-lg p-6 mx-8 shadow-lg bg-green-100"
          style={{ width: "auto", minWidth: 280, maxWidth: 350 }}
        >
          <View className="flex-row items-center mb-4">
            <View className="flex-row items-center">
              <AnimatedLogo
                size={32}
                backgroundColor="#D1FAE5"
                animation="pulse"
                style={{ marginRight: 8 }}
              />
              <Icon
                name="check-circle"
                type="font-awesome-5"
                color="#10B981"
                size={24}
              />
            </View>
            <Text className="text-green-800 text-lg font-medium ml-2">
              Success
            </Text>
          </View>

          <Text className="text-green-800 text-base mb-6">
            {message}
          </Text>

          <View className="flex-col space-y-3">
            <TouchableOpacity
              onPress={handleGoToJobQueue}
              className="px-6 py-3 rounded-md bg-indigo-600"
            >
              <Text className="text-white font-medium text-center">Go to Job Queue</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleStartNewWizard}
              className="px-6 py-3 rounded-md bg-white border border-gray-300"
            >
              <Text className="text-gray-800 font-medium text-center">Start New Wizard</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

SuccessAlertModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  message: PropTypes.string.isRequired,
  onStartNewWizard: PropTypes.func.isRequired,
};

export default SuccessAlertModal;
