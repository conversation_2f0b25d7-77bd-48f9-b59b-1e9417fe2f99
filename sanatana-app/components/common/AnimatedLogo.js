import React, { useEffect, useState } from "react";
import { View, Image, Animated, Easing, StyleSheet } from "react-native";
import PropTypes from "prop-types";

/**
 * An animated Sanatana logo component with various animation options
 * 
 * @param {Object} props - Component props
 * @param {number} props.size - Size of the logo in pixels (default: 40)
 * @param {string} props.backgroundColor - Background color of the circular container (default: "white")
 * @param {string} props.animation - Type of animation: "pulse", "rotate", "fade", "hueShift", or "none" (default: "pulse")
 * @param {boolean} props.circle - Whether to display the logo in a circular container (default: true)
 * @param {Object} props.style - Additional style for the container
 */
const AnimatedLogo = ({
  size = 40,
  backgroundColor = "white",
  animation = "pulse",
  circle = true,
  style = {},
}) => {
  // Animation values
  const pulseAnim = new Animated.Value(1);
  const rotateAnim = new Animated.Value(0);
  const fadeAnim = new Animated.Value(1);
  const [hueRotate, setHueRotate] = useState(0);

  useEffect(() => {
    let animationLoop;

    switch (animation) {
      case "pulse":
        // Pulse animation
        animationLoop = Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnim, {
              toValue: 1.1,
              duration: 1000,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 1000,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
          ])
        );
        break;

      case "rotate":
        // Rotation animation
        animationLoop = Animated.loop(
          Animated.timing(rotateAnim, {
            toValue: 1,
            duration: 3000,
            easing: Easing.linear,
            useNativeDriver: true,
          })
        );
        break;

      case "fade":
        // Fade in/out animation
        animationLoop = Animated.loop(
          Animated.sequence([
            Animated.timing(fadeAnim, {
              toValue: 0.5,
              duration: 1000,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 1000,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
          ])
        );
        break;

      case "hueShift":
        // Hue shift animation (CSS filter not supported by React Native, using state)
        const interval = setInterval(() => {
          setHueRotate((prev) => (prev + 10) % 360);
        }, 100);
        return () => clearInterval(interval);

      default:
        // No animation
        return;
    }

    animationLoop.start();

    return () => {
      if (animationLoop) {
        animationLoop.stop();
      }
    };
  }, [animation, pulseAnim, rotateAnim, fadeAnim]);

  // Calculate rotation interpolation
  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  // Apply the appropriate animation style
  const animationStyle = {};
  if (animation === "pulse") {
    animationStyle.transform = [{ scale: pulseAnim }];
  } else if (animation === "rotate") {
    animationStyle.transform = [{ rotate: spin }];
  } else if (animation === "fade") {
    animationStyle.opacity = fadeAnim;
  }

  // Apply hue rotation filter for web (not supported in React Native)
  const hueRotateStyle = animation === "hueShift" ? { filter: `hue-rotate(${hueRotate}deg)` } : {};

  return (
    <View
      style={[
        styles.container,
        circle && styles.circle,
        { backgroundColor, width: size, height: size, borderRadius: circle ? size / 2 : 0 },
        style,
      ]}
    >
      <Animated.View style={animationStyle}>
        <Image
          source={require("../../assets/sanatana_logo.jpg")}
          style={[
            { width: circle ? size * 0.8 : size, height: circle ? size * 0.8 : size },
            circle && { borderRadius: (size * 0.8) / 2 },
            hueRotateStyle,
          ]}
          resizeMode="cover"
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  circle: {
    padding: 2,
  },
});

AnimatedLogo.propTypes = {
  size: PropTypes.number,
  backgroundColor: PropTypes.string,
  animation: PropTypes.oneOf(["pulse", "rotate", "fade", "hueShift", "none"]),
  circle: PropTypes.bool,
  style: PropTypes.object,
};

export default AnimatedLogo;
