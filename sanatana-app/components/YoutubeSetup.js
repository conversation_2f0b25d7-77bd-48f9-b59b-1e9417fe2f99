import React, { useState, useEffect, useContext } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
} from "react-native";
import PropTypes from "prop-types";

import { FontAwesome } from "@expo/vector-icons";
import Constants from "expo-constants";
import { UserContext } from "../context/UserContext";
import AuthorizedChannels from "./AuthorizedChannels";
import YoutubeTutorialModal from "./YoutubeTutorialModal";

const YoutubeSetup = ({ onBackToWizard }) => {
  const { user } = useContext(UserContext);
  const [authorizedUrls, setAuthorizedUrls] = useState([]);
  const [isYoutubeTutorialVisible, setYoutubeTutorialVisible] = useState(false);

  const { width } = useWindowDimensions();
  const isDesktop = width > 768;

  // Function to fetch authorized channels
  const fetchAuthUrls = async () => {
    if (!user || !user.email) {
      console.log("YoutubeSetup: No user email available");
      return;
    }

    try {
      console.log("YoutubeSetup: Fetching authorization URLs for", user.email);
      const response = await fetch(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/youtube/get_authorization_urls`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ sanatana_email: user.email }),
        }
      );
      console.log("YoutubeSetup: fetchAuthUrls: response", response);
      const data = await response.json();
      if (data.length === 0) {
        console.log("YoutubeSetup: No authorized URLs found");
      } else {
        console.log("YoutubeSetup: Found", data.length, "authorized URLs");
        setAuthorizedUrls(data);
      }
    } catch (error) {
      console.error("Fetch error:", error);
    }
  };

  // Initial fetch on component mount
  useEffect(() => {
    fetchAuthUrls();
  }, [user]);

  // Handler for when authorization steps are closed
  const handleCloseAuthSteps = () => {
    console.log("Close Auth Steps - Refreshing authorized channels");
    // Refresh the authorized channels list
    fetchAuthUrls();
  };

  const openYoutubeCredentialsScreen = () => {
    setYoutubeTutorialVisible(true);
    console.log("Open here Youtube Credential Screen");
  };

  const handle_youtube_upload = async (file) => {
    const formData = new FormData();
    formData.append("creds", file);
    formData.append("sanatana_email", user.email);
    formData.append("for_use", user.email);
    formData.append("platform", "youtube");

    try {
      const response = await fetch(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/youtube/upload_user_creds`,
        {
          method: "POST",
          body: formData,
        }
      );
      if (!response.ok) throw new Error("Upload failed");

      // Refresh auth URLs after successful upload
      const fetchAuthUrls = async () => {
        const response = await fetch(
          `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/youtube/get_authorization_urls`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ sanatana_email: user.email }),
          }
        );
        const data = await response.json();
        setAuthorizedUrls(data);
      };
      fetchAuthUrls();
    } catch (error) {
      console.error("Upload error:", error);
    }
  };

  return (
    <View className="my-4  rounded-lg">
      <ScrollView
        contentContainerStyle={{ paddingBottom: 20 }}
        style={{ maxHeight: 500 }}
      >
        {authorizedUrls.length > 0 ? (
          <AuthorizedChannels
            authorizedUrls={authorizedUrls}
            onCloseAuthSteps={handleCloseAuthSteps}
            onBackToWizard={onBackToWizard}
          />
        ) : (
          <View className="flex-row justify-between p-3 border-b border-gray-200">
            <Text className="flex-1 text-gray-600">No credentials found</Text>
          </View>
        )}
      </ScrollView>

      {authorizedUrls.length === 0 ? (
        <Text className="text-[16px] font-bold mb-2 px-4">
          No YouTube credentials yet? Don't miss out! Watch the video below on how to get them.
        </Text>
      ) : (
        <Text className="text-[16px] font-bold mb-2 px-4">
          Maximize your uploads! Get more credentials from unused Google
          accounts or create new Google accounts. More credentials mean more
          video uploads!
        </Text>
      )}
      <View className="mb-4 px-4">
        <View className="flex-row items-center mb-2">
          <Text className="text-base px-2">Upload Credentials</Text>

          {isDesktop ? (
            <TouchableOpacity
              onPress={openYoutubeCredentialsScreen}
              className="flex-row items-center ml-2"
            >
              <FontAwesome name="play-circle" size={20} color="blue" />
              <Text className="text-blue-600 font-semibold ml-2">
              Watch How to Get Credentials
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              onPress={openYoutubeCredentialsScreen}
              className="items-center ml-2"
            >
              <View className="flex-row items-center">
              <FontAwesome name="play-circle" size={20} color="blue" />
                <Text className="text-sm text-blue-600 font-semibold">Watch How to</Text>
              </View>
              <Text className="text-sm text-blue-600 ml-1 font-semibold">
                Get Credentials
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <input
          type="file"
          onChange={(e) => handle_youtube_upload(e.target.files[0])}
          accept=".json"
          className="w-full p-2 border rounded"
        />
      </View>
      {/* Fullscreen Modal */}
      <YoutubeTutorialModal
        visible={isYoutubeTutorialVisible}
        onClose={() => setYoutubeTutorialVisible(false)}
      />
      {/* Close Button */}
      <TouchableOpacity
        onPress={() => setYoutubeTutorialVisible(false)}
        className=""
      >
        <FontAwesome name="close" size={24} color="white" />
      </TouchableOpacity>
    </View>
  );
};

// Add PropTypes validation
YoutubeTutorialModal.propTypes = {
  onBackToWizard: PropTypes.func,
};

// Default props
YoutubeSetup.defaultProps = {
  onBackToWizard: () => {},
};

export default YoutubeSetup;
