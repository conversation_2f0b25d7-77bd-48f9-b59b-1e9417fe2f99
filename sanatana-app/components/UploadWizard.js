import React, { useState, useEffect, useContext } from "react";
import { View, Text, TouchableOpacity, ScrollView, Image } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import SourceSelector from "./wizard/SourceSelector";
import DestinationSelector from "./wizard/DestinationSelector";
import TitleEditor from "./wizard/TitleEditor";
import DescriptionEditor from "./wizard/DescriptionEditor";
import ScheduleSelector from "./wizard/ScheduleSelector";
import SourceDisplayComponent from "./wizard/SourceDisplayComponent";
import DestinationDisplayComponent from "./wizard/DestinationDisplayComponent";
import ConfirmationModal from "./common/ConfirmationModal";
import AlertModal from "./common/AlertModal";
import SuccessAlertModal from "./common/SuccessAlertModal";
import AnimatedLogo from "./common/AnimatedLogo";
import { uploadSelection } from "../utils/uploadSelection";
import { UserContext } from "../context/UserContext";
// Import clearYoutubeCache for the handleClearYoutubeCache function
import { clearYoutubeCache } from "../utils/youtubeCache";

const STEPS = {
  SOURCE: 0,
  TITLE: 1,
  DESCRIPTION: 2,
  DESTINATION: 3,
  SCHEDULE: 4,
  CONFIRM: 5,
};

// Common styles for consistent UI
const COMMON_STYLES = {
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: "12px 0", // Vertical padding only
    marginBottom: 0, // Positioned right at the bottom
    backgroundColor: "white",
    borderTop: "1px solid #e5e7eb",
    zIndex: 10,
  },
  // Style for input dialog buttons
  inputButtonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: "12px 0", // Vertical padding only
    marginBottom: 0, // Positioned right at the bottom
    backgroundColor: "white",
    borderTop: "1px solid #e5e7eb",
    zIndex: 10,
  },
  // Button style with horizontal margins
  button: {
    marginHorizontal: 20, // 20px margin on left and right
  },
};

// Storage keys
const WIZARD_STEP_KEY = "sanatana_upload_wizard_step";
const WIZARD_DATA_KEY = "sanatana_upload_wizard_data";

export default function UploadWizard({ onClose }) {
  // State for modals
  const { user } = useContext(UserContext);
  const [showCloseConfirmation, setShowCloseConfirmation] = useState(false);
  const [showResetConfirmation, setShowResetConfirmation] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [alertType, setAlertType] = useState("info");

  // State for file upload progress is handled in the fileUploadHandler.js
  // Initialize with saved state or defaults
  const [currentStep, setCurrentStep] = useState(() => {
    // Try to get saved step from localStorage
    if (typeof window !== "undefined") {
      const savedStep = localStorage.getItem(WIZARD_STEP_KEY);
      return savedStep !== null ? parseInt(savedStep, 10) : STEPS.SOURCE;
    }
    return STEPS.SOURCE;
  });

  // Track sub-steps for source selection
  const [sourceSubStep, setSourceSubStep] = useState(null);

  const [wizardData, setWizardData] = useState(() => {
    // Try to get saved data from localStorage
    if (typeof window !== "undefined") {
      const savedData = localStorage.getItem(WIZARD_DATA_KEY);
      return savedData !== null
        ? JSON.parse(savedData)
        : {
            source: null,
            sourceName: null,
            destinations: [],
            title: "",
            description: "",
            extractFromFolder: false,
            scheduleDate: null,
            scheduleTime: null,
          };
    }
    return {
      source: null,
      sourceName: null,
      destinations: [],
      title: "",
      description: "",
      extractFromFolder: false,
      scheduleDate: null,
      scheduleTime: null,
    };
  });

  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(WIZARD_STEP_KEY, currentStep.toString());
      localStorage.setItem(WIZARD_DATA_KEY, JSON.stringify(wizardData));
    }
  }, [currentStep, wizardData]);

  // Debug: Log initial state
  console.log("Initializing UploadWizard with steps:", STEPS);
  console.log("Initial currentStep:", currentStep);
  console.log("Initial wizardData:", wizardData);

  const handleStepComplete = (data) => {
    console.log("Step complete. Received data:", data);

    setWizardData((prev) => {
      const newData = { ...prev, ...data };
      console.log("Updated wizardData:", newData);
      return newData;
    });

    if (currentStep < Object.keys(STEPS).length - 1) {
      const nextStep = currentStep + 1;
      console.log("Moving to next step:", nextStep);
      setCurrentStep(nextStep);
    } else {
      console.log("Already at last step, not incrementing");
    }
  };

  const handleConfirmUpload = async () => {
    console.log("Confirming upload with data:", wizardData);
    // If this is a file upload, we need to upload the file
    if (wizardData.source === "file" && wizardData.fileInfo) {
      setAlertMessage(
        "File Upload not implemented yet. Please use other sources."
      );
      setAlertType("failure");
      setShowAlert(true);
      startNewWizard();
      return;
    }

    try {
      // Upload the selection data to the server
      const result = await uploadSelection({
        wizardData,
        sanatanaEmail: user.email,
      });

      console.log("Upload selection result:", result);

      // Show success message with options
      setAlertMessage("Upload scheduled successfully! Your content will be processed and uploaded according to your settings.");
      setShowSuccessAlert(true);

      // Note: We don't automatically clear the wizard state here
      // The user will choose whether to start a new wizard or go to job queue
    } catch (error) {
      console.error("Error scheduling upload:", error);

      setAlertMessage("Failed to schedule upload. Please try again.");
      setAlertType("error");
      setShowAlert(true);
      // Don't show success alert for errors
    }
  };

  // Function to navigate to the previous step while preserving all wizard data
  const handlePreviousStep = () => {
    // If we're on a source sub-step, go back to main source selection
    if (currentStep === STEPS.SOURCE && sourceSubStep) {
      console.log(
        "Moving back to main source selection from sub-step:",
        sourceSubStep
      );
      setSourceSubStep(null);
    }
    // If we're on Title step and came from a source with sub-step, go back to that sub-step
    else if (currentStep === STEPS.TITLE && wizardData.source) {
      console.log("Moving back to source sub-step:", wizardData.source);
      setCurrentStep(STEPS.SOURCE);
      setSourceSubStep(wizardData.source);
    }
    // Otherwise just go back one step
    else if (currentStep > STEPS.SOURCE) {
      console.log("Moving to previous step:", currentStep - 1);
      setCurrentStep(currentStep - 1);
    }
  };

  // Function to clear YouTube cache if needed (e.g., for troubleshooting)
  // This function is not currently used but is kept for future use or debugging
  // eslint-disable-next-line no-unused-vars
  const handleClearYoutubeCache = () => {
    clearYoutubeCache();
    console.log("YouTube video info cache cleared");
  };

  const startNewWizard = () => {
    console.log("Starting new wizard");
    // Reset all state variables
    setCurrentStep(STEPS.SOURCE);
    setSourceSubStep(null);
    setWizardData({
      source: null,
      sourceName: null,
      destinations: [],
      title: "",
      description: "",
      extractFromFolder: false,
      scheduleDate: null,
      scheduleTime: null,
      videoLink: null,
      videoId: null,
      useYouTubeTitle: false,
      useYouTubeDescription: false,
      hashtags: [],
    });
    setShowResetConfirmation(false);

    // Clear any alerts
    setShowAlert(false);
    setShowSuccessAlert(false);

    // Clear local storage
    if (typeof window !== "undefined") {
      localStorage.removeItem(WIZARD_STEP_KEY);
      localStorage.removeItem(WIZARD_DATA_KEY);

      // We don't clear the YouTube cache here to keep it for future use
      // Only clear it if explicitly requested or if it's causing issues
      // handleClearYoutubeCache();
    }
  };

  const renderStep = () => {
    console.log("Rendering step:", currentStep);
    switch (currentStep) {
      case STEPS.SOURCE:
        return (
          <SourceSelector
            onComplete={handleStepComplete}
            initialSource={wizardData.source}
            initialSourceData={wizardData}
            sourceSubStep={sourceSubStep}
            setSourceSubStep={setSourceSubStep}
            buttonContainerStyle={COMMON_STYLES.buttonContainer}
            inputButtonContainerStyle={COMMON_STYLES.inputButtonContainer}
          />
        );
      case STEPS.TITLE:
        return (
          <TitleEditor
            onComplete={handleStepComplete}
            wizardData={wizardData}
            buttonContainerStyle={COMMON_STYLES.buttonContainer}
          />
        );
      case STEPS.DESCRIPTION:
        return (
          <DescriptionEditor
            onComplete={handleStepComplete}
            wizardData={wizardData}
            buttonContainerStyle={COMMON_STYLES.buttonContainer}
          />
        );
      case STEPS.DESTINATION:
        return (
          <DestinationSelector
            onComplete={handleStepComplete}
            initialDestinations={wizardData.destinations}
            wizardData={wizardData} // Pass the entire wizardData object to access categories
            buttonContainerStyle={COMMON_STYLES.buttonContainer}
          />
        );
      case STEPS.SCHEDULE:
        return (
          <ScheduleSelector
            onComplete={handleStepComplete}
            initialData={wizardData}
            buttonContainerStyle={COMMON_STYLES.buttonContainer}
          />
        );
      case STEPS.CONFIRM:
        console.log("Rendering CONFIRM step with data:", wizardData);
        return (
          <View className="flex-1">
            <ScrollView
              className="flex-1"
              style={{ maxHeight: "75%" }}
              contentContainerStyle={{ paddingBottom: 20 }}
            >
              <View className="p-5">
                <Text className="text-2xl font-bold mb-5">
                  Review Your Upload
                </Text>

                {/* Source Information */}
                <SourceDisplayComponent wizardData={wizardData} />

                {/* Destination & Schedule Information */}
                <DestinationDisplayComponent wizardData={wizardData} />

                {/* Debug JSON Output */}
                {/* <View className="mb-5 p-4 bg-gray-50 rounded-lg">
                  <Text className="text-sm font-medium text-gray-700 mb-2">
                    Debug Information (JSON):
                  </Text>
                  <Text className="text-xs text-gray-600">
                    {JSON.stringify(wizardData, null, 2)}
                  </Text>
                </View> */}
              </View>
            </ScrollView>

            {/* Confirm button */}
            <View style={COMMON_STYLES.buttonContainer}>
              <TouchableOpacity
                onPress={handleConfirmUpload}
                className="bg-blue-600 py-4 rounded-lg mx-5"
                style={COMMON_STYLES.button}
              >
                <View className="flex-row items-center justify-center">
                  <View className="bg-white rounded-full p-1 mr-3">
                    <Image
                      source={require("../assets/sanatana_logo.jpg")}
                      style={{ width: 24, height: 24, borderRadius: 12 }}
                    />
                  </View>
                  <Text className="text-white text-center font-semibold">
                    Confirm Upload
                  </Text>
                  <View className="bg-white rounded-full p-1 ml-3">
                    <Image
                      source={require("../assets/sanatana_logo.jpg")}
                      style={{ width: 24, height: 24, borderRadius: 12 }}
                    />
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        );
      default:
        console.error("Unknown step:", currentStep);
        return null;
    }
  };

  console.log("Rendering UploadWizard container");
  return (
    <View
      className="flex-1 bg-white rounded-2xl overflow-hidden"
      style={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <View className="flex-row justify-between items-center p-4 border-b border-gray-200">
        <View className="flex-row items-center">
          {/* Close Button */}
          <TouchableOpacity
            onPress={() => {
              // Ask for confirmation if there's data in the wizard
              if (
                wizardData.source ||
                wizardData.destinations.length > 0 ||
                wizardData.title ||
                wizardData.description
              ) {
                setShowCloseConfirmation(true);
              } else {
                onClose();
              }
            }}
          >
            <Icon
              name="times"
              type="font-awesome-5"
              size={24}
              color="#4B5563"
            />
          </TouchableOpacity>

          {/* Previous Step Button */}
          {currentStep > STEPS.SOURCE && (
            <TouchableOpacity
              onPress={handlePreviousStep}
              className="ml-4"
              accessibilityLabel="Go back to previous step"
            >
              <Icon
                name="arrow-left"
                type="font-awesome-5"
                size={20}
                color="#4B5563"
              />
            </TouchableOpacity>
          )}
        </View>

        <View className="flex-row items-center">
          <View className="bg-blue-100 rounded-full p-1 mr-2">
            <Image
              source={require("../assets/sanatana_logo.jpg")}
              style={{ width: 20, height: 20, borderRadius: 10 }}
            />
          </View>
          <Text className="text-base text-gray-600">
            Step {currentStep + 1} of {Object.keys(STEPS).length}
          </Text>
        </View>

        {/* Reset Button */}
        <TouchableOpacity
          onPress={() => {
            setShowResetConfirmation(true);
          }}
          className="px-2 py-1 bg-gray-100 rounded-md"
        >
          <Text className="text-sm text-gray-600">Start New Wizard</Text>
        </TouchableOpacity>
      </View>

      <View
        className="flex-1 relative"
        style={{ height: "calc(100% - 60px)", position: "relative", overflow: "auto", paddingBottom: 85 }}
      >
        {/* Background Logo */}
        <View
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            zIndex: -1,
            opacity: 0.05,
          }}
        >
          <AnimatedLogo
            size={300}
            backgroundColor="transparent"
            circle={false}
            animation="fade"
          />
        </View>

        {renderStep()}
      </View>

      {/* Close Confirmation Modal */}
      <ConfirmationModal
        visible={showCloseConfirmation}
        onClose={() => setShowCloseConfirmation(false)}
        message="Are you sure you want to close the wizard? All progress will be lost."
        onConfirm={() => {
          // Clear all progress before closing
          startNewWizard();
          onClose();
        }}
        onCancel={() => setShowCloseConfirmation(false)}
        confirmText="Yes"
        cancelText="No"
      />

      {/* Reset Confirmation Modal */}
      <ConfirmationModal
        visible={showResetConfirmation}
        onClose={() => setShowResetConfirmation(false)}
        message="Are you sure you want to Start New Wizard? All progress will be lost."
        onConfirm={startNewWizard}
        onCancel={() => setShowResetConfirmation(false)}
        confirmText="Yes"
        cancelText="No"
      />

      {/* Alert Modal */}
      <AlertModal
        visible={showAlert}
        onClose={() => {
          setShowAlert(false);
          // If it was a success alert, close the wizard after dismissing
          if (alertType === "success") {
            onClose();
          }
        }}
        message={alertMessage}
        type={alertType}
      />

      {/* Success Alert Modal with action buttons */}
      <SuccessAlertModal
        visible={showSuccessAlert}
        onClose={() => {
          setShowSuccessAlert(false);
          // Don't automatically close the wizard here
          // The user will choose whether to start a new wizard or go to job queue
        }}
        message={alertMessage}
        onStartNewWizard={() => {
          // Start a new wizard and close the wizard component
          startNewWizard();
          onClose();
        }}
      />
    </View>
  );
}

UploadWizard.propTypes = {
  onClose: PropTypes.func.isRequired,
};
