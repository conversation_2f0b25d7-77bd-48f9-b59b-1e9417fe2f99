import React from 'react';
import { View, TouchableOpacity, Dimensions, StyleSheet } from 'react-native';
import { Feather } from '@expo/vector-icons';

const ScrollControlsOverlay = ({ send_scroll_command }) => {
  const screenHeight = Dimensions.get('window').height;
  const scrollAmount = Math.round(screenHeight * 0.3); // 30% of screen height

  return (
    <View style={styles.overlay}>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          onPress={() => send_scroll_command(0, -scrollAmount)}
          style={styles.button}
        >
          <Feather name="chevron-up" size={40} color="black" />
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => send_scroll_command(0, scrollAmount)}
          style={styles.button}
        >
          <Feather name="chevron-down" size={40} color="black" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0, left: 0, right: 0, bottom: 0,
    zIndex: 10000,
    pointerEvents: 'box-none',
  },
  buttonContainer: {
    position: 'absolute',
    top: '40%',
    right: 10,
    justifyContent: 'space-between',
    height: 100,
  },
  button: {
    backgroundColor: 'transparent',
    padding: 10,
  },
});

export default ScrollControlsOverlay;
