import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Linking,
  ScrollView,
  useWindowDimensions,
} from "react-native";
import { MaterialIcons, FontAwesome } from "@expo/vector-icons";
import AuthorizationStepsModal from "./AuthorizationStepsModal";
import AuthorizationSuccessModal from "./AuthorizationSuccessModal";
import PropTypes from "prop-types";

const AuthorizedChannels = ({ authorizedUrls, onBackToWizard }) => {
  const [showAuthStepsModal, setShowAuthStepsModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const { width } = useWindowDimensions();
  const isDesktop = width > 768;
  
  if (!authorizedUrls || authorizedUrls.length === 0) {
    return (
      <View className="flex-1 justify-center items-center">
        {/* <Text className="text-gray-500">No authorized channels found.</Text> */}
      </View>
    );
  }

  return (
    <>
      <ScrollView className="">
        {authorizedUrls.map((auth, index) => (
          <View key={`auth-${index}`} className="mb-6 p-4 bg-white shadow rounded-lg">
            {/* Desktop: Project ID & Auth Button in the same row */}
            {isDesktop ? (
              <View className="flex-row items-center justify-between">
                <Text className="text-lg font-bold text-white bg-blue-400 px-3 py-2 rounded-t-lg">
                  Credentials: {auth.project_id}
                </Text>
                <View className="flex-row items-center">
                  <TouchableOpacity
                    className="bg-blue-600 py-2 px-4 rounded-lg flex-row items-center mr-2"
                    onPress={() => {
                      setShowAuthStepsModal(true);
                    }}
                  >
                    <FontAwesome name="play-circle" size={20} color="white" />
                    <Text className="text-white font-semibold ml-2">
                      Watch Authorization Steps
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    className="bg-blue-600 py-2 px-4 rounded-lg flex-row items-center"
                    onPress={() => {
                      Linking.openURL(auth.authorization_url);
                      // Show success modal after a delay (simulating successful authorization)
                      setTimeout(() => setShowSuccessModal(true), 1000);
                    }}
                  >
                    <MaterialIcons name="vpn-key" size={30} color="white" />
                    <Text className="text-xl text-white font-semibold ml-2">
                      Authorize New Channel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <>
                {/* Mobile: Project ID on top, then button below */}
                <Text className="text-lg font-bold text-white bg-blue-400 px-3 py-2 rounded-t-lg">
                  Credentials: {auth.project_id}
                </Text>
                {/* For mobile, stack the buttons vertically */}
                <TouchableOpacity
                  className="bg-blue-600 my-2 px-4 py-2 rounded-lg flex-row items-center justify-center self-end"
                  onPress={() => {
                    setShowAuthStepsModal(true);
                  }}
                >
                  <FontAwesome name="play-circle" size={20} color="white" />
                  <Text className="text-white font-semibold ml-2">
                    Watch Authorization Steps
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  className="bg-blue-600 my-2 px-4 py-2 rounded-lg flex-row items-center justify-center self-end"
                  onPress={() => {
                    Linking.openURL(auth.authorization_url);
                    // Show success modal after a delay (simulating successful authorization)
                    setTimeout(() => setShowSuccessModal(true), 1000);
                  }}
                >
                  <MaterialIcons name="vpn-key" size={30} color="white" />
                  <Text className="text-xl text-white font-semibold ml-2">
                    Authorize New Channel
                  </Text>
                </TouchableOpacity>
              </>
            )}

            {/* Available Uploads */}
            <View className="mt-4">
              <Text className="text-lg font-semibold text-gray-700">
                Available Uploads: {auth.available_uploads}
              </Text>
            </View>

            {/* Channels List */}
            {auth.channels.map((channel, idx) => (
              <View key={`channel-${idx}`} className="mt-3 bg-gray-100 rounded-lg">
                {isDesktop ? (
                  <View className="flex-row items-center justify-between">
                    {/* Left-aligned Channel Button */}
                    <TouchableOpacity
                      className="flex-row items-center p-3"
                      onPress={() => Linking.openURL(channel.channel_url)}
                    >
                      <MaterialIcons
                        name="play-circle-outline"
                        size={24}
                        color="#4B5563"
                      />
                      <Text className="text-gray-700 font-medium ml-2">
                        {channel.channel_title}
                      </Text>
                    </TouchableOpacity>

                    {/* Right-aligned Channel ID */}
                    <Text className="text-gray-500 p-3">
                      ID: {channel.channel_id}
                    </Text>
                  </View>
                ) : (
                  <>
                    {/* Mobile: Stack vertically */}
                    <TouchableOpacity
                      className="flex-row items-center p-3"
                      onPress={() => Linking.openURL(channel.channel_url)}
                    >
                      <MaterialIcons
                        name="play-circle-outline"
                        size={24}
                        color="#4B5563"
                      />
                      <Text className="text-gray-700 font-medium ml-2">
                        {channel.channel_title}
                      </Text>
                    </TouchableOpacity>
                    <Text className="text-gray-500 px-3 pb-3">
                      ID: {channel.channel_id}
                    </Text>
                  </>
                )}
              </View>
            ))}
          </View>
        ))}
      </ScrollView>
      
      {/* Authorization Steps Modal */}
      <AuthorizationStepsModal
        visible={showAuthStepsModal}
        onClose={() => setShowAuthStepsModal(false)}
        onAuthorized={() => {
          setShowAuthStepsModal(false);
          setTimeout(() => setShowSuccessModal(true), 500);
        }}
      />
      
      {/* Authorization Success Modal */}
      <AuthorizationSuccessModal
        visible={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        onBackToWizard={onBackToWizard}
      />
    </>
  );
};

// Add PropTypes validation
AuthorizedChannels.propTypes = {
  authorizedUrls: PropTypes.array,
  onBackToWizard: PropTypes.func
};

// Default props
AuthorizedChannels.defaultProps = {
  authorizedUrls: [],
  onBackToWizard: () => {}
};

export default AuthorizedChannels;
