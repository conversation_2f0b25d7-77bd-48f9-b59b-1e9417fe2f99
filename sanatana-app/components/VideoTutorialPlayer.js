import React, { useState } from "react";
import { ActivityIndicator, View } from "react-native";

const VideoTutorialPlayer = ({ youtubeUrl, loading = false }) => {
  const [isLoading, setIsLoading] = useState(loading);

  // Handle iframe load event
  const handleLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className="w-full h-full relative">
      {isLoading && (
        <View className="absolute inset-0 flex items-center justify-center z-10 bg-gray-800 bg-opacity-50">
          <ActivityIndicator size="large" color="#4F46E5" />
        </View>
      )}
      <iframe
        width="100%"
        height="100%"
        src={youtubeUrl}
        title="YouTube Video"
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        className="rounded-lg"
        onLoad={handleLoad}
      ></iframe>
    </div>
  );
};

export default VideoTutorialPlayer;
