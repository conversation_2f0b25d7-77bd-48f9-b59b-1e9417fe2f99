import React, { useState, useEffect } from "react";
import { Modal, Linking, View, Text, TouchableOpacity, useWindowDimensions, ActivityIndicator } from "react-native";
import { FontAwesome } from "@expo/vector-icons";
import VideoTutorialPlayer from "./VideoTutorialPlayer";
import { getTutorialUrl } from "../utils/tutorialUtils";

const AuthorizationStepsModal = ({ visible, onClose, onAuthorized }) => {
  const { width } = useWindowDimensions();
  const isDesktop = width > 768;

  const [selectedOption, setSelectedOption] = useState(isDesktop ? "desktop-video" : "mobile-video");
  const [loading, setLoading] = useState(true);

  // YouTube video URLs (fallbacks)
  const [desktopVideoUrl, setDesktopVideoUrl] = useState("https://www.youtube.com/embed/fzoVQ1WdXxI");
  const [mobileVideoUrl, setMobileVideoUrl] = useState("https://www.youtube.com/embed/c7tr3_eYnU8");

  // Fetch tutorial URLs when component becomes visible
  useEffect(() => {
    // Only fetch when modal is visible
    if (!visible) return;

    const fetchTutorialUrls = async () => {
      try {
        setLoading(true);
        console.log('AuthorizationStepsModal: Fetching fresh tutorial URLs');

        // Generate a timestamp to force fresh data
        const timestamp = Date.now();

        // Get desktop tutorial URL
        const desktopUrl = await getTutorialUrl(
          "Authorization Steps - Desktop",
          "https://www.youtube.com/watch?v=r4KYEgf1c2U",
          timestamp
        );
        setDesktopVideoUrl(desktopUrl);

        // Get mobile tutorial URL
        const mobileUrl = await getTutorialUrl(
          "Authorization Steps - Mobile",
          "https://www.youtube.com/shorts/U3V_Bc6Q8o0",
          timestamp
        );
        setMobileVideoUrl(mobileUrl);
      } catch (error) {
        console.error('Error fetching tutorial URLs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTutorialUrls();
  }, [visible]); // Re-fetch when visibility changes

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-90 justify-center items-center">
        <View className="flex-row justify-center space-x-4 mb-4 w-full">
          <TouchableOpacity
            key="desktop-video"
            onPress={() => setSelectedOption("desktop-video")}
            className={`px-3 py-1 rounded ${
              selectedOption === "desktop-video" ? "bg-blue-500" : "bg-gray-700"
            }`}
          >
            <Text className="text-white text-sm">Desktop Video</Text>
          </TouchableOpacity>
          <TouchableOpacity
            key="mobile-video"
            onPress={() => setSelectedOption("mobile-video")}
            className={`px-3 py-1 rounded ${
              selectedOption === "mobile-video" ? "bg-blue-500" : "bg-gray-700"
            }`}
          >
            <Text className="text-white text-sm">Mobile Video</Text>
          </TouchableOpacity>
        </View>

        {/* Content Area */}
        <View className="w-auto h-auto flex-grow flex items-center justify-center">
          <View className="w-auto h-auto bg-gray-800 rounded-lg">
            <VideoTutorialPlayer
              youtubeUrl={
                selectedOption === "desktop-video"
                  ? desktopVideoUrl
                  : mobileVideoUrl
              }
              loading={loading}
            />
          </View>
        </View>

        <View className="flex justify-center items-center p-5 space-y-6">
          {/* Expand Video to Full Screen Section */}
          <View className="bg-blue-500 p-4 rounded-lg shadow-lg w-full text-center flex flex-row items-center justify-center">
            <Text className="text-white text-sm">
              For better viewing, expand video to full screen.
              You can view on one device and follow on another.
            </Text>
          </View>

          {/* Authorization Tips */}
          <View className="bg-green-500 p-4 rounded-lg shadow-lg w-full">
            <Text className="text-white text-sm font-bold mb-2">Authorization Tips:</Text>
            <Text className="text-white text-sm">• Select the Google account you want to use</Text>
            <Text className="text-white text-sm">• Allow all required permissions</Text>
            <Text className="text-white text-sm">• You can authorize multiple channels from different accounts</Text>
          </View>
        </View>

        <View className="flex-row justify-center space-x-4 mb-4 w-full">
          {/* Close Button */}
          <TouchableOpacity onPress={onClose} className="">
            <FontAwesome name="close" size={24} color="red">
              {" "}
              Close Tutorial{" "}
            </FontAwesome>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default AuthorizationStepsModal;
