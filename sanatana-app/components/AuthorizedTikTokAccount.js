import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Linking,
  ScrollView,
  useWindowDimensions,
  Image,
} from "react-native";
import { MaterialIcons, FontAwesome } from "@expo/vector-icons";
import PropTypes from "prop-types";
import MagicApp from "./MagicApp";

const AuthorizedTikTokAccount = ({ authorizedAccounts, onCloseAuthSteps, onBackToWizard }) => {
  const [showMagicApp, setShowMagicApp] = useState(false);
  const [currentAccount, setCurrentAccount] = useState("new");
  const { width } = useWindowDimensions();
  const isDesktop = width > 768;

  if (!authorizedAccounts || authorizedAccounts.length === 0) {
    return (
      <View className="flex-1 justify-center items-center">
        {/* Empty state */}
      </View>
    );
  }

  const handleEnableLogin = (profileLink) => {
    setCurrentAccount(profileLink);
    setShowMagicApp(true);
  };

  return (
    <>
      <ScrollView className="">
        {authorizedAccounts.map((account, index) => (
          <View key={index} className="mb-6 p-4 bg-white shadow rounded-lg">
            {/* Account Header */}
            <View className="flex-row items-center mb-4">
              <Image
                source={{ uri: account.profile_pic }}
                style={{ width: 50, height: 50, borderRadius: 25 }}
              />
              <View className="ml-3">
                <Text className="text-lg font-bold">{account.username}</Text>
                <TouchableOpacity
                  onPress={() => Linking.openURL(account.profile_link)}
                  className="flex-row items-center"
                >
                  <FontAwesome name="music" size={16} color="#000000" /> {/* Using music icon as FontAwesome doesn't have a TikTok icon */}
                  <Text className="ml-2 text-blue-600">View Profile</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Login Status */}
            <View className="mt-2 flex-row items-center justify-between">
              <View className="flex-row items-center">
                <MaterialIcons
                  name={account.login_enabled ? "check-circle" : "error-outline"}
                  size={20}
                  color={account.login_enabled ? "green" : "gray"}
                />
                <Text className={`ml-2 ${account.login_enabled ? "text-green-700" : "text-gray-600"}`}>
                  {account.login_enabled ? "Login Enabled" : "Login Disabled"}
                </Text>
              </View>

              {!account.login_enabled && (
                <TouchableOpacity
                  className="bg-blue-600 py-2 px-4 rounded-lg"
                  onPress={() => handleEnableLogin(account.profile_link)}
                >
                  <Text className="text-white font-semibold">Enable Login</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        ))}
      </ScrollView>

      {/* MagicApp Modal */}
      {showMagicApp && (
        <MagicApp
          platform="tiktok"
          account={currentAccount}
          onClose={() => {
            setShowMagicApp(false);
            onCloseAuthSteps();
          }}
        />
      )}
    </>
  );
};

// Add PropTypes validation
AuthorizedTikTokAccount.propTypes = {
  authorizedAccounts: PropTypes.array,
  onCloseAuthSteps: PropTypes.func,
  onBackToWizard: PropTypes.func,
};

// Default props
AuthorizedTikTokAccount.defaultProps = {
  authorizedAccounts: [],
  onCloseAuthSteps: () => {},
  onBackToWizard: () => {},
};

export default AuthorizedTikTokAccount;
