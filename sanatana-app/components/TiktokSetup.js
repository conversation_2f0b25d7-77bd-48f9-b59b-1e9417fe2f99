import React, { useState, useEffect, useContext } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
} from "react-native";
import PropTypes from "prop-types";

import { FontAwesome } from "@expo/vector-icons";
import Constants from "expo-constants";
import { UserContext } from "../context/UserContext";
import AuthorizedTikTokAccount from "./AuthorizedTikTokAccount";
import MagicApp from "./MagicApp";
import TiktokTutorialModal from "./TiktokTutorialModal";

const TiktokSetup = ({ onBackToWizard }) => {
  const { user } = useContext(UserContext);
  const [authorizedAccounts, setAuthorizedAccounts] = useState([]);
  const [isTiktokTutorialVisible, setTiktokTutorialVisible] = useState(false);
  const [showMagicApp, setShowMagicApp] = useState(false);
  
  const { width } = useWindowDimensions();
  const isDesktop = width > 768;

  const fetchAuthAccounts = async () => {
    if (!user || !user.email) {
      console.log("TiktokSetup: No user email available");
      return;
    }

    try {
      console.log("TiktokSetup: Fetching authorization accounts for", user.email);
      const response = await fetch(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/tiktok/get_auth_acccounts`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ sanatana_email: user.email }),
        }
      );
      console.log("TiktokSetup: fetchAuthAccounts: response", response);
      
      // Since the endpoint is not yet implemented, handle potential errors
      if (!response.ok) {
        console.log("TiktokSetup: API endpoint not implemented yet or returned an error");
        // For testing, create fake data
        const fakeData = [
          {
            profile_pic: "https://p16-sign-va.tiktokcdn.com/tos-maliva-avt-0068/1727974373153eaef0f6f558bf217bc8~c5_100x100.jpeg",
            profile_link: "https://www.tiktok.com/@rushinarasima",
            login_enabled: false,
            username: "rushinarasima"
          }
        ];
        setAuthorizedAccounts(fakeData);
        return;
      }
      
      const data = await response.json();
      if (data.length === 0) {
        console.log("TiktokSetup: No authorized accounts found");
      } else {
        console.log("TiktokSetup: Found", data.length, "authorized accounts");
        setAuthorizedAccounts(data);
      }
    } catch (error) {
      console.error("Fetch error:", error);
      // For testing, create fake data
      const fakeData = [
        {
          profile_pic: "https://p16-sign-va.tiktokcdn.com/tos-maliva-avt-0068/1727974373153eaef0f6f558bf217bc8~c5_100x100.jpeg",
          profile_link: "https://www.tiktok.com/@rushinarasima",
          login_enabled: false,
          username: "rushinarasima"
        }
      ];
      setAuthorizedAccounts(fakeData);
    }
  };

  // Initial fetch on component mount
  useEffect(() => {
    fetchAuthAccounts();
  }, [user]);

  // Handler for when authorization steps are closed
  const handleCloseAuthSteps = () => {
    console.log("Close Auth Steps - Refreshing authorized accounts");
    // Refresh the authorized accounts list
    fetchAuthAccounts();
  };

  const openTiktokTutorialScreen = () => {
    setTiktokTutorialVisible(true);
    console.log("Open here TikTok Tutorial Screen");
  };

  return (
    <View className="my-4 rounded-lg">
      {/* <ScrollView
        contentContainerStyle={{ paddingBottom: 20 }}
        style={{ maxHeight: 500 }}
      >
        {authorizedAccounts.length > 0 ? (
          <AuthorizedTikTokAccount
            authorizedAccounts={authorizedAccounts}
            onCloseAuthSteps={handleCloseAuthSteps}
            onBackToWizard={onBackToWizard}
          />
        ) : (
          <View className="flex-row justify-between p-3 border-b border-gray-200">
            <Text className="flex-1 text-gray-600">No TikTok accounts found</Text>
          </View>
        )}
      </ScrollView>

      {authorizedAccounts.length === 0 ? (
        <Text className="text-[16px] font-bold mb-2 px-4">
          No TikTok Account yet? Don't miss out! Watch the video below on how to authorize a TikTok account.
        </Text>
      ) : (
        <Text className="text-[16px] font-bold mb-2 px-4">
          Maximize your reach on Tiktok! Authorize more TikTok accounts. One for each specialty. One for Business. One for entertainment. One for family. One for politics. And others for special groups.
        </Text>
      )} */}
      <View className="mb-4 px-4">
        <View className="flex-row items-center mb-2">
          {/* <Text className="text-base px-2">Authorize TikTok Account</Text> */}

          {isDesktop ? (
            <TouchableOpacity
              onPress={openTiktokTutorialScreen}
              className="flex-row items-center ml-2"
            >
              <FontAwesome name="play-circle" size={20} color="blue" />
              <Text className="text-blue-600 font-semibold ml-2">
                Watch How to Authorize New Social Media Accounts
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              onPress={openTiktokTutorialScreen}
              className="items-center ml-2"
            >
              <View className="flex-row items-center">
                <FontAwesome name="play-circle" size={20} color="blue" />
                <Text className="text-sm text-blue-600 font-semibold">Watch How to Authorize </Text>
              </View>
              <Text className="text-sm text-blue-600 ml-1 font-semibold">
               New Social Media Accounts
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <TouchableOpacity
          onPress={() => {
            // Open MagicApp component with platform="tiktok" and account="new"
            // This would typically be handled by navigation or a modal
            console.log("Opening MagicApp for new TikTok account");
            // Implementation would depend on how you want to show the MagicApp component
            // For now, we'll just show the tutorial modal
            setShowMagicApp(true);
          }}
          className="bg-green-600 py-2 px-4 rounded-lg flex-row items-center justify-center"
        >
          <FontAwesome name="plus-circle" size={20} color="white" />
          <Text className="text-white font-semibold ml-2">
            Authorize New Social Media Accounts
          </Text>
        </TouchableOpacity>
      </View>
     {/* Fullscreen Modal */}
     <TiktokTutorialModal
        visible={isTiktokTutorialVisible}
        onClose={() => setTiktokTutorialVisible(false)}
      />
      {/* MagicApp Modal */}
      {showMagicApp && (
        <MagicApp
          platform="tiktok"
          account="new"
          onClose={() => {
            setShowMagicApp(false);
           
          }}
        />
      )}    
    </View>
  );
};

// Add PropTypes validation
TiktokSetup.propTypes = {
  onBackToWizard: PropTypes.func,
};

// Default props
TiktokSetup.defaultProps = {
  onBackToWizard: () => {},
};

export default TiktokSetup;
