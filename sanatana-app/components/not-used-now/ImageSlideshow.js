import React, { useState } from 'react';
import { View, Image, TouchableOpacity, Dimensions, Text, Linking } from 'react-native';

export const ImageSlideshow = ({ images, onClose }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;

  console.log('ImageSlideshow render:', { images, screenWidth, screenHeight });

  if (!images.length) {
    return <Text className="text-center text-red-500">No images found</Text>;
  }

  const nextImage = () => setCurrentIndex((prev) => (prev + 1) % images.length);
  const prevImage = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);

  return (
    <View className="absolute inset-0 z-50 bg-black/90">
      {/* Close Button - Top Right */}
      <View className="absolute right-4 top-4 z-50">
        <TouchableOpacity 
          className="bg-red-500 p-4 rounded-full"
          onPress={onClose}
        >
          <Text className="text-white text-2xl font-bold">×</Text>
        </TouchableOpacity>
      </View>

      {/* Previous Button - Left Side */}
      <View className="absolute left-4 top-1/2 -translate-y-1/2 z-50">
        <TouchableOpacity 
          className="bg-blue-600 p-4 rounded-full"
          onPress={prevImage}
        >
          <Text className="text-white text-xl font-bold">←</Text>
        </TouchableOpacity>
      </View>

      {/* Next Button - Right Side */}
      <View className="absolute right-4 top-1/2 -translate-y-1/2 z-50">
        <TouchableOpacity 
          className="bg-blue-600 p-4 rounded-full"
          onPress={nextImage}
        >
          <Text className="text-white text-xl font-bold">→</Text>
        </TouchableOpacity>
      </View>

      {/* Image Container */}
      <View className="flex-1 justify-center items-center p-4">
        <Image
          style={{
            width: screenWidth - 32,
            height: screenHeight - 200,
          }}
          source={{ uri: images[currentIndex].url }}
          resizeMode="contain"
        />
        <Text className="text-white mt-2">Debug: Image URL - {images[currentIndex].url}</Text>
      </View>

      {/* Image Counter - Bottom Center */}
      <View className="absolute bottom-20 left-0 right-0">
        <Text className="text-center text-white text-xl">
          {currentIndex + 1}/{images.length}
        </Text>
      </View>

      {/* Google Console Link - Bottom Center */}
      <View className="absolute bottom-4 left-0 right-0 flex items-center">
        <TouchableOpacity
          className="bg-green-600 px-6 py-3 rounded-lg"
          onPress={() => Linking.openURL('http://console.cloud.google.com')}
        >
          <Text className="text-white text-lg font-bold">Go to Google Console</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};
