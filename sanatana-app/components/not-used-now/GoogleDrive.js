import { useState } from 'react';
import Constants from "expo-constants";

const fetchGoogleDriveImages = async (folderId) => {
    try {
      // Validate input
      if (!folderId || typeof folderId !== 'string') {
        throw new Error('Invalid folder ID');
      }
  
      // API Endpoint
      const url = `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/gfolder_list_files?folder_id=${folderId}`;
  
      // Fetch folder contents from API
      const response = await fetch(url);
  
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
  
      // Convert response to JSON
      const data = await response.json();
  
      // Process the response and format the URL
      const files = data.map(file => ({
        id: file.id,
        name: file.name,
        url: `https://lh3.googleusercontent.com/d/${file.id}`
        // url: `https://drive.google.com/uc?id=${file.id}`
      }));
  
      console.log(files);
      return files;
    } catch (error) {
      console.error('Google Drive API error:', error);
      return [];
    }
  };
  

export { fetchGoogleDriveImages };
