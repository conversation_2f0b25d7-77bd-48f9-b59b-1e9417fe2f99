import React, { useState } from "react";
import { View, Image, TouchableOpacity } from "react-native";
import { FontAwesome } from "@expo/vector-icons"; // Using Expo vector icons

const SlideShowTutorial = ({ images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextImage = () => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const prevImage = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  return (
    <View className="w-full h-full flex justify-center items-center">
      <Image
        source={{ uri: images[currentIndex] }}
        style={{ width: "100%", height: "80%", resizeMode: "contain" }}
      />
      <View className="flex-row mt-4">
        <TouchableOpacity onPress={prevImage} className="px-4 py-2 bg-gray-700 rounded-full mr-2">
          <FontAwesome name="arrow-left" size={24} color="white" />
        </TouchableOpacity>
        <TouchableOpacity onPress={nextImage} className="px-4 py-2 bg-gray-700 rounded-full ml-2">
          <FontAwesome name="arrow-right" size={24} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default SlideShowTutorial;
