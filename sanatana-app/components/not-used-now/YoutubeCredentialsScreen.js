import React, { useState, useEffect } from 'react';
import { View, Text, Button, Dimensions, ScrollView } from 'react-native';
import { ImageSlideshow } from './ImageSlideshow';
import { fetchGoogleDriveImages } from './GoogleDrive';

const MOBILE_FOLDER_ID = "1GQgVxmOpwQ3CcLWsuQLHGi1YFU-snWQx";
const DESKTOP_FOLDER_ID = "1q698FoB4uYksqwUJQNA5Dl8UTdUaoICF";

const YoutubeCredentialsScreen = ({messageForCredentials}) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const [images, setImages] = useState([]);
  const [showSlideshow, setShowSlideshow] = useState(false);
  const isMobile = Dimensions.get("window").width < 768;

  useEffect(() => {
    if (selectedOption) {
      const folderId = selectedOption === "mobile" ? MOBILE_FOLDER_ID : DESKTOP_FOLDER_ID;
      fetchGoogleDriveImages(folderId).then(images => {
        console.log('Fetched images:', images);
        setImages(images);
        setShowSlideshow(true);
      });
    }
  }, [selectedOption]);

  const handleCloseSlideshow = () => {
    setShowSlideshow(false);
    setSelectedOption(null);
  };

  return (
    <View className="flex-1">
      <ScrollView className="flex items-center p-5">
        <Text className="text-xl font-bold mb-5 text-center">
          {messageForCredentials === 1
            ? "🚀 No YouTube credentials uploaded! Select mobile or desktop setup."
            : "🎯 Upload more credentials from different Google accounts to boost uploads!"}
        </Text>

        <View className="w-full flex-row justify-center mb-4">
          {isMobile ? (
            <Button title="Mobile Screenshots" onPress={() => setSelectedOption("mobile")} />
          ) : (
            <Button title="Desktop Screenshots" onPress={() => setSelectedOption("desktop")} />
          )}
        </View>
      </ScrollView>

      {showSlideshow && images.length > 0 && (
        <ImageSlideshow
          images={images}
          onClose={handleCloseSlideshow}
        />
      )}
    </View>
  );
};

export default YoutubeCredentialsScreen;
