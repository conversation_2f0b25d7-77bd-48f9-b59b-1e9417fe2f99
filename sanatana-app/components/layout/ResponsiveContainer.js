import React from 'react';
import { View, Dimensions } from 'react-native';
import PropTypes from 'prop-types';

/**
 * A responsive container that limits width on desktop screens
 * and centers content horizontally
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} props.style - Additional styles to apply to the container
 * @param {string} props.className - Additional Tailwind classes to apply
 */
const ResponsiveContainer = ({ children, style, className = '' }) => {
  const { width } = Dimensions.get('window');
  const isMobile = width < 768;
  
  // On mobile, use full width
  // On desktop, limit width to 50% and center horizontally
  const containerStyle = isMobile
    ? { width: '100%' }
    : { 
        width: '50%', 
        alignSelf: 'center',
        maxWidth: 768, // Maximum width for very large screens
      };
  
  return (
    <View 
      style={[containerStyle, style]} 
      className={`${className}`}
    >
      {children}
    </View>
  );
};

ResponsiveContainer.propTypes = {
  children: PropTypes.node.isRequired,
  style: PropTypes.object,
  className: PropTypes.string
};

export default ResponsiveContainer;
