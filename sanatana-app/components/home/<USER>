import React, { useState, useEffect, useRef } from "react";
import { View, Text, Dimensions, TouchableOpacity, Animated } from "react-native";
import Animated<PERSON>ogo from "../common/AnimatedLogo";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";

/**
 * A card component that displays beta announcement information with two columns
 *
 * @param {Object} props - Component props
 * @param {string} props.title - The title to display
 */
const BetaAnnouncementCard = ({
  title = "Sanatana Media Beta"
}) => {
  const { width } = Dimensions.get("window");
  const isMobile = width < 768;
  const [activeSlide, setActiveSlide] = useState(0);
  const slideAnim = useRef(new Animated.Value(0)).current;

  // Define the slides
  const slides = [
    {
      title: "Welcome to the Beta!",
      content: "You're part of our exclusive beta program! Enjoy lifetime free services and huge discounts on future premium features.",
      bgColor: "bg-indigo-100",
      textColor: "text-indigo-700"
    },
    {
      title: "Your Beta Benefits",
      content: (
        <View>
          <View className="flex-row items-start mb-1">
            <Icon
              name="check-circle"
              type="font-awesome-5"
              size={12}
              color="#10B981"
              style={{ marginRight: 4, marginTop: 1 }}
            />
            <Text className="text-gray-700 flex-1" style={{ fontSize: 11 }}>Lifetime free access to core features</Text>
          </View>

          <View className="flex-row items-start mb-1">
            <Icon
              name="check-circle"
              type="font-awesome-5"
              size={12}
              color="#10B981"
              style={{ marginRight: 4, marginTop: 1 }}
            />
            <Text className="text-gray-700 flex-1" style={{ fontSize: 11 }}>50% discount on future premium plans</Text>
          </View>

          <View className="flex-row items-start">
            <Icon
              name="check-circle"
              type="font-awesome-5"
              size={12}
              color="#10B981"
              style={{ marginRight: 4, marginTop: 1 }}
            />
            <Text className="text-gray-700 flex-1" style={{ fontSize: 11 }}>Early access to new features</Text>
          </View>
        </View>
      ),
      bgColor: "bg-white",
      textColor: "text-gray-700"
    },
    {
      title: "Coming Soon",
      content: "Stay tuned for more exciting features and updates as we continue to improve Sanatana Media!",
      bgColor: "bg-purple-100",
      textColor: "text-purple-700"
    }
  ];

  // Auto-advance slides every 2 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      const nextSlide = (activeSlide + 1) % slides.length;
      animateToSlide(nextSlide);
    }, 2000);

    return () => clearInterval(interval);
  }, [activeSlide]);

  // Animate to the selected slide
  const animateToSlide = (index) => {
    Animated.timing(slideAnim, {
      toValue: index,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      setActiveSlide(index);
    });
  };

  return (
    <View className={`bg-indigo-50 ${isMobile ? 'p-2 mb-2' : 'p-6 mb-6'} rounded-2xl shadow-lg border border-indigo-200`}>
      {/* Header with logo and title - more compact on mobile */}
      <View className="flex-row items-center justify-center mb-2">
        <AnimatedLogo
          size={isMobile ? 30 : 50}
          animation="hueShift"
          backgroundColor="transparent"
          style={{ marginRight: 8 }}
        />
        <View>
          <View className="flex-row items-center">
            <Icon
              name="star"
              type="font-awesome"
              size={isMobile ? 12 : 16}
              color="#4F46E5"
              style={{ marginRight: 4 }}
            />
            <Text className={`${isMobile ? 'text-base' : 'text-xl'} font-bold text-indigo-700`}>
              {title}
            </Text>
          </View>
          <View className="bg-indigo-600 px-2 py-0.5 rounded-full self-start mt-1">
            <Text className="text-white text-xs font-medium">EARLY ACCESS</Text>
          </View>
        </View>
      </View>

      {/* Content area - Carousel on mobile, two columns on desktop */}
      {isMobile ? (
        // Mobile: Carousel - Extremely compact
        <View style={{ height: 120 }}> {/* Absolute minimum height */}
          {/* Current slide - Fixed height container */}
          <View
            className={`${slides[activeSlide].bgColor} p-3 rounded-lg mb-1`}
            style={{
              height: 90, // Absolute minimum height
              overflow: 'hidden'
            }}
          >
            <Text className="text-gray-800 font-medium mb-1 text-sm">{slides[activeSlide].title}</Text>
            <View style={{ flex: 1 }}>
              {typeof slides[activeSlide].content === 'string' ? (
                <Text className={slides[activeSlide].textColor} style={{ lineHeight: 18, fontSize: 12 }}>
                  {slides[activeSlide].content}
                </Text>
              ) : (
                slides[activeSlide].content
              )}
            </View>
          </View>

          {/* Dots indicator - more compact */}
          <View className="flex-row justify-center mt-1">
            {slides.map((slide, index) => (
              <TouchableOpacity
                key={`slide-dot-${slide.title}`}
                onPress={() => animateToSlide(index)}
                style={{ padding: 2 }}
              >
                <View
                  className={`rounded-full ${index === activeSlide ? 'bg-indigo-600' : 'bg-gray-300'}`}
                  style={{ width: 6, height: 6, marginHorizontal: 2 }}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>
      ) : (
        // Desktop: Two-column layout
        <View className="flex-row">
          {/* Left column - Message */}
          <View className="bg-indigo-100 p-4 rounded-lg mr-4" style={{ flex: 1 }}>
            <Text className="text-indigo-800 text-lg font-medium mb-2">Welcome to the Beta!</Text>
            <Text className="text-indigo-700" style={{ lineHeight: 24 }}>
              You're part of our exclusive beta program! Enjoy lifetime free services and huge discounts on future premium features.
            </Text>
          </View>

          {/* Right column - Benefits */}
          <View className="bg-white p-4 rounded-lg" style={{ flex: 1 }}>
            <Text className="text-gray-800 font-medium mb-3">Your Beta Benefits:</Text>

            <View className="flex-row items-start mb-3">
              <Icon
                name="check-circle"
                type="font-awesome-5"
                size={16}
                color="#10B981"
                style={{ marginRight: 8, marginTop: 2 }}
              />
              <Text className="text-gray-700 flex-1">Lifetime free access to core features</Text>
            </View>

            <View className="flex-row items-start mb-3">
              <Icon
                name="check-circle"
                type="font-awesome-5"
                size={16}
                color="#10B981"
                style={{ marginRight: 8, marginTop: 2 }}
              />
              <Text className="text-gray-700 flex-1">50% discount on future premium plans</Text>
            </View>

            <View className="flex-row items-start">
              <Icon
                name="check-circle"
                type="font-awesome-5"
                size={16}
                color="#10B981"
                style={{ marginRight: 8, marginTop: 2 }}
              />
              <Text className="text-gray-700 flex-1">Early access to new features</Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

BetaAnnouncementCard.propTypes = {
  title: PropTypes.string
};

export default BetaAnnouncementCard;
