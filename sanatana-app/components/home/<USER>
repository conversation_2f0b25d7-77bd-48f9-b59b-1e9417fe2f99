import React, { useState, useEffect } from "react";
import { View, Text, Dimensions, ActivityIndicator } from "react-native";
import VideoTutorialPlayer from "../VideoTutorialPlayer";
import { getTutorialUrl } from "../../utils/tutorialUtils";

/**
 * A card component that displays a YouTube tutorial video
 *
 * @param {Object} props - Component props
 * @param {string} props.youtubeUrl - The YouTube video URL to embed
 * @param {string} props.title - The title to display above the video
 * @param {string} props.caption - The caption to display below the video
 */
const YoutubeTutorialCard = ({
  youtubeUrl = "https://www.youtube.com/embed/irhhMLKDBZ8", // Default tutorial video
  title = "How to Use Sanatana Media",
  caption = "Learn how to dominate social media with Sanatana Media's powerful automation tools",
  tutorialName = "How to Use Sanatana Media"
}) => {
  const { width } = Dimensions.get("window");
  const isMobile = width < 768;
  const [loading, setLoading] = useState(true);
  const [videoUrl, setVideoUrl] = useState(youtubeUrl);

  // Fetch the tutorial URL when component mounts or when the component is re-rendered
  useEffect(() => {
    // Generate a timestamp to force fresh data
    const timestamp = Date.now();

    const fetchTutorialUrl = async () => {
      try {
        setLoading(true);
        console.log(`YoutubeTutorialCard: Fetching fresh URL for ${tutorialName} at ${timestamp}`);

        // Pass the timestamp to ensure we get a fresh URL
        const url = await getTutorialUrl(tutorialName, youtubeUrl, timestamp);
        setVideoUrl(url);
      } catch (error) {
        console.error(`Error fetching tutorial URL for ${tutorialName}:`, error);
        // Fall back to the provided URL
        setVideoUrl(youtubeUrl);
      } finally {
        setLoading(false);
      }
    };

    fetchTutorialUrl();

    // Add a cleanup function to cancel any pending requests
    return () => {
      console.log(`YoutubeTutorialCard: Cleaning up for ${tutorialName}`);
    };
  }, [tutorialName, youtubeUrl]);

  // Calculate responsive height based on screen width
  const videoHeight = isMobile ? 200 : 300;

  return (
    <View className="bg-white p-6 rounded-2xl shadow-lg mb-6">
      <Text className="text-xl font-bold text-gray-800 mb-3 text-center">
        {title}
      </Text>

      <View
        className="w-full rounded-lg overflow-hidden mb-4"
        style={{ height: videoHeight }}
      >
        <VideoTutorialPlayer youtubeUrl={videoUrl} loading={loading} />
      </View>

      <Text className="text-gray-600 text-center">
        {caption}
      </Text>
    </View>
  );
};

export default YoutubeTutorialCard;
