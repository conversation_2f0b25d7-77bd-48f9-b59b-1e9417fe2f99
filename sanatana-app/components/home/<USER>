import React, { useState } from "react";
import { View, Text, TouchableOpacity, Dimensions } from "react-native";
import { Icon } from "@rneui/themed";

import VideoTutorialModal from "./VideoTutorialModal";

/**
 * A button component that opens a YouTube tutorial video modal
 */
const TutorialButton = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const { width } = Dimensions.get("window");
  const isMobile = width < 768;

  return (
    <>
      <TouchableOpacity
        onPress={() => setModalVisible(true)}
        className="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 rounded-xl shadow-md flex-row items-center justify-between mb-6"
        style={{
          width: "100%"
        }}
      >
        <View className="flex-row items-center">
          {/* YouTube Icon */}
          <View className="bg-red-600 rounded-full p-2 mr-3">
            <Icon
              name="youtube"
              type="font-awesome-5"
              color="white"
              size={isMobile ? 20 : 24}
              solid
            />
          </View>

          {/* Button Text */}
          <Text className="text-white font-bold" style={{ fontSize: isMobile ? 14 : 16 }}>
            Dominate Social Media
          </Text>
        </View>
      </TouchableOpacity>

      {/* Video Tutorial Modal */}
      <VideoTutorialModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default TutorialButton;
