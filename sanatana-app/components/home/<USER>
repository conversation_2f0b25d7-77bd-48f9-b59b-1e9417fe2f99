import React, { useEffect, useRef } from "react";
import { View, Text, Animated, Easing } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";

/**
 * A promotional message that slides in and fades out automatically
 *
 * @param {Object} props - Component props
 * @param {string} props.message - The message to display
 * @param {number} props.duration - How long the message stays visible in ms
 * @param {number} props.delay - Delay before the message appears in ms
 */
const PromoMessage = ({
  message = "Beta users get lifetime free services and huge discounts on future premium features!",
  duration = 5000,
  delay = 1000
}) => {
  // Animation values
  const translateY = useRef(new Animated.Value(-50)).current; // Start from above the screen
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Sequence of animations
    const slideIn = Animated.timing(translateY, {
      toValue: 0,
      duration: 800,
      easing: Easing.out(Easing.back(1.5)),
      useNativeDriver: true,
    });

    const fadeIn = Animated.timing(opacity, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    });

    const wait = Animated.delay(duration);

    const fadeOut = Animated.timing(opacity, {
      toValue: 0,
      duration: 800,
      useNativeDriver: true,
    });

    // Start the animation sequence after delay
    setTimeout(() => {
      Animated.parallel([slideIn, fadeIn]).start(() => {
        wait.start(() => {
          fadeOut.start();
        });
      });
    }, delay);

    // Cleanup
    return () => {
      slideIn.stop();
      fadeIn.stop();
      wait.stop();
      fadeOut.stop();
    };
  }, [translateY, opacity, duration, delay]);

  return (
    <Animated.View
      style={{
        transform: [{ translateY }],
        opacity,
        position: "absolute",
        top: 10, // Position at the top
        left: 20,
        right: 20,
        zIndex: 100,
        elevation: 5, // For Android
        shadowColor: '#000', // For iOS
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 3,
      }}
    >
      <View className="bg-gradient-to-r from-indigo-600 to-purple-600 px-4 py-3 rounded-lg shadow-lg flex-row items-center">
        <Icon
          name="gift"
          type="font-awesome-5"
          size={20}
          color="#FFFFFF"
          style={{ marginRight: 10 }}
        />
        <Text className="text-white flex-1">{message}</Text>
      </View>
    </Animated.View>
  );
};

PromoMessage.propTypes = {
  message: PropTypes.string,
  duration: PropTypes.number,
  delay: PropTypes.number
};

export default PromoMessage;
