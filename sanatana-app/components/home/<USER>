import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, Modal, Dimensions, ActivityIndicator } from "react-native";
import { Icon } from "@rneui/themed";
import VideoTutorialPlayer from "../VideoTutorialPlayer";
import Animated<PERSON>ogo from "../common/AnimatedLogo";
import PropTypes from "prop-types";
import { getTutorialUrl } from "../../utils/tutorialUtils";

/**
 * A modal component that displays a YouTube tutorial video
 *
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {Function} props.onClose - Function to call when the modal is closed
 */
const VideoTutorialModal = ({
  visible = false,
  onClose = () => {}
}) => {
  const { width, height } = Dimensions.get("window");
  const isMobile = width < 768;
  const [loading, setLoading] = useState(true);

  // Different YouTube URLs for mobile and desktop (fallbacks)
  const [mobileYoutubeUrl, setMobileYoutubeUrl] = useState("https://www.youtube.com/watch?v=B-ZvG93utgA");
  const [desktopYoutubeUrl, setDesktopYoutubeUrl] = useState("https://www.youtube.com/embed/irhhMLKDBZ8");

  // Fetch tutorial URLs when component becomes visible
  useEffect(() => {
    // Only fetch when modal is visible
    if (!visible) return;

    const fetchTutorialUrls = async () => {
      try {
        setLoading(true);
        console.log('VideoTutorialModal: Fetching fresh tutorial URLs');

        // Generate a timestamp to force fresh data
        const timestamp = Date.now();

        // Get desktop tutorial URL
        const desktopUrl = await getTutorialUrl(
          "How to Use Sanatana Media - Desktop",
          "https://www.youtube.com/embed/irhhMLKDBZ8",
          timestamp
        );
        setDesktopYoutubeUrl(desktopUrl);

        // Get mobile tutorial URL
        const mobileUrl = await getTutorialUrl(
          "How to Use Sanatana Media - Mobile",
          "https://www.youtube.com/watch?v=B-ZvG93utgA",
          timestamp
        );
        setMobileYoutubeUrl(mobileUrl);
      } catch (error) {
        console.error('Error fetching tutorial URLs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTutorialUrls();
  }, [visible]); // Re-fetch when visibility changes

  // Calculate responsive dimensions
  const modalWidth = isMobile ? width * 0.95 : width * 0.8;
  const modalHeight = isMobile ? height * 0.7 : height * 0.8;
  const videoHeight = isMobile ? modalHeight * 0.6 : modalHeight * 0.7;

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "rgba(0, 0, 0, 0.7)"
        }}
      >
        <View
          style={{
            width: modalWidth,
            maxHeight: modalHeight,
            backgroundColor: "white",
            borderRadius: 16,
            overflow: "hidden",
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            elevation: 5
          }}
        >
          {/* Header */}
          <View className="bg-indigo-700 p-4 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <AnimatedLogo
                size={30}
                animation="pulse"
                backgroundColor="transparent"
                style={{ marginRight: 12 }}
              />
              <Text className="text-white text-xl font-bold">
                Sanatana Media Tutorial
              </Text>
            </View>
            <TouchableOpacity onPress={onClose}>
              <Icon name="times" type="font-awesome-5" color="white" size={20} />
            </TouchableOpacity>
          </View>

          {/* Video Player */}
          <View
            style={{
              height: videoHeight,
              width: "100%"
            }}
          >
            <VideoTutorialPlayer
              youtubeUrl={isMobile ? mobileYoutubeUrl : desktopYoutubeUrl}
              loading={loading}
            />
          </View>

          {/* Footer */}
          <View className="p-4">
            <Text className="text-gray-700 mb-4">
              For the best experience, we recommend viewing this tutorial in full screen mode.
            </Text>

            <TouchableOpacity
              onPress={onClose}
              className="self-center"
            >
              <Text className="text-red-600 font-medium text-center">
                Close Tutorial
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

VideoTutorialModal.propTypes = {
  visible: PropTypes.bool,
  onClose: PropTypes.func
};

export default VideoTutorialModal;
