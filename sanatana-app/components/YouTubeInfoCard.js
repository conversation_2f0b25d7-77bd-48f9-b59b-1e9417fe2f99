import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, ScrollView } from 'react-native';
import { Icon } from '@rneui/themed';
import PropTypes from 'prop-types';



const YouTubeInfoCard = ({ videoInfo }) => {
  const [expanded, setExpanded] = useState(false);

  if (!videoInfo) {
    return null;
  }

  // Format duration from seconds to readable format
  const formatDuration = (seconds) => {
    if (!seconds) return 'Unknown';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Format view count with commas
  const formatNumber = (num) => {
    return num ? num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") : '0';
  };

  // Format date from YYYYMMDD to readable format
  const formatDate = (dateStr) => {
    if (!dateStr || dateStr.length !== 8) return 'Unknown';

    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);

    const date = new Date(year, month - 1, day);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Check if video is longer than 1 hour
  const isLongVideo = videoInfo.duration > 3600;

  return (
    <View className="bg-gray-50 rounded-lg border border-gray-200 my-2.5 overflow-hidden w-full">
      <View className="flex-row justify-between items-center p-3 border-b border-gray-200 bg-gray-100">
        <Text className="text-base font-semibold text-gray-700">YouTube Video Information</Text>
        <TouchableOpacity
          onPress={() => setExpanded(!expanded)}
          className="p-1"
        >
          <Icon
            name={expanded ? 'chevron-up' : 'chevron-down'}
            type="font-awesome"
            size={16}
            color="#4B5563"
          />
        </TouchableOpacity>
      </View>

      {/* Thumbnail and basic info */}
      <View className="relative w-full h-[180px]">
        {videoInfo.thumbnail && (
          <Image
            source={{ uri: videoInfo.thumbnail }}
            className="w-full h-full"
            resizeMode="cover"
          />
        )}
        <View className="absolute bottom-2 right-2 bg-black bg-opacity-70 px-1.5 py-0.5 rounded">
          <Text className="text-white text-xs font-medium">
            {videoInfo.duration_string || formatDuration(videoInfo.duration)}
          </Text>
        </View>
      </View>

      {/* Warning for long videos */}
      {isLongVideo && (
        <View className="flex-row items-center bg-red-50 border border-red-200 rounded m-2 p-2">
          <Icon
            name="exclamation-triangle"
            type="font-awesome"
            size={16}
            color="#B91C1C"
          />
          <Text className="text-red-700 text-xs ml-1.5 flex-1">
            This video exceeds the 1 hour maximum duration limit
          </Text>
        </View>
      )}

      {/* Basic info */}
      <View className="p-3">
        <View className="flex-row mb-1.5 flex-wrap">
          <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Channel:</Text>
          <Text className="text-sm text-gray-800 flex-1" numberOfLines={1}>
            {videoInfo.channel || 'Unknown'}
          </Text>
        </View>
        <View className="flex-row mb-1.5 flex-wrap">
          <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Views:</Text>
          <Text className="text-sm text-gray-800 flex-1">
            {formatNumber(videoInfo.view_count)}
          </Text>
        </View>
        <View className="flex-row mb-1.5 flex-wrap">
          <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Uploaded:</Text>
          <Text className="text-sm text-gray-800 flex-1">
            {formatDate(videoInfo.upload_date)}
          </Text>
        </View>
      </View>

      {/* Expanded content */}
      {expanded && (
        <ScrollView className="max-h-[300px]">
          {/* Video details section */}
          <View className="p-3 border-t border-gray-200">
            <Text className="text-[15px] font-semibold text-gray-700 mb-2">Video Details</Text>
            <View className="flex-row mb-1.5 flex-wrap">
              <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Resolution:</Text>
              <Text className="text-sm text-gray-800 flex-1">
                {videoInfo.width && videoInfo.height
                  ? `${videoInfo.width}x${videoInfo.height}`
                  : 'Unknown'}
              </Text>
            </View>
            <View className="flex-row mb-1.5 flex-wrap">
              <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Privacy:</Text>
              <Text className="text-sm text-gray-800 flex-1">
                {videoInfo.privacy_status || 'Unknown'}
              </Text>
            </View>
            {videoInfo.age_limit > 0 && (
              <View className="flex-row mb-1.5 flex-wrap">
                <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Age Limit:</Text>
                <Text className="text-sm text-gray-800 flex-1">{videoInfo.age_limit}+</Text>
              </View>
            )}
          </View>

          {/* Stats section */}
          <View className="p-3 border-t border-gray-200">
            <Text className="text-[15px] font-semibold text-gray-700 mb-2">Stats</Text>
            <View className="flex-row mb-1.5 flex-wrap">
              <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Likes:</Text>
              <Text className="text-sm text-gray-800 flex-1">
                {formatNumber(videoInfo.like_count)}
              </Text>
            </View>
            <View className="flex-row mb-1.5 flex-wrap">
              <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Comments:</Text>
              <Text className="text-sm text-gray-800 flex-1">
                {formatNumber(videoInfo.comment_count)}
              </Text>
            </View>
          </View>

          {/* Tags and categories */}
          {(videoInfo.tags?.length > 0 || videoInfo.categories?.length > 0 || videoInfo.hashtags?.length > 0) && (
            <View className="p-3 border-t border-gray-200">
              <Text className="text-[15px] font-semibold text-gray-700 mb-2">Tags & Categories</Text>

              {videoInfo.categories?.length > 0 && (
                <View className="flex-row mb-1.5 flex-wrap">
                  <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Categories:</Text>
                  <Text className="text-sm text-gray-800 flex-1">
                    {videoInfo.categories.join(', ')}
                  </Text>
                </View>
              )}

              {videoInfo.hashtags?.length > 0 && (
                <View className="mt-2">
                  <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Hashtags:</Text>
                  <View className="flex-row flex-wrap mt-1">
                    {videoInfo.hashtags.map((tag, i) => (
                      <View key={`hashtag-${i}-${tag}`} className="bg-gray-200 rounded-full px-2 py-1 m-0.5">
                        <Text className="text-xs text-gray-600">{tag}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {videoInfo.tags?.length > 0 && (
                <View className="mt-2">
                  <Text className="text-sm font-medium text-gray-600 mr-1.5 min-w-[70px]">Tags:</Text>
                  <View className="flex-row flex-wrap mt-1">
                    {videoInfo.tags.slice(0, 10).map((tag, i) => (
                      <View key={`tag-${i}-${tag}`} className="bg-gray-200 rounded-full px-2 py-1 m-0.5">
                        <Text className="text-xs text-gray-600">{tag}</Text>
                      </View>
                    ))}
                    {videoInfo.tags.length > 10 && (
                      <Text className="text-xs text-gray-500 ml-1 self-center">+{videoInfo.tags.length - 10} more</Text>
                    )}
                  </View>
                </View>
              )}
            </View>
          )}
        </ScrollView>
      )}
    </View>
  );
};



YouTubeInfoCard.propTypes = {
  videoInfo: PropTypes.object
};

export default YouTubeInfoCard;
