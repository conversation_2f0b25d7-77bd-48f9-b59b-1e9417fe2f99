import React, { useState, useRef } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { FontAwesome5, MaterialIcons, Ionicons, Entypo, AntDesign } from '@expo/vector-icons';
import WatermarkLogo from './common/WatermarkLogo';

const categories = [
  {
    icon: <FontAwesome5 name="user-astronaut" size={30} color="#4f46e5" />,
    title: 'Influencers & Creators',
    description: '✔️ Share once, appear on all platforms\n✔️ Focus on content, not uploads\n✔️ Grow faster with less effort\n✔️ Earn more via brand deals & ads'
  },
  {
    icon: <Ionicons name="rocket-outline" size={30} color="#10b981" />,
    title: 'Aspiring Creators',
    description: '✔️ Kickstart your social media presence\n✔️ Build audience even by sharing others’ content\n✔️ Appear active everywhere from day one\n✔️ Learn and grow without burnout'
  },
  {
    icon: <MaterialIcons name="school" size={30} color="#f59e0b" />,
    title: 'Students & Educators',
    description: '✔️ Share lectures and tutorials\n✔️ Build an academic brand\n✔️ Post to YouTube, LinkedIn, Substack\n✔️ Attract research funding and recognition'
  },
  {
    icon: <Ionicons name="business-outline" size={30} color="#6366f1" />,
    title: 'Entrepreneurs & Startups',
    description: '✔️ Announce new features\n✔️ Attract users and investors\n✔️ Grow faster without a social media team\n✔️ Schedule updates and product demos'
  },
  {
    icon: <Entypo name="shop" size={30} color="#ec4899" />,
    title: 'Small Businesses',
    description: '✔️ Promote products & offers\n✔️ Consistent posting builds trust\n✔️ Save time by automating your marketing\n✔️ Grow sales organically'
  },
  {
    icon: <Ionicons name="megaphone-outline" size={30} color="#ef4444" />,
    title: 'Political & Advocacy Groups',
    description: '✔️ Spread your message across platforms\n✔️ Promote events and rallies\n✔️ Repost third-party approved content\n✔️ Increase supporter engagement'
  },
  {
    icon: <MaterialIcons name="groups" size={30} color="#3b82f6" />,
    title: 'Nonprofits & NGOs',
    description: '✔️ Amplify your mission\n✔️ Share updates and calls to action\n✔️ Promote campaigns with less effort\n✔️ Reach supporters where they are'
  },
  {
    icon: <FontAwesome5 name="laptop-code" size={30} color="#0ea5e9" />,
    title: 'Tech Communities',
    description: '✔️ Share tutorials and demos\n✔️ Grow your open-source community\n✔️ Update followers about releases\n✔️ Attract contributors & collaborators'
  },
  {
    icon: <Ionicons name="newspaper-outline" size={30} color="#10b981" />,
    title: 'News & Journalists',
    description: '✔️ Post breaking news instantly\n✔️ Share verified clips and reels\n✔️ Increase story visibility\n✔️ Stay active on all channels'
  },
  {
    icon: <FontAwesome5 name="chalkboard-teacher" size={30} color="#f43f5e" />,
    title: 'Universities & Labs',
    description: '✔️ Promote academic programs\n✔️ Post lectures and research\n✔️ Reach students and partners\n✔️ Maintain a consistent presence'
  }
];

export default function AudienceShowcase() {
  const [selectedCategory, setSelectedCategory] = useState(categories[0].title);
  const contentScrollRef = useRef(null);
  const { width } = Dimensions.get('window');

  // Calculate item width for scrolling - adjust for desktop
  const isDesktop = width >= 768;
  // For desktop, center the content by using a fixed width
  const itemWidth = isDesktop ? Math.min(600, width * 0.5) : width * 0.9; // Narrower on desktop

  // Handle category selection
  const handleCategorySelect = (title, index) => {
    setSelectedCategory(title);
    // Scroll to the selected category in the content view
    if (contentScrollRef.current) {
      contentScrollRef.current.scrollTo({ x: index * itemWidth, animated: true });
    }
  };

  // The selected category is tracked by the selectedCategory state

  // Function to scroll categories left or right
  const scrollCategories = (direction) => {
    const currentIndex = categories.findIndex(cat => cat.title === selectedCategory);
    let newIndex;

    if (direction === 'left') {
      newIndex = Math.max(0, currentIndex - 1);
    } else {
      newIndex = Math.min(categories.length - 1, currentIndex + 1);
    }

    // Ensure the index is valid
    if (newIndex >= 0 && newIndex < categories.length) {
      handleCategorySelect(categories[newIndex].title, newIndex);

      // Debug log to help troubleshoot
      console.log(`Scrolling ${direction} from ${currentIndex} to ${newIndex}: ${categories[newIndex].title}`);
    }
  };

  return (
    <View className="w-full">
      {/* Main content area - shows the selected category */}
      <ScrollView
        ref={contentScrollRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        pagingEnabled
        className="pt-4"
        snapToInterval={itemWidth}
        decelerationRate="fast"
        snapToAlignment="center"
        contentContainerStyle={{
          alignItems: 'center',
          justifyContent: 'center',
          paddingHorizontal: isDesktop ? width * 0.25 : width * 0.05 // Add padding to prevent next item from showing
        }}
        onMomentumScrollEnd={(event) => {
          // Update selected category when scrolling ends
          const newIndex = Math.round(event.nativeEvent.contentOffset.x / itemWidth);
          if (newIndex >= 0 && newIndex < categories.length) {
            setSelectedCategory(categories[newIndex].title);
          }
        }}
      >
        {categories.map((item) => (
          <View
            key={item.title}
            className={`mx-2 rounded-2xl bg-white shadow-lg p-4 ${isDesktop ? 'w-[50vw]' : 'w-[90vw]'}`}
            style={{
              minHeight: 260,
              width: isDesktop ? Math.min(580, width * 0.5 - 20) : width * 0.9 - 16,
              alignSelf: 'center',
              marginHorizontal: 'auto',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              alignItems: 'center',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            {/* Background logo watermark */}
            <WatermarkLogo
              size={200}
              opacity={0.05}
              position="center"
              style={{ position: 'absolute', width: '100%', height: '100%' }}
            />

            <View className="flex-row items-center space-x-3 mb-3 self-center">
              {item.icon}
              <Text className="text-lg font-bold text-gray-800">
                {item.title}
              </Text>
            </View>
            <Text className="text-gray-600 text-base leading-relaxed whitespace-pre-line text-center px-4">
              {item.description}
            </Text>
          </View>
        ))}
      </ScrollView>

      {/* Navigation arrows and category selector */}
      <View className="flex-row items-center justify-center mt-4">
        {/* Left arrow */}
        <TouchableOpacity
          onPress={() => scrollCategories('left')}
          className="bg-gray-200 rounded-full p-2 mr-2 shadow-sm"
          disabled={selectedCategory === categories[0].title}
          style={{
            opacity: selectedCategory === categories[0].title ? 0.5 : 1,
            padding: isDesktop ? 12 : 8,
          }}
        >
          <AntDesign name="left" size={isDesktop ? 24 : 20} color="#4F46E5" />
        </TouchableOpacity>

        {/* Category selector buttons */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          className="px-2 flex-1"
          contentContainerStyle={{ paddingRight: 20 }}
          ref={(ref) => {
            // When the selected category changes, scroll to make it visible
            if (ref) {
              const index = categories.findIndex(cat => cat.title === selectedCategory);
              if (index >= 0) {
                // Use setTimeout to ensure the scroll happens after render
                setTimeout(() => {
                  ref.scrollTo({ x: index * 120, animated: true });
                }, 100);
              }
            }
          }}
        >
          {categories.map((item, index) => {
            const isSelected = selectedCategory === item.title;
            // Assign a unique color to each category
            const colors = [
              { bg: '#4F46E5', text: 'white' }, // indigo
              { bg: '#10B981', text: 'white' }, // emerald
              { bg: '#F59E0B', text: 'white' }, // amber
              { bg: '#6366F1', text: 'white' }, // indigo
              { bg: '#EC4899', text: 'white' }, // pink
              { bg: '#EF4444', text: 'white' }, // red
              { bg: '#3B82F6', text: 'white' }, // blue
              { bg: '#0EA5E9', text: 'white' }, // sky
              { bg: '#10B981', text: 'white' }, // emerald
              { bg: '#F43F5E', text: 'white' }  // rose
            ];
            const colorIndex = index % colors.length;
            const color = colors[colorIndex];

            return (
              <TouchableOpacity
                key={`btn-${item.title}`}
                className={`px-4 py-2 mx-1 rounded-full shadow-md ${isSelected ? 'border-2 border-gray-800' : ''}`}
                style={{
                  backgroundColor: isSelected ? 'white' : color.bg,
                  minWidth: isDesktop ? 120 : 'auto',
                }}
                onPress={() => handleCategorySelect(item.title, index)}
              >
                <Text
                  className={`text-sm font-semibold ${isDesktop ? 'text-center' : ''}`}
                  style={{ color: isSelected ? color.bg : color.text }}
                >
                  {item.title}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>

        {/* Right arrow */}
        <TouchableOpacity
          onPress={() => scrollCategories('right')}
          className="bg-gray-200 rounded-full p-2 ml-2 shadow-sm"
          disabled={selectedCategory === categories[categories.length - 1].title}
          style={{
            opacity: selectedCategory === categories[categories.length - 1].title ? 0.5 : 1,
            padding: isDesktop ? 12 : 8,
          }}
        >
          <AntDesign name="right" size={isDesktop ? 24 : 20} color="#4F46E5" />
        </TouchableOpacity>
      </View>
    </View>
  );
}
