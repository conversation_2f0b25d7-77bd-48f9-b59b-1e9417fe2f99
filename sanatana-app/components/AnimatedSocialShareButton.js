import React, { useEffect, useRef } from 'react';
import { View, TouchableOpacity, Animated } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import PropTypes from 'prop-types';

// Editable platform list (easy to swap icons/colors)
const platforms = [
  { name: 'facebook', color: '#3b5998' },
  { name: 'twitter', color: '#00acee' },
  { name: 'instagram', color: '#C13584' },
  { name: 'youtube', color: '#FF0000' },
  { name: 'linkedin', color: '#0077B5' },
  { name: 'tiktok', color: '#000000' },
];

export default function AnimatedSocialShareButton({ onPress }) {
  const iconAnimations = useRef(platforms.map(() => new Animated.Value(0))).current;
  const videoX = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Fade-in social icons one by one
    Animated.stagger(
      150,
      iconAnimations.map(anim =>
        Animated.timing(anim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        })
      )
    ).start(() => animateVideo(0));
  }, []);

  const animateVideo = index => {
    // Reset to beginning when reaching the end
    const nextIndex = index >= platforms.length ? 0 : index;

    const targetX = nextIndex * 40; // Adjusted for wider spacing

    Animated.sequence([
      Animated.timing(videoX, {
        toValue: targetX,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.delay(400),
    ]).start(() => animateVideo(nextIndex + 1)); // Continue to next or loop back
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.9}
      className="w-full h-full rounded-2xl bg-gray-100 p-3 flex justify-center items-center shadow-md"
    >
      <View className="relative flex-row items-center justify-center overflow-visible w-full">
        {/* Social Icons */}
        {platforms.map((p, index) => (
          <Animated.View
            key={p.name}
            style={{
              opacity: iconAnimations[index],
              transform: [
                {
                  scale: iconAnimations[index],
                },
              ],
              marginHorizontal: 8,
            }}
          >
            <FontAwesome name={p.name} size={24} color={p.color} />
          </Animated.View>
        ))}

        {/* Animated Video Icon */}
        <Animated.View
          style={{
            position: 'absolute',
            top: -40,
            left: 0,
            transform: [{ translateX: videoX }],
          }}
        >
          <FontAwesome name="video-camera" size={20} color="#333" />
        </Animated.View>
      </View>
    </TouchableOpacity>
  );
}

AnimatedSocialShareButton.propTypes = {
  onPress: PropTypes.func
};

AnimatedSocialShareButton.defaultProps = {
  onPress: () => {}
};
