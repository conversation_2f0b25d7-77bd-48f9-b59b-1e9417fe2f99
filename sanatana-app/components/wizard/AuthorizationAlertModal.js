import React from "react";
import { View, Text, TouchableOpacity, Modal, Platform, Linking, Dimensions } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import Animated<PERSON>ogo from "../common/AnimatedLogo";
import { useNavigation } from '@react-navigation/native';

/**
 * A custom alert modal for authorization instructions
 *
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {function} props.onClose - Function to call when the modal is closed
 * @param {function} props.onProceed - Function to call when the user wants to proceed
 * @param {string} props.destinationTitle - The title of the destination
 * @param {string} props.platformId - The platform ID to navigate to
 */

const isMobile =  Dimensions.get('window').width < 768;

const AuthorizationAlertModal = ({
  visible,
  onClose,
  onProceed,
  destinationTitle,
  platformId,
}) => {
  const message = `You have not authorized ${destinationTitle} yet.\n\nYou will be redirected to the Setup Media page where you need to:\n\n1. Watch the "How to Get Credentials" video\n2. Upload your credentials file\n3. Click "Authorize New Channel" to connect your account\n\nAfter authorization is complete, return to this page to continue.`;
  const navigation = useNavigation();

  return (
    <Modal
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      animationType="fade"
    >
      <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
        <View
          className="bg-white rounded-lg p-6 mx-8 shadow-lg"
          style={{ width: "auto", minWidth: 280, maxWidth: 400 }}
        >
          <View className="flex-row items-center mb-4">
            <View className="flex-row items-center">
              <AnimatedLogo
                size={32}
                backgroundColor="#DBEAFE"
                animation="hueShift"
                style={{ marginRight: 8 }}
              />
              <Icon
                name="info-circle"
                type="font-awesome-5"
                color="#3B82F6"
                size={24}
              />
            </View>
            <Text className="text-blue-800 text-lg font-medium ml-2">
              {destinationTitle} Authorization Required
            </Text>
          </View>

          <Text className="text-gray-700 text-base mb-6 leading-relaxed">
            {message.split('\n').map((line, i) => (
              <React.Fragment key={i}>
                {line}
                {i < message.split('\n').length - 1 && <Text>{'\n'}</Text>}
              </React.Fragment>
            ))}
          </Text>

          <View className="flex-row justify-end space-x-3">
            <TouchableOpacity
              onPress={onClose}
              className="px-4 py-2 rounded-md bg-gray-100 border border-gray-300"
            >
              <Text className="text-gray-700 font-medium">Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                // Close the modal first
                onClose();

                // Then navigate to SetupMedia
                if (!isMobile) {
                  // For web, use window.location
                  window.location.href = `/SetupMedia?default_open_id=${platformId}`;
                } else {
                  // For mobile, use Linking
                 // Linking.openURL(`sanatana://SetupMedia?default_open_id=${platformId}`);
                 // window.location.href = `/SetupMedia?default_open_id=${platformId}`;
                 
                 navigation.navigate('SetupMedia', {
                    default_open_id: platformId,
                  });
                }

                // Also call the onProceed callback if provided
                if (onProceed) {
                  onProceed();
                }
              }}
              className="px-4 py-2 rounded-md bg-blue-600"
            >
              <Text className="text-white font-medium">Go to Setup</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

AuthorizationAlertModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onProceed: PropTypes.func.isRequired,
  destinationTitle: PropTypes.string.isRequired,
  platformId: PropTypes.string.isRequired,
};

export default AuthorizationAlertModal;
