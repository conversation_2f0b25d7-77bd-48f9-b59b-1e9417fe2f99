import React, { useState, useRef, useEffect } from "react";
import { View, Text, TouchableOpacity, Platform } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";

export default function FileUploadInput({
  onComplete,
  onCancel,
  initialData,
  buttonContainerStyle,
}) {
  // Initialize with saved data if available
  const [selectedFile, setSelectedFile] = useState(() => {
    if (initialData && initialData.file) {
      return initialData.file;
    }
    return null;
  });

  const [error, setError] = useState("");
  const fileInputRef = useRef(null);

  // Log initial state for debugging
  useEffect(() => {
    console.log("FileUploadInput initializing with:", {
      initialData,
      selectedFile: selectedFile ? selectedFile.name : null,
    });
  }, []);

  const MAX_FILE_SIZE = 300 * 1024 * 1024; // 300MB in bytes

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > MAX_FILE_SIZE) {
        setError(
          `File size (${formatFileSize(file.size)}) exceeds 300MB limit`
        );
        setSelectedFile(null);
        return;
      }

      if (!file.type.startsWith("video/mp4")) {
        setError("Please select an MP4 video file");
        setSelectedFile(null);
        return;
      }

      setError("");
      setSelectedFile(file);
    }
  };

  const handleUploadClick = () => {
    // Click the hidden file input to open the file picker
    fileInputRef.current?.click();
  };

  const handleSubmit = () => {
    if (selectedFile) {
      // Extract necessary information from the File object
      // This ensures it can be properly serialized to JSON
      onComplete({
        source: "file",
        sourceName: initialData?.sourceName || "File Upload",
        file: selectedFile,
        fileInfo: {
          name: selectedFile.name,
          size: selectedFile.size,
          type: selectedFile.type,
          lastModified: selectedFile.lastModified,
        },
      });
    }
  };

  return (
    <View className="flex-1 flex-col">
      <View className="px-5 pt-5 bg-white">
        <View className="flex-row items-center mb-4">
          <TouchableOpacity onPress={onCancel} className="mr-3">
            <Icon
              name="arrow-left"
              type="font-awesome"
              size={20}
              color="#4B5563"
            />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-800">
            Upload Video File
          </Text>
        </View>
      </View>

      <View className="flex-1 px-5">
        <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
          <Text className="text-gray-700 font-medium mb-4">
            Select Video File
          </Text>

          <input
            type="file"
            ref={fileInputRef}
            accept="video/mp4"
            onChange={handleFileSelect}
            className="hidden"
            capture={Platform.OS !== "web" ? "camera" : undefined}
          />

          <TouchableOpacity
            onPress={handleUploadClick}
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 items-center"
          >
            <Icon
              name="cloud-upload"
              type="font-awesome"
              size={32}
              color="#6B7280"
            />
            <Text className="text-gray-600 mt-4 text-center">
              {selectedFile
                ? `${selectedFile.name} (${formatFileSize(selectedFile.size)})`
                : "Click to select or drag and drop your video file"}
            </Text>
          </TouchableOpacity>

          {error ? (
            <Text className="text-sm text-red-500 mt-2">{error}</Text>
          ) : (
            <Text className="text-sm text-gray-500 mt-2">
              Maximum file size: 300MB, Format: MP4
            </Text>
          )}
        </View>

        <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <Text className="text-sm text-yellow-800 font-medium mb-2">
            Requirements:
          </Text>
          <View className="ml-2">
            <Text className="text-sm text-yellow-800 mb-2">
              • Maximum file size: 300MB
            </Text>
            <Text className="text-sm text-yellow-800 mb-2">
              • Supported format: MP4 only
            </Text>
            <Text className="text-sm text-yellow-800">
              • Video will be processed as is
            </Text>
          </View>
        </View>

        <View className="flex-row space-x-3" style={buttonContainerStyle}>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={!selectedFile}
            className={`py-4 rounded-lg ${
              selectedFile ? "bg-blue-600" : "bg-gray-300"
            }`}
            style={{ flex: 1, marginHorizontal: 20 }}
          >
            <Text className="text-center text-white font-medium">Continue</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

FileUploadInput.propTypes = {
  onComplete: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  initialData: PropTypes.object,
  buttonContainerStyle: PropTypes.object,
};

FileUploadInput.defaultProps = {
  initialData: null,
  buttonContainerStyle: null,
};
