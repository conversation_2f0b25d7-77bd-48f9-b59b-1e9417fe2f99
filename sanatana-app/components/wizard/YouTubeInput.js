import React, { useState } from "react";
import { View, Text, TextInput, TouchableOpacity } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import YouTubeVideoInfoCard from "./YouTubeVideoInfoCard";
import { getFromYoutubeCache } from "../../utils/youtubeCache";

export default function YouTubeInput({
  onComplete,
  onCancel,
  initialData,
  buttonContainerStyle,
}) {
  // Initialize with saved data if available
  const [videoLink, setVideoLink] = useState(
    initialData?.videoLink || ""
  );
  const [error, setError] = useState("");
  const [showVideoInfo, setShowVideoInfo] = useState(false);

  const isValidVideoLink = (link) => {
    const trimmedLink = link.trim();

    // Basic HTTP/HTTPS URL validation
    const urlRegex = /^https?:\/\/.+/i;

    return urlRegex.test(trimmedLink);
  };

  const handleLinkChange = (text) => {
    setVideoLink(text);
    if (text.trim() && !isValidVideoLink(text)) {
      setError("Please enter a valid HTTP or HTTPS URL");
    } else {
      setError("");
    }
  };

  const handleSubmit = () => {
    if (videoLink.trim() && isValidVideoLink(videoLink)) {
      // Check if we have cached data for this URL
      const cachedData = getFromYoutubeCache(videoLink);
      if (cachedData) {
        console.log("Using cached data for direct completion:", videoLink);
        // If we have cached data, we could optionally skip the info card
        // and go directly to the next step
        // handleSelectVideo(cachedData);
        // But for now, we'll still show the info card for user confirmation
      }

      setShowVideoInfo(true);
    }
  };

  const handleSelectVideo = (videoInfo) => {
    onComplete({
      source: "youtube",
      sourceName: initialData?.sourceName || "YouTube",
      videoLink: videoLink.trim(),
      title: videoInfo.title,
      description: videoInfo.description,
      hashtags: [], // Always keep hashtags empty by default
      categories: videoInfo.categories || [],
      duration: videoInfo.duration || 0, // Add duration in seconds
    });
  };

  if (showVideoInfo) {
    return (
      <YouTubeVideoInfoCard
        videoUrl={videoLink}
        onCancel={() => setShowVideoInfo(false)}
        onSelectVideo={handleSelectVideo}
        buttonContainerStyle={buttonContainerStyle}
      />
    );
  }

  return (
    <View className="flex-1 flex-col">
      <View className="px-5 pt-5 bg-white">
        <View className="flex-row items-center mb-4">
          <TouchableOpacity onPress={onCancel} className="mr-3">
            <Icon
              name="arrow-left"
              type="font-awesome"
              size={20}
              color="#4B5563"
            />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-800">
            Video Link
          </Text>
        </View>
      </View>

      <View className="flex-1 px-5">
        <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
          <Text className="text-gray-700 font-medium mb-2">Video Link</Text>
          <TextInput
            value={videoLink}
            onChangeText={handleLinkChange}
            placeholder="https://youtube.com/watch?v=... or any video link"
            placeholderTextColor="#9CA3AF"
            className={`border rounded-lg p-2 ${
              error ? "border-red-300" : "border-gray-200"
            }`}
          />
          {error ? (
            <Text className="text-sm text-red-500 mt-1">{error}</Text>
          ) : (
            <Text className="text-sm text-gray-500 mt-1">
              Paste the link to your video from any platform
            </Text>
          )}
        </View>

        <View className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <View className="flex-row items-start">
            <Icon
              name="info-circle"
              type="font-awesome"
              size={16}
              color="#1D4ED8"
              style={{ marginTop: 2, marginRight: 8 }}
            />
            <Text className="text-sm text-blue-800 flex-1">
              Video will be downloaded in 1080p resolution with highest audio
              quality. Make sure you have the rights to use and share this
              content.
            </Text>
          </View>
        </View>

        <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <Text className="text-sm text-yellow-800 font-medium mb-2">
            Requirements:
          </Text>
          <View className="ml-2">
            <Text className="text-sm text-yellow-800 mb-2">
              • Maximum video duration: 1 hour
            </Text>
            <Text className="text-sm text-yellow-800">
              • Video must be publicly accessible
            </Text>
          </View>
        </View>

        <View className="flex-row space-x-3" style={buttonContainerStyle}>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={!videoLink.trim() || !isValidVideoLink(videoLink)}
            className={`py-4 rounded-lg ${
              videoLink.trim() && isValidVideoLink(videoLink)
                ? "bg-blue-600"
                : "bg-gray-300"
            }`}
            style={{ flex: 1, marginHorizontal: 20 }}
          >
            <Text className="text-center text-white font-medium">Continue</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

YouTubeInput.propTypes = {
  onComplete: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  initialData: PropTypes.object,
  buttonContainerStyle: PropTypes.object,
};

YouTubeInput.defaultProps = {
  initialData: null,
  buttonContainerStyle: null,
};
