import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";

// Utility function to format file size
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export default function SourceDisplayComponent({ wizardData }) {
  const [showDescription, setShowDescription] = useState(false);
  const [showHashtags, setShowHashtags] = useState(false);

  // Helper function to get source-specific details
  const getSourceDetails = () => {
    const { source, sourceName, file, fileInfo, videoLink, folderLink } = wizardData;

    switch (source) {
      case "file":
        return (
          <View className="mt-2">
            <Text className="text-gray-600">
              <Text className="font-medium">File: </Text>
              {fileInfo?.name || file?.name} ({formatFileSize(fileInfo?.size || file?.size || 0)})
            </Text>
            <Text className="text-gray-600">
              <Text className="font-medium">Type: </Text>
              {fileInfo?.type || file?.type || "video/mp4"}
            </Text>
          </View>
        );
      case "youtube":
        return (
          <View className="mt-2">
            <Text className="text-gray-600">
              <Text className="font-medium">YouTube Link: </Text>
              {videoLink}
            </Text>
          </View>
        );
      case "tiktok":
        return (
          <View className="mt-2">
            <Text className="text-gray-600">
              <Text className="font-medium">TikTok Link: </Text>
              {videoLink}
            </Text>
          </View>
        );
      case "gdrive":
        return (
          <View className="mt-2">
            <Text className="text-gray-600">
              <Text className="font-medium">Google Drive Folder: </Text>
              {folderLink}
            </Text>
          </View>
        );
      default:
        return null;
    }
  };

  // Helper function to get title information
  const getTitleInfo = () => {
    const { title, useYouTubeTitle, useAITitle, useCustomTitle } = wizardData;

    if (useYouTubeTitle) {
      return (
        <Text className="text-gray-600">
          <Text className="font-medium">Title: </Text>
          Using original YouTube video title
        </Text>
      );
    } else if (useAITitle) {
      return (
        <Text className="text-gray-600">
          <Text className="font-medium">Title: </Text>
          Using AI-generated title
        </Text>
      );
    } else if (useCustomTitle || title) {
      return (
        <Text className="text-gray-600">
          <Text className="font-medium">Title: </Text>
          {title || "Custom title"}
        </Text>
      );
    }

    return null;
  };

  // Helper function to get description information
  const getDescriptionInfo = () => {
    const { description, useSourceDescription, useAIDescription, useCustomDescription } = wizardData;

    if (useSourceDescription) {
      return (
        <Text className="text-gray-600">
          Using original description from source
        </Text>
      );
    } else if (useAIDescription) {
      return (
        <Text className="text-gray-600">
          Using AI-generated description
        </Text>
      );
    } else if (useCustomDescription || description) {
      // Truncate description if it's too long
      const truncatedDescription = description && description.length > 100
        ? `${description.substring(0, 100)}...`
        : description;

      return (
        <View>
          <Text className="text-gray-600">
            {truncatedDescription || "Custom description"}
          </Text>
          {description && description.length > 100 && (
            <TouchableOpacity
              onPress={() => setShowDescription(!showDescription)}
              className="flex-row items-center mt-1"
            >
              <Text className="text-blue-600 text-sm mr-1">
                {showDescription ? "Show less" : "Show more"}
              </Text>
              <Icon
                name={showDescription ? "chevron-up" : "chevron-down"}
                type="font-awesome"
                size={12}
                color="#2563EB"
              />
            </TouchableOpacity>
          )}
          {showDescription && description && (
            <Text className="text-gray-600 mt-2 p-2 bg-gray-50 rounded-lg">
              {description}
            </Text>
          )}
        </View>
      );
    }

    return null;
  };

  // Helper function to get hashtags information
  const getHashtagsInfo = () => {
    const { hashtags, useAIHashtags } = wizardData;

    if (useAIHashtags) {
      return (
        <Text className="text-gray-600">
          Using AI-generated hashtags
        </Text>
      );
    } else if (hashtags) {
      // Truncate hashtags if they're too long
      const truncatedHashtags = hashtags.length > 100
        ? `${hashtags.substring(0, 100)}...`
        : hashtags;

      return (
        <View>
          <Text className="text-gray-600">
            {truncatedHashtags}
          </Text>
          {hashtags.length > 100 && (
            <TouchableOpacity
              onPress={() => setShowHashtags(!showHashtags)}
              className="flex-row items-center mt-1"
            >
              <Text className="text-blue-600 text-sm mr-1">
                {showHashtags ? "Show less" : "Show more"}
              </Text>
              <Icon
                name={showHashtags ? "chevron-up" : "chevron-down"}
                type="font-awesome"
                size={12}
                color="#2563EB"
              />
            </TouchableOpacity>
          )}
          {showHashtags && hashtags && (
            <Text className="text-gray-600 mt-2 p-2 bg-gray-50 rounded-lg">
              {hashtags}
            </Text>
          )}
        </View>
      );
    }

    return null;
  };

  return (
    <View className="mb-5 p-4 bg-white rounded-lg border border-gray-200">
      <View className="flex-row items-center mb-3">
        <Icon
          name="info-circle"
          type="font-awesome"
          size={20}
          color="#3B82F6"
          style={{ marginRight: 8 }}
        />
        <Text className="text-lg font-bold text-gray-800">
          Source Information
        </Text>
      </View>

      <View className="space-y-2">
        {/* Source Type */}
        <Text className="text-gray-600">
          <Text className="font-medium">Source: </Text>
          {wizardData.sourceName || wizardData.source || "Not selected"}
        </Text>

        {/* Source-specific details */}
        {getSourceDetails()}

        {/* Title information */}
        {getTitleInfo()}

        {/* Description section */}
        <View className="mt-2">
          <Text className="font-medium text-gray-700">Description:</Text>
          {getDescriptionInfo()}
        </View>

        {/* Hashtags section */}
        {(wizardData.hashtags || wizardData.useAIHashtags) && (
          <View className="mt-2">
            <Text className="font-medium text-gray-700">Hashtags:</Text>
            {getHashtagsInfo()}
          </View>
        )}
      </View>
    </View>
  );
}

SourceDisplayComponent.propTypes = {
  wizardData: PropTypes.object.isRequired,
};
