import React, { useState, useEffect } from "react";
import { View, Text, TextInput, TouchableOpacity } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";

export default function TikTokInput({
  onComplete,
  onCancel,
  initialData,
  buttonContainerStyle,
}) {
  // Initialize with saved data if available
  const [videoLink, setVideoLink] = useState(() => {
    if (initialData && initialData.videoLink) {
      return initialData.videoLink;
    }
    return "";
  });

  const [error, setError] = useState("");

  // Log initial state for debugging
  useEffect(() => {
    console.log("TikTokInput initializing with:", {
      initialData,
      videoLink,
    });
  }, []);

  const isValidTikTokLink = (link) => {
    return link.trim().match(/https?:\/\/((?:vm|vt|www)\.)?tiktok\.com\/.+/i);
  };

  const handleLinkChange = (text) => {
    setVideoLink(text);
    if (text.trim() && !isValidTikTokLink(text)) {
      setError("Please enter a valid TikTok video link");
    } else {
      setError("");
    }
  };

  const handleSubmit = () => {
    if (videoLink.trim() && isValidTikTokLink(videoLink)) {
      onComplete({
        source: "tiktok",
        sourceName: initialData?.sourceName || "TikTok",
        videoLink: videoLink.trim(),
      });
    }
  };

  return (
    <View className="flex-1 flex-col">
      <View className="px-5 pt-5 bg-white">
        <View className="flex-row items-center mb-4">
          <TouchableOpacity onPress={onCancel} className="mr-3">
            <Icon
              name="arrow-left"
              type="font-awesome"
              size={20}
              color="#4B5563"
            />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-800">
            TikTok Video Link
          </Text>
        </View>
      </View>

      <View className="flex-1 px-5">
        <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
          <Text className="text-gray-700 font-medium mb-2">Video Link</Text>
          <TextInput
            value={videoLink}
            onChangeText={handleLinkChange}
            placeholder="https://www.tiktok.com/@username/video/..."
            placeholderTextColor="#9CA3AF"
            className={`border rounded-lg p-3 mb-2 ${
              error ? "border-red-300" : "border-gray-200"
            }`}
          />
          {error ? (
            <Text className="text-sm text-red-500 mb-2">{error}</Text>
          ) : (
            <Text className="text-sm text-gray-500 mb-4">
              Paste the link to your TikTok video
            </Text>
          )}
        </View>

        <View className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <View className="flex-row items-start">
            <Icon
              name="info-circle"
              type="font-awesome"
              size={16}
              color="#1D4ED8"
              style={{ marginTop: 2, marginRight: 8 }}
            />
            <Text className="text-sm text-blue-800 flex-1">
              Your TikTok video will be downloaded without watermark for better
              quality. Make sure you have the rights to use and share this
              content.
            </Text>
          </View>
        </View>

        <View className="flex-row space-x-3" style={buttonContainerStyle}>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={!videoLink.trim() || !isValidTikTokLink(videoLink)}
            className={`py-4 rounded-lg ${
              videoLink.trim() && isValidTikTokLink(videoLink)
                ? "bg-blue-600"
                : "bg-gray-300"
            }`}
            style={{ flex: 1, marginHorizontal: 20 }}
          >
            <Text className="text-center text-white font-medium">Continue</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

TikTokInput.propTypes = {
  onComplete: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  initialData: PropTypes.object,
  buttonContainerStyle: PropTypes.object,
};

TikTokInput.defaultProps = {
  initialData: null,
  buttonContainerStyle: null,
};
