import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import Checkbox from "expo-checkbox";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import ErrorModal from "../common/ErrorModal";

export default function ScheduleSelector({
  onComplete,
  initialData,
  buttonContainerStyle,
}) {
  // Initialize with saved data if available
  const [date, setDate] = useState(() => {
    if (initialData && initialData.scheduleDate) {
      return new Date(initialData.scheduleDate);
    }
    return new Date();
  });

  const [uploadWithinHour, setUploadWithinHour] = useState(() => {
    if (initialData && initialData.uploadWithinHour !== undefined) {
      return initialData.uploadWithinHour;
    }
    return true;
  });

  const [scheduleTime, setScheduleTime] = useState(() => {
    if (initialData && initialData.uploadWithinHour !== undefined) {
      return !initialData.uploadWithinHour;
    }
    return false;
  });

  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  // State for error modal
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Log initial state for debugging
  useEffect(() => {
    console.log("ScheduleSelector initializing with:", {
      initialData,
      date,
      uploadWithinHour,
      scheduleTime,
    });
  }, []);

  // Get abbreviated timezone
  const getAbbreviatedTimeZone = () => {
    try {
      const options = { timeZoneName: "short" };
      const timeZoneString = new Intl.DateTimeFormat("en-US", options)
        .formatToParts(date)
        .find((part) => part.type === "timeZoneName")?.value;
      return timeZoneString ? ` (${timeZoneString})` : "";
    } catch (error) {
      return "";
    }
  };

  const handleCheckboxChange = (type) => {
    if (type === "upload") {
      setUploadWithinHour(true);
      setScheduleTime(false);
      setDate(new Date()); // Reset to current time
    } else {
      setUploadWithinHour(false);
      setScheduleTime(true);
    }
  };

  const handleDateChange = (event) => {
    if (!uploadWithinHour) {
      // Parse the date parts from the input value (YYYY-MM-DD)
      const [year, month, day] = event.target.value.split("-").map(Number);

      // Create a new date with the selected date and current time
      const newDate = new Date(date);
      newDate.setFullYear(year, month - 1, day); // month is 0-indexed in JavaScript

      setDate(newDate);
    }
  };

  const handleTimeChange = (event) => {
    if (!uploadWithinHour) {
      const [hours, minutes] = event.target.value.split(":").map(Number);
      const newDate = new Date(date);
      newDate.setHours(hours, minutes, 0, 0); // Set seconds and milliseconds to 0
      setDate(newDate);
    }
  };

  // Function to validate if the selected date/time is in the past
  const isDateInPast = (selectedDate) => {
    const now = new Date();
    return selectedDate < now;
  };

  // Function to close the error modal
  const closeErrorModal = () => {
    setShowErrorModal(false);
  };

  const handleNext = () => {
    // If uploading within an hour, no need to validate the date
    if (uploadWithinHour) {
      const formattedDate = new Date();
      onComplete({
        scheduleDate: formattedDate,
        scheduleTime: null,
        timeZone,
        uploadWithinHour,
      });
      return;
    }

    // Validate that the selected date/time is not in the past
    if (isDateInPast(date)) {
      setErrorMessage("Please select a future date and time for scheduling.");
      setShowErrorModal(true);
      return;
    }

    // If date is valid, proceed
    onComplete({
      scheduleDate: date,
      scheduleTime: formatTimeForInput(date),
      timeZone,
      uploadWithinHour,
    });
  };

  const formatTimeForInput = (date) => {
    // Format the time as HH:MM without timezone adjustments
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const formatDateForInput = (date) => {
    // Format the date as YYYY-MM-DD without timezone adjustments
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // +1 because months are 0-indexed
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  return (
    <View
      className="flex-1 flex-col"
      style={{ height: "100%", display: "flex", flexDirection: "column" }}
    >
      <View className="px-5 pt-5">
        <Text className="text-2xl font-bold text-gray-800 mb-4">
          Schedule Destinations
        </Text>
        <Text className="text-gray-600 mb-3">
          Choose when you want your content to be published
        </Text>
      </View>

      <ScrollView
        className="flex-1 px-5"
        contentContainerStyle={{ paddingBottom: 95 }}
      >
        <View className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <View className="flex-row items-start">
            <Icon
              name="info-circle"
              type="font-awesome"
              size={16}
              color="#1D4ED8"
              style={{ marginTop: 2, marginRight: 8 }}
            />
            <Text className="text-sm text-blue-800 flex-1">
              Your upload will be processed within 30 minutes of the scheduled
              time.
            </Text>
          </View>
        </View>
        <View className="bg-white rounded-lg border border-gray-200 p-2 mb-2">
          <View className="flex-row items-start mt-2 pb-2">
            <Text className="text-gray-600 mb-2">Time Zone:</Text>
            <Text className="text-gray-800 font-medium mb-4">
              {timeZone}
              {getAbbreviatedTimeZone()}
            </Text>
          </View>

          <View className="flex-col md:flex-row items-start md:items-center pt-2 pb-2">
            <View className="flex-row items-center mb-4 md:mb-0 md:mr-8">
              <Checkbox
                value={uploadWithinHour}
                onValueChange={() => handleCheckboxChange("upload")}
                color={uploadWithinHour ? "#2563EB" : undefined}
                className="mr-3"
                style={{ width: 20, height: 20 }}
              />
              <Text className="text-gray-700">Upload within 1 hour</Text>
            </View>

            <View className="flex-row items-center">
              <Checkbox
                value={scheduleTime}
                onValueChange={() => handleCheckboxChange("schedule")}
                color={scheduleTime ? "#2563EB" : undefined}
                className="mr-3"
                style={{ width: 20, height: 20 }}
              />
              <Text className="text-gray-700">Schedule time</Text>
            </View>
          </View>
        </View>

        <View
          className={`bg-white rounded-lg border border-gray-200 p-4 mb-8 ${
            uploadWithinHour ? "opacity-50" : ""
          }`}
        >
          <View className="flex-row justify-between items-center py-3 border-b border-gray-100">
            <Text className="text-gray-600">Date</Text>
            <input
              type="date"
              value={formatDateForInput(date)}
              onChange={handleDateChange}
              min={formatDateForInput(new Date())}
              className="border border-gray-200 rounded px-2 py-1"
              disabled={uploadWithinHour}
            />
          </View>

          <View className="flex-row justify-between items-center py-3">
            <Text className="text-gray-600">Time</Text>
            <View className="flex-row items-center">
              <input
                type="time"
                value={formatTimeForInput(date)}
                onChange={handleTimeChange}
                className="border border-gray-200 rounded px-2 py-1 [color-scheme:light]"
                disabled={uploadWithinHour}
              />
              <Text
                className={`ml-2 font-medium ${
                  date.getHours() < 12 ? "text-green-500" : "text-gray-400"
                }`}
              >
                AM
              </Text>
              <Text className="mx-1 text-gray-400">/</Text>
              <Text
                className={`font-medium ${
                  date.getHours() >= 12 ? "text-green-500" : "text-gray-400"
                }`}
              >
                PM
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      <View
        className="bg-white"
        style={
          buttonContainerStyle || {
            position: "sticky",
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 10,
          }
        }
      >
        <TouchableOpacity
          onPress={handleNext}
          className="rounded-lg py-4 bg-blue-600"
          style={{ marginHorizontal: 20 }}
        >
          <Text className="text-white text-center font-semibold">Continue</Text>
        </TouchableOpacity>
      </View>

      {/* Error Modal for past date selection */}
      <ErrorModal
        visible={showErrorModal}
        onClose={closeErrorModal}
        message={errorMessage}
        title="Invalid Date"
        buttonText="OK"
      />
    </View>
  );
}

ScheduleSelector.propTypes = {
  onComplete: PropTypes.func.isRequired,
  initialData: PropTypes.object,
  buttonContainerStyle: PropTypes.object,
};

ScheduleSelector.defaultProps = {
  initialData: null,
  buttonContainerStyle: null,
};
