import React from "react";
import { View, Text } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import { format } from "date-fns";

export default function DestinationDisplayComponent({ wizardData }) {
  const { destinations, uploadWithinHour, scheduleDate, scheduleTime } =
    wizardData;

  // Helper function to format date and time
  const formatDateTime = () => {
    if (!scheduleDate) return null;

    try {
      // Handle different date formats
      let dateObj;

      if (typeof scheduleDate === "string") {
        // If it's a string, parse it
        dateObj = new Date(scheduleDate);
      } else if (scheduleDate instanceof Date) {
        // If it's already a Date object
        dateObj = scheduleDate;
      } else if (typeof scheduleDate === "object" && scheduleDate !== null) {
        // If it's a serialized date object
        dateObj = new Date(scheduleDate);
      } else {
        console.error("Unknown date format:", scheduleDate);
        return "Invalid date format";
      }

      // Check if the date is valid
      if (isNaN(dateObj.getTime())) {
        console.error("Invalid date object:", dateObj);
        return "Invalid date";
      }

      // Format the date
      const dateStr = format(dateObj, "MMMM d, yyyy");

      // Handle time
      let timeStr;
      if (scheduleTime instanceof Date) {
        timeStr = format(scheduleTime, "h:mm a");
      } else if (
        typeof scheduleTime === "string" &&
        scheduleTime.includes(":")
      ) {
        // If it's a time string like "14:30"
        const [hours, minutes] = scheduleTime.split(":").map(Number);
        const timeObj = new Date();
        timeObj.setHours(hours, minutes, 0, 0);
        timeStr = format(timeObj, "h:mm a");
      } else {
        // Use the time from the date object
        timeStr = format(dateObj, "h:mm a");
      }

      return `${dateStr} at ${timeStr}`;
    } catch (error) {
      console.error("Error formatting date:", error, scheduleDate);
      return "Invalid date";
    }
  };

  // Helper function to get playlist name based on type
  const getPlaylistName = (playlist) => {
    if (!playlist) return "None";

    if (playlist.type === "auto") return "Auto Playlist";
    if (playlist.type === "none") return "No Playlist";
    return playlist.name || "None";
  };

  // Helper function to get scheduling information
  const getSchedulingInfo = () => {
    if (uploadWithinHour) {
      return (
        <View className="flex-row items-center">
          <Icon
            name="clock-o"
            type="font-awesome"
            size={16}
            color="#16A34A"
            style={{ marginRight: 8 }}
          />
          <Text className="text-green-600">Upload within 1 hour</Text>
        </View>
      );
    } else if (scheduleDate) {
      const formattedDateTime = formatDateTime();
      if (!formattedDateTime) return null;

      return (
        <View className="flex-row items-center">
          <Icon
            name="calendar"
            type="font-awesome"
            size={16}
            color="#3B82F6"
            style={{ marginRight: 8 }}
          />
          <Text className="text-blue-600">
            Scheduled for {formattedDateTime}
          </Text>
        </View>
      );
    }

    return null;
  };

  return (
    <View className="mb-5 p-4 bg-white rounded-lg border border-gray-200">
      <View className="flex-row items-center mb-3">
        <Icon
          name="share-alt"
          type="font-awesome"
          size={20}
          color="#3B82F6"
          style={{ marginRight: 8 }}
        />
        <Text className="text-lg font-bold text-gray-800">
          Destination & Schedule
        </Text>
      </View>

      {/* Scheduling information */}
      <View className="mb-4">
        {getSchedulingInfo() || (
          <Text className="text-gray-500 italic">
            No scheduling information provided
          </Text>
        )}
      </View>

      {/* Destinations */}
      <Text className="font-medium text-gray-700 mb-2">
        Uploading to {destinations.length} destination
        {destinations.length !== 1 ? "s" : ""}:
      </Text>

      {destinations.map((dest, index) => (
        <View
          key={`${dest.id}-${index}`}
          className="mb-3 pl-2 border-l-2 border-blue-200"
        >
          <Text className="text-gray-700 font-medium">
            {index + 1}. {dest.name || dest.id}
          </Text>

          {/* Channel information */}
          {dest.playlist && dest.playlist.channel_name && (
            <Text className="text-gray-600 ml-2">
              Channel: {dest.playlist.channel_name}
            </Text>
          )}

          {/* Playlist information */}
          {(dest.id === "youtube_video" || dest.id === "youtube_shorts") &&
            dest.playlist && (
              <View className="flex-row items-center ml-2">
                <Icon
                  name="list-ul"
                  type="font-awesome"
                  size={12}
                  color="#4B5563"
                  style={{ marginRight: 4 }}
                />
                <Text className="text-gray-600">
                  Playlist: {getPlaylistName(dest.playlist)}
                </Text>
              </View>
            )}
        </View>
      ))}

      {destinations.length === 0 && (
        <Text className="text-red-500">No destinations selected</Text>
      )}
    </View>
  );
}

DestinationDisplayComponent.propTypes = {
  wizardData: PropTypes.object.isRequired,
};
