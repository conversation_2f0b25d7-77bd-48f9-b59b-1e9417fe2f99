import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import GoogleDriveInput from "./GoogleDriveInput";
import FileUploadInput from "./FileUploadInput";
import TikTokInput from "./TikTokInput";
import YouTubeInput from "./YouTubeInput";

const sources = [
  {
    id: "gdrive",
    title: "Shared Google Drive Folder",
    icon: "google-drive",
    type: "material-community",
    enabled: false,
  },
  {
    id: "file",
    title: "File Upload",
    icon: "cloud-upload",
    type: "material-community",
    enabled: false,
  },
  {
    id: "tiktok",
    title: "TikTok Post Link",
    icon: "music",
    type: "font-awesome",
    enabled: false,
  },
  {
    id: "youtube",
    title: "YouTube Video Link",
    icon: "youtube",
    type: "font-awesome",
    enabled: true,
  },
  {
    id: "facebook",
    title: "Facebook Post Link",
    icon: "facebook",
    type: "font-awesome",
    enabled: false,
  },
  {
    id: "instagram",
    title: "Instagram Post Link",
    icon: "instagram",
    type: "font-awesome",
    enabled: false,
  },
  {
    id: "twitter",
    title: "X Post Link",
    icon: "twitter",
    type: "font-awesome",
    enabled: false,
  },
  {
    id: "bluesky",
    title: "BlueSky Post Link",
    icon: "cloud",
    type: "font-awesome",
    enabled: false,
  },
  {
    id: "discord",
    title: "Discord Message Link",
    icon: "discord",
    type: "font-awesome-5",
    enabled: false,
  },
  {
    id: "substack",
    title: "Substack Article Link",
    icon: "newspaper",
    type: "font-awesome-5",
    enabled: false,
  },
];

export default function SourceSelector({
  onComplete,
  initialSource,
  initialSourceData,
  sourceSubStep,
  setSourceSubStep,
  buttonContainerStyle,
  inputButtonContainerStyle,
}) {
  // Use sourceSubStep from parent if provided, otherwise use local state
  const [showGDriveInput, setShowGDriveInput] = useState(
    sourceSubStep === "gdrive"
  );
  const [showFileUpload, setShowFileUpload] = useState(
    sourceSubStep === "file"
  );
  const [showTikTokInput, setShowTikTokInput] = useState(
    sourceSubStep === "tiktok"
  );
  const [showYouTubeInput, setShowYouTubeInput] = useState(
    sourceSubStep === "youtube"
  );
  // No need to track selectedSource as it's managed by parent

  useEffect(() => {
    // If sourceSubStep is provided, show the appropriate input
    if (sourceSubStep) {
      if (sourceSubStep === "gdrive") setShowGDriveInput(true);
      else if (sourceSubStep === "file") setShowFileUpload(true);
      else if (sourceSubStep === "tiktok") setShowTikTokInput(true);
      else if (sourceSubStep === "youtube") setShowYouTubeInput(true);
    }
  }, [initialSource, sourceSubStep]);

  const enabledSources = sources.filter((source) => source.enabled);
  const disabledSources = sources.filter((source) => !source.enabled);
  const allSources = [...enabledSources, ...disabledSources];

  const handleSourceSelect = (sourceId) => {
    // Find the source object to get its name
    const selectedSource = allSources.find((source) => source.id === sourceId);
    const sourceName = selectedSource ? selectedSource.title : null;

    // Clear previous source data when selecting a new source
    // This prevents data from one source type from being retained when switching to another
    const baseData = {
      source: sourceId,
      sourceName: sourceName,
      // Clear source-specific fields
      videoLink: undefined,
      folderLink: undefined,
      file: undefined,
    };

    if (sourceId === "gdrive") {
      setShowGDriveInput(true);
      if (setSourceSubStep) setSourceSubStep("gdrive");
    } else if (sourceId === "file") {
      setShowFileUpload(true);
      if (setSourceSubStep) setSourceSubStep("file");
    } else if (sourceId === "tiktok") {
      setShowTikTokInput(true);
      if (setSourceSubStep) setSourceSubStep("tiktok");
    } else if (sourceId === "youtube") {
      setShowYouTubeInput(true);
      if (setSourceSubStep) setSourceSubStep("youtube");
    } else {
      onComplete(baseData);
    }
  };

  if (showGDriveInput) {
    return (
      <GoogleDriveInput
        onComplete={(data) => {
          onComplete(data);
          if (setSourceSubStep) setSourceSubStep(null);
        }}
        onCancel={() => {
          setShowGDriveInput(false);
          if (setSourceSubStep) setSourceSubStep(null);
        }}
        initialData={initialSourceData}
        buttonContainerStyle={inputButtonContainerStyle}
      />
    );
  }

  if (showFileUpload) {
    return (
      <FileUploadInput
        onComplete={(data) => {
          onComplete(data);
          if (setSourceSubStep) setSourceSubStep(null);
        }}
        onCancel={() => {
          setShowFileUpload(false);
          if (setSourceSubStep) setSourceSubStep(null);
        }}
        initialData={initialSourceData}
        buttonContainerStyle={inputButtonContainerStyle}
      />
    );
  }

  if (showTikTokInput) {
    return (
      <TikTokInput
        onComplete={(data) => {
          onComplete(data);
          if (setSourceSubStep) setSourceSubStep(null);
        }}
        onCancel={() => {
          setShowTikTokInput(false);
          if (setSourceSubStep) setSourceSubStep(null);
        }}
        initialData={initialSourceData}
        buttonContainerStyle={inputButtonContainerStyle}
      />
    );
  }

  if (showYouTubeInput) {
    return (
      <YouTubeInput
        onComplete={(data) => {
          onComplete(data);
          if (setSourceSubStep) setSourceSubStep(null);
        }}
        onCancel={() => {
          setShowYouTubeInput(false);
          if (setSourceSubStep) setSourceSubStep(null);
        }}
        initialData={initialSourceData}
        buttonContainerStyle={inputButtonContainerStyle}
      />
    );
  }

  return (
    <View className="flex-1 flex-col">
      <View className="px-5 pt-5 bg-white">
        <Text className="text-xl font-bold text-gray-800 mb-2">
          Select Content Source
        </Text>
        <Text className="text-gray-600 mb-6">
          Choose where your content is coming from
        </Text>
      </View>

      <ScrollView className="flex-1 px-5" style={{ paddingBottom: 90 }}>
        {allSources.map((source) => (
          <TouchableOpacity
            key={source.id}
            onPress={() => source.enabled && handleSourceSelect(source.id)}
            className={`flex-row items-center p-4 mb-3 rounded-lg border ${
              source.enabled
                ? "border-gray-200 bg-white active:border-blue-500 active:bg-blue-50"
                : "border-gray-200 bg-gray-50 opacity-50"
            }`}
            disabled={!source.enabled}
          >
            <Icon
              name={source.icon}
              type={source.type || "font-awesome"}
              size={24}
              color={source.enabled ? "#4B5563" : "#9CA3AF"}
            />
            <Text
              className={`ml-3 flex-1 ${
                source.enabled ? "text-gray-700" : "text-gray-400"
              }`}
            >
              {source.title}
            </Text>
            {!source.enabled && (
              <Text className="text-xs text-gray-400">Coming soon</Text>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

SourceSelector.propTypes = {
  onComplete: PropTypes.func.isRequired,
  initialSource: PropTypes.string,
  initialSourceData: PropTypes.object,
  sourceSubStep: PropTypes.string,
  setSourceSubStep: PropTypes.func,
  buttonContainerStyle: PropTypes.object,
  inputButtonContainerStyle: PropTypes.object,
};

SourceSelector.defaultProps = {
  initialSource: null,
  initialSourceData: null,
  sourceSubStep: null,
  setSourceSubStep: null,
  buttonContainerStyle: null,
  inputButtonContainerStyle: null,
};
