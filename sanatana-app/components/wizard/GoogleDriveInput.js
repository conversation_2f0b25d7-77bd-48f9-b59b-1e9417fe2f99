import React, { useState, useEffect } from "react";
import { View, Text, TextInput, TouchableOpacity } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";

export default function GoogleDriveInput({
  onComplete,
  onCancel,
  initialData,
  buttonContainerStyle,
}) {
  // Initialize with saved data if available
  const [folderLink, setFolderLink] = useState(() => {
    if (initialData && initialData.folderLink) {
      return initialData.folderLink;
    }
    return "";
  });

  const [error, setError] = useState("");

  // Log initial state for debugging
  useEffect(() => {
    console.log("GoogleDriveInput initializing with:", {
      initialData,
      folderLink,
    });
  }, []);

  const isValidDriveLink = (link) => {
    return link.trim().startsWith("https://drive.google.com/drive/folders/");
  };

  const handleLinkChange = (text) => {
    setFolderLink(text);
    if (text.trim() && !isValidDriveLink(text)) {
      setError("Please enter a valid Google Drive folder link");
    } else {
      setError("");
    }
  };

  const handleSubmit = () => {
    if (folderLink.trim() && isValidDriveLink(folderLink)) {
      onComplete({
        source: "gdrive",
        sourceName: initialData?.sourceName || "Google Drive",
        folderLink: folderLink.trim(),
      });
    }
  };

  return (
    <View className="flex-1 flex-col">
      <View className="px-5 pt-5 bg-white">
        <View className="flex-row items-center mb-4">
          <TouchableOpacity onPress={onCancel} className="mr-3">
            <Icon
              name="arrow-left"
              type="font-awesome"
              size={20}
              color="#4B5563"
            />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-800">
            Google Drive Folder
          </Text>
        </View>
      </View>

      <View className="flex-1 px-5">
        <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
          <Text className="text-gray-700 font-medium mb-2">Folder Link</Text>
          <TextInput
            value={folderLink}
            onChangeText={handleLinkChange}
            placeholder="https://drive.google.com/drive/folders/..."
            placeholderTextColor="#9CA3AF"
            className={`border rounded-lg p-3 mb-2 ${
              error ? "border-red-300" : "border-gray-200"
            }`}
          />
          {error ? (
            <Text className="text-sm text-red-500 mb-2">{error}</Text>
          ) : (
            <Text className="text-sm text-gray-500 mb-4">
              Make sure the folder is shared publicly or with
              <EMAIL>
            </Text>
          )}
        </View>

        <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <Text className="text-sm text-yellow-800 font-medium mb-2">
            Important Notes:
          </Text>
          <View className="ml-2">
            <Text className="text-sm text-yellow-800 mb-2">
              • Only the first video found in the folder will be processed
            </Text>
            <Text className="text-sm text-yellow-800 mb-2">
              • Keep only one video file in the folder
            </Text>
            <Text className="text-sm text-yellow-800">
              • You can include a description.txt file in the same folder for
              video description
            </Text>
          </View>
        </View>

        <View className="flex-row space-x-3" style={buttonContainerStyle}>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={!folderLink.trim() || !isValidDriveLink(folderLink)}
            className={`py-4 rounded-lg ${
              folderLink.trim() && isValidDriveLink(folderLink)
                ? "bg-blue-600"
                : "bg-gray-300"
            }`}
            style={{ flex: 1, marginHorizontal: 20 }}
          >
            <Text className="text-center text-white font-medium">Continue</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

GoogleDriveInput.propTypes = {
  onComplete: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  initialData: PropTypes.object,
  buttonContainerStyle: PropTypes.object,
};

GoogleDriveInput.defaultProps = {
  initialData: null,
  buttonContainerStyle: null,
};
