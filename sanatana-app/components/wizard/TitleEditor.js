import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { Icon } from "@rneui/themed";
import Checkbox from "expo-checkbox";
import PropTypes from "prop-types";

const MAX_TITLE_LENGTH = 70;

export default function TitleEditor({
  onComplete,
  wizardData,
  buttonContainerStyle,
}) {
  // Initialize with saved data if available
  const [title, setTitle] = useState(() => {
    // If we have saved title data and it's not null (which means useYouTubeTitle was true)
    if (wizardData?.title !== undefined && wizardData?.title !== null) {
      return wizardData.title;
    }
    return "";
  });

  // Store YouTube title if available
  const [youtubeTitle, setYoutubeTitle] = useState(wizardData?.title || "");

  // Initialize useYouTubeTitle from saved data
  const [useYouTubeTitle, setUseYouTubeTitle] = useState(() => {
    // If we have saved useYouTubeTitle data
    if (wizardData?.useYouTubeTitle !== undefined) {
      return wizardData.useYouTubeTitle;
    }
    // Default to false - make custom title the default
    return false;
  });

  // Initialize useAITitle - always false since it's disabled
  const [useAITitle, setUseAITitle] = useState(false);

  const [error, setError] = useState("");

  // Log initial state for debugging
  useEffect(() => {
    console.log("TitleEditor initializing with:", {
      wizardData,
      title,
      useYouTubeTitle,
      useAITitle,
    });
  }, []);

  // Initialize useCustomTitle state
  const [useCustomTitle, setUseCustomTitle] = useState(() => {
    // If useYouTubeTitle is not true, then useCustomTitle should be true
    return !useYouTubeTitle;
  });

  const handleCheckboxChange = (type) => {
    if (type === "youtube") {
      setUseYouTubeTitle(true);
      setUseAITitle(false);
      setUseCustomTitle(false);
    } else if (type === "ai") {
      // AI title is disabled for now
      // setUseYouTubeTitle(false);
      // setUseAITitle(true);
      // setUseCustomTitle(false);
    } else if (type === "custom") {
      setUseYouTubeTitle(false);
      setUseAITitle(false);
      setUseCustomTitle(true);
    }
  };

  const handleContinue = () => {
    if (useCustomTitle && !title.trim()) {
      setError("Please enter a title");
      return;
    }
    onComplete({
      title: useYouTubeTitle || useAITitle ? null : title.trim(),
      useYouTubeTitle,
      useAITitle,
      useCustomTitle,
    });
  };

  const isYouTubeSource = wizardData?.source === "youtube";

  const handleTitleChange = (text) => {
    if (text.length <= MAX_TITLE_LENGTH) {
      setTitle(text);
      setError("");
    }
  };

  return (
    <View className="h-full bg-white flex flex-col justify-between">
      {/* Header */}
      <View className="px-5 py-2">
        <Text className="text-xl font-bold text-gray-800">
          Set Content Title
        </Text>
        <Text className="text-gray-600 mt-1">
          Enter the title for your content
        </Text>
      </View>

      {/* Main Content - Now in a ScrollView */}
      <ScrollView
        className="flex-1 px-5"
        contentContainerStyle={{ paddingBottom: 95 }}
      >
        <View className="mb-4">
          {isYouTubeSource && (
            <View className="flex-row items-center mb-2">
              <Checkbox
                value={useYouTubeTitle}
                onValueChange={() => handleCheckboxChange("youtube")}
                color={useYouTubeTitle ? "#2563EB" : undefined}
                className="mr-3"
                style={{ width: 20, height: 20 }}
              />
              <Text className="text-gray-700">
                Use original YouTube video title
              </Text>
            </View>
          )}

          <View className="flex-row items-center mb-2">
            <Checkbox
              value={useAITitle}
              onValueChange={() => {}}
              color={"#9CA3AF"}
              className="mr-3"
              style={{ width: 20, height: 20 }}
              disabled={true}
            />
            <Text className="text-gray-500">Use AI title</Text>
            <View className="flex-row items-center ml-2 bg-gray-100 px-2 py-0.5 rounded">
              <Icon
                name="clock-o"
                type="font-awesome"
                size={12}
                color="#9CA3AF"
                style={{ marginRight: 4 }}
              />
              <Text className="text-xs text-gray-500">Coming soon</Text>
            </View>
          </View>

          <View className="flex-row items-center mb-2">
            <Checkbox
              value={useCustomTitle}
              onValueChange={() => handleCheckboxChange("custom")}
              color={useCustomTitle ? "#2563EB" : undefined}
              className="mr-3"
              style={{ width: 20, height: 20 }}
            />
            <Text className="text-gray-700">Custom title</Text>
          </View>
        </View>

        <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-gray-700 font-medium">Title</Text>
            <Text className="text-sm text-gray-500">
              {title.length}/{MAX_TITLE_LENGTH}
            </Text>
          </View>
          <TextInput
            value={title}
            onChangeText={handleTitleChange}
            placeholder={
              useYouTubeTitle
                ? youtubeTitle
                  ? `Using YouTube title: ${youtubeTitle}`
                  : "Using YouTube video title"
                : "Enter title"
            }
            maxLength={MAX_TITLE_LENGTH}
            className={`border rounded-lg p-2 ${
              error ? "border-red-300" : "border-gray-200"
            } ${!useCustomTitle ? "bg-gray-100 text-gray-500" : ""}`}
            editable={useCustomTitle}
          />
          {error && <Text className="text-sm text-red-500 mt-1">{error}</Text>}
          <Text className="text-xs text-gray-500 mt-1">
            Maximum {MAX_TITLE_LENGTH} characters allowed
          </Text>
        </View>

        {isYouTubeSource && useYouTubeTitle && (
          <View className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <View className="flex-row items-start">
              <Icon
                name="info-circle"
                type="font-awesome"
                size={16}
                color="#1D4ED8"
                style={{ marginTop: 2, marginRight: 8 }}
              />
              <Text className="text-sm text-blue-800 flex-1">
                The original title from your YouTube video will be used. You can
                uncheck the option above to set a custom title.
              </Text>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Footer */}
      <View
        className="bg-white"
        style={
          buttonContainerStyle || {
            position: "fixed",
            bottom: 0,
            left: 0,
            right: 0,
            padding: "16px 20px",
            backgroundColor: "white",
            borderTop: "1px solid #e5e7eb",
            zIndex: 10,
          }
        }
      >
        <TouchableOpacity
          onPress={handleContinue}
          disabled={useCustomTitle && !title.trim()}
          className={`rounded-lg py-3 ${
            useYouTubeTitle || useAITitle || (useCustomTitle && title.trim())
              ? "bg-blue-600"
              : "bg-gray-300"
          }`}
          style={{ marginHorizontal: 20 }}
        >
          <Text className="text-white text-center font-semibold">Continue</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

TitleEditor.propTypes = {
  onComplete: PropTypes.func.isRequired,
  wizardData: PropTypes.object,
  buttonContainerStyle: PropTypes.object,
};

TitleEditor.defaultProps = {
  wizardData: {},
  buttonContainerStyle: null,
};
