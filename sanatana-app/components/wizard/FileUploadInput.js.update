// This file contains the updates to be made to the FileUploadInput.js component
// to integrate with the new file upload service

// Import the file upload utility
import { uploadFile } from '../../utils/fileUpload';

// Replace the handleSubmit function with this implementation
const handleSubmit = async () => {
  if (!selectedFile) {
    return;
  }

  try {
    setIsUploading(true);
    setUploadProgress(0);
    setUploadError(null);

    // Start the upload
    const { dbId } = await uploadFile({
      uri: selectedFile.uri,
      sanatanaEmail: initialData?.sanatanaEmail || '<EMAIL>',
      filename: selectedFile.name,
      onProgress: (progress) => {
        setUploadProgress(progress);
      },
      onSuccess: ({ uploadId, dbId }) => {
        console.log(`Upload complete. Upload ID: ${uploadId}, DB ID: ${dbId}`);
      },
      onError: (error) => {
        setUploadError(error);
        setIsUploading(false);
      },
    });

    // Complete the wizard step with the file upload ID
    onComplete({
      source: "file",
      sourceName: initialData?.sourceName || "File Upload",
      file: selectedFile,
      fileInfo: {
        name: selectedFile.name,
        size: selectedFile.size,
        type: selectedFile.type,
        lastModified: selectedFile.lastModified,
      },
      file_upload_id: dbId,
    });
  } catch (error) {
    if (error.message !== 'Upload aborted') {
      setUploadError(error.message);
    }
    setIsUploading(false);
  }
};

// Add these state variables to the component
const [isUploading, setIsUploading] = useState(false);
const [uploadProgress, setUploadProgress] = useState(0);
const [uploadError, setUploadError] = useState(null);
const [uploadController, setUploadController] = useState(null);

// Add a function to cancel the upload
const cancelUpload = () => {
  if (uploadController) {
    uploadController.abort();
    setIsUploading(false);
    setUploadProgress(0);
  }
};

// Add this JSX to display upload progress
const renderUploadProgress = () => {
  if (!isUploading && !uploadError) return null;

  return (
    <View className="mt-4 p-4 bg-gray-50 rounded-lg">
      {isUploading && (
        <>
          <Text className="text-gray-700 font-medium mb-2">Uploading...</Text>
          <View className="w-full bg-gray-200 rounded-full h-2.5">
            <View 
              className="bg-blue-600 h-2.5 rounded-full" 
              style={{ width: `${uploadProgress}%` }}
            />
          </View>
          <Text className="text-gray-600 text-sm mt-1">
            {uploadProgress.toFixed(1)}% complete
          </Text>
          <TouchableOpacity
            onPress={cancelUpload}
            className="mt-2 self-start"
          >
            <Text className="text-red-600">Cancel</Text>
          </TouchableOpacity>
        </>
      )}

      {uploadError && (
        <View className="bg-red-50 border border-red-200 rounded-lg p-3">
          <Text className="text-red-700">{uploadError}</Text>
          <TouchableOpacity
            onPress={() => setUploadError(null)}
            className="mt-2 self-start"
          >
            <Text className="text-blue-600">Try Again</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

// Add this to the JSX, after the upload button
{renderUploadProgress()}
