import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
} from "react-native";
import { Icon } from "@rneui/themed";
import Checkbox from "expo-checkbox";
import PropTypes from "prop-types";

// Character limits for different platforms
const PLATFORM_LIMITS = {
  youtube: 4500,
  tiktok: 2200,
  instagram: 2200,
  facebook: 63206,
  twitter: 280,
};

export default function DescriptionEditor({
  onComplete,
  wizardData,
  buttonContainerStyle,
}) {
  const MAX_DESCRIPTION_LENGTH = 4500;

  // Initialize description from saved data
  const [description, setDescription] = useState(() => {
    // If we have saved description data
    if (wizardData?.description !== undefined) {
      return wizardData.description || "";
    }
    // Default to empty string
    return "";
  });

  // Store YouTube description if available
  const [youtubeDescription, setYoutubeDescription] = useState(wizardData?.description || "");

  // Initialize useSourceDescription from saved data
  const [useSourceDescription, setUseSourceDescription] = useState(() => {
    // If we have saved useSourceDescription data
    if (wizardData?.useSourceDescription !== undefined) {
      return wizardData.useSourceDescription;
    }
    // Default to false - make custom description the default
    return false;
  });

  // Initialize useAIDescription - always false since it's disabled
  const [useAIDescription, setUseAIDescription] = useState(false);

  // Initialize hashtags from saved data
  const [hashtags, setHashtags] = useState(() => {
    // If we have saved hashtags data
    if (wizardData?.hashtags !== undefined) {
      return wizardData.hashtags;
    }
    // Always default to empty string - don't add extracted hashtags
    return "";
  });

  // Initialize useAIHashtags - always false since it's disabled
  const [useAIHashtags, setUseAIHashtags] = useState(false);

  const [error, setError] = useState("");

  // Log initial state for debugging
  useEffect(() => {
    console.log("DescriptionEditor initializing with:", {
      wizardData,
      description,
      useSourceDescription,
      useAIDescription,
      hashtags,
      useAIHashtags,
    });
  }, []);

  // Initialize useCustomDescription state
  const [useCustomDescription, setUseCustomDescription] = useState(() => {
    // If we have saved useCustomDescription data
    if (wizardData?.useCustomDescription !== undefined) {
      return wizardData.useCustomDescription;
    }
    // Default to true - make custom description the default
    return true;
  });

  const handleCheckboxChange = (type) => {
    if (type === "source") {
      setUseSourceDescription(true);
      setUseAIDescription(false);
      setUseCustomDescription(false);
    } else if (type === "ai") {
      // AI description is disabled for now
      // setUseSourceDescription(false);
      // setUseAIDescription(true);
      // setUseCustomDescription(false);
    } else if (type === "custom") {
      setUseSourceDescription(false);
      setUseAIDescription(false);
      setUseCustomDescription(true);
    } else if (type === "aiHashtags") {
      // AI hashtags is disabled for now
      // setUseAIHashtags(!useAIHashtags);
    }
  };

  const getPlaceholderText = () => {
    if (useSourceDescription) {
      if (wizardData?.source === "gdrive") {
        return "Using description from source folder";
      } else if (wizardData?.source === "youtube" && youtubeDescription) {
        // Truncate long descriptions for placeholder
        const maxPreviewLength = 50;
        const truncated = youtubeDescription.length > maxPreviewLength
          ? youtubeDescription.substring(0, maxPreviewLength) + '...'
          : youtubeDescription;
        return `Using YouTube description: ${truncated}`;
      } else {
        return "Using original video description";
      }
    } else {
      return "Enter description here...";
    }
  };

  const handleContinue = () => {
    if (useCustomDescription && !description.trim()) {
      setError("Please enter a description");
      return;
    }
    onComplete({
      description:
        useSourceDescription || useAIDescription ? null : description,
      useSourceDescription,
      useAIDescription,
      useCustomDescription,
      hashtags: useAIHashtags ? null : hashtags,
      useAIHashtags,
    });
  };

  // Find the most restrictive platform limit
  const selectedDestinations =
    wizardData?.source === "gdrive" ||
    wizardData?.source === "tiktok" ||
    wizardData?.source === "youtube"
      ? [wizardData?.source]
      : [];
  const lowestLimit = selectedDestinations.reduce((lowest, dest) => {
    const limit = PLATFORM_LIMITS[dest] || MAX_DESCRIPTION_LENGTH;
    return Math.min(lowest, limit);
  }, MAX_DESCRIPTION_LENGTH);

  return (
    <View className="flex-1 flex-col">
      <ScrollView className="flex-1 px-5" contentContainerStyle={{ paddingBottom: 95 }}>
        <Text className="text-xl font-bold mb-4">Description</Text>

        <View className="mb-4">
          {(wizardData?.source === "gdrive" ||
            wizardData?.source === "tiktok" ||
            wizardData?.source === "youtube") && (
            <View className="flex-row items-center mb-2">
              <Checkbox
                value={useSourceDescription}
                onValueChange={() => handleCheckboxChange("source")}
                color={useSourceDescription ? "#2563EB" : undefined}
                className="mr-3"
                style={{ width: 20, height: 20 }}
              />
              <Text className="text-gray-700">
                {wizardData?.source === "gdrive"
                  ? "Pick up description from source folder"
                  : "Use original post description"}
              </Text>
            </View>
          )}

          <View className="flex-row items-center mb-2">
            <Checkbox
              value={useAIDescription}
              onValueChange={() => {}}
              color={"#9CA3AF"}
              className="mr-3"
              style={{ width: 20, height: 20 }}
              disabled={true}
            />
            <Text className="text-gray-500">Use AI description</Text>
            <View className="flex-row items-center ml-2 bg-gray-100 px-2 py-0.5 rounded">
              <Icon
                name="clock-o"
                type="font-awesome"
                size={12}
                color="#9CA3AF"
                style={{ marginRight: 4 }}
              />
              <Text className="text-xs text-gray-500">Coming soon</Text>
            </View>
          </View>

          <View className="flex-row items-center mb-2">
            <Checkbox
              value={useCustomDescription}
              onValueChange={() => handleCheckboxChange("custom")}
              color={useCustomDescription ? "#2563EB" : undefined}
              className="mr-3"
              style={{ width: 20, height: 20 }}
            />
            <Text className="text-gray-700">Custom description</Text>
          </View>
        </View>

        <View className="mb-4">
          <Text className="text-sm font-medium mb-2">Description</Text>
          <TextInput
            value={description}
            onChangeText={setDescription}
            placeholder={getPlaceholderText()}
            multiline
            maxLength={MAX_DESCRIPTION_LENGTH}
            className={`border rounded-lg p-2 min-h-[120px] ${
              error ? "border-red-300" : "border-gray-200"
            } ${!useCustomDescription ? "bg-gray-100 text-gray-500" : ""}`}
            textAlignVertical="top"
            editable={useCustomDescription}
          />
          {error && <Text className="text-sm text-red-500 mt-1">{error}</Text>}
          <Text className="text-xs text-gray-500 mt-1">
            {description ? description.length : 0}/{MAX_DESCRIPTION_LENGTH}{" "}
            characters
          </Text>
          <Text className="text-xs text-gray-500 mt-2">
            Use #hashtags to add keywords to your content
          </Text>
        </View>

        {/* Hashtags Input */}
        <View className="mb-4 mt-6">
          <View className="flex-row items-center mb-2">
            <Checkbox
              value={useAIHashtags}
              onValueChange={() => {}}
              color={"#9CA3AF"}
              className="mr-3"
              style={{ width: 20, height: 20 }}
              disabled={true}
            />
            <Text className="text-gray-500">AI hashtags</Text>
            <View className="flex-row items-center ml-2 bg-gray-100 px-2 py-0.5 rounded">
              <Icon
                name="clock-o"
                type="font-awesome"
                size={12}
                color="#9CA3AF"
                style={{ marginRight: 4 }}
              />
              <Text className="text-xs text-gray-500">Coming soon</Text>
            </View>
          </View>

          <Text className="text-sm font-medium mb-2">Hashtags</Text>
          <TextInput
            value={hashtags}
            onChangeText={(text) => {
              if (text.length <= 2000) {
                setHashtags(text);
              }
            }}
            placeholder="Enter hashtags here..."
            multiline
            numberOfLines={3}
            maxLength={2000}
            className="border rounded-lg p-2 min-h-[80px] border-gray-200"
            textAlignVertical="top"
            editable={true}
          />
          <Text className="text-xs text-gray-500 mt-1">
            {hashtags ? hashtags.length : 0}/2000 characters
          </Text>
          <Text className="text-xs text-gray-500 mt-2">
            Enter hashtags with # symbols or comma separated words, or mix of
            them. If there are hashtags in the content, they will be considered
            first. Note that depending on social media platform, limited number
            of hashtags are added. Put the most important ones first.
          </Text>
        </View>

        {/* Hashtags will be parsed on the server side */}

        {lowestLimit < MAX_DESCRIPTION_LENGTH && (
          <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <View className="flex-row items-start">
              <Icon
                name="warning"
                type="font-awesome"
                size={16}
                color="#92400E"
                style={{ marginTop: 2, marginRight: 8 }}
              />
              <View className="flex-1">
                <Text className="text-sm text-yellow-800">
                  Character limit for selected platform:{" "}
                  <Text className="font-medium">{lowestLimit}</Text>
                </Text>
                <Text className="text-xs text-yellow-700 mt-1">
                  Your description will be truncated if it exceeds this limit.
                </Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>

      <View style={buttonContainerStyle}>
        <TouchableOpacity
          onPress={handleContinue}
          disabled={useCustomDescription && !description.trim()}
          className={`rounded-lg py-3 ${
            useSourceDescription ||
            useAIDescription ||
            (useCustomDescription && description.trim())
              ? "bg-blue-600"
              : "bg-gray-300"
          }`}
          style={{ marginHorizontal: 20 }}
        >
          <Text className="text-white text-center font-medium">Continue</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

DescriptionEditor.propTypes = {
  onComplete: PropTypes.func.isRequired,
  wizardData: PropTypes.object,
  buttonContainerStyle: PropTypes.object,
};

DescriptionEditor.defaultProps = {
  wizardData: null,
  buttonContainerStyle: null,
};
