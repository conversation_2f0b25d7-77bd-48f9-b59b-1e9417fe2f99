import React, { useState, useCallback, useContext } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { Icon } from '@rneui/themed';
import { useFocusEffect } from '@react-navigation/native';
import axios from 'axios';
import Constants from 'expo-constants';
import PropTypes from 'prop-types';
import JobStatusCardWrapper from './JobStatusCardWrapper';
import { UserContext } from '../context/UserContext';

const ProfileJobStatus = ({ navigation, maxItems = 5 }) => {
  const { user } = useContext(UserContext);
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  // Get the job status service URL
  const getJobServiceUrl = () => {
    const url = Constants.expoConfig?.extra?.SANATANA_JOBSTATUS_DOMAIN;
    console.log('Job status service URL:', url);
    return url;
  };

  const getMediaVerseJobServiceUrl = () => {
    const url = "https://service.mediaverse.site"
    console.log('MediaVerse Job status service URL:', url);
    return url;
  };

  const fetchJobs = async () => {
    try {
      if (!user?.email) {
        console.log('User not authenticated, email missing');
        setError('User not authenticated');
        setLoading(false);
        return;
      }

      const serviceUrl = getJobServiceUrl();
      if (!serviceUrl) {
        console.error('Job status service URL is undefined');
        setError('Job service configuration missing');
        setLoading(false);
        return;
      }

      console.log(`Fetching jobs for user: ${user.email}`);
      setError(null);

      const url = `${serviceUrl}/api/jobs/user/${user.email}/pending`;
      console.log('Fetching from URL:', url);

      const response = await axios.get(url);
      console.log('Job fetch response:', response.status, typeof response.data);

      const mediaVerseServiceUrl  = getMediaVerseJobServiceUrl();
      const url2 = `${mediaVerseServiceUrl}/get_jobs_for_user`
      const response2 = await axios.get(url2, {
        params: {
          username: user.email,
          status: 'pending'
        }
      });
      console.log('MediaVerse Job fetch response:', response2.status, typeof response2.data);

      // Ensure jobs is always an array
      const jobsData = Array.isArray(response.data) ? response.data : [];
      console.log(`Received ${jobsData.length} jobs`);
      setJobs(jobsData);
    } catch (err) {
      console.error('Error fetching jobs:', err);
      console.error('Error details:', err.message, err.stack);
      setError(`Failed to load jobs: ${err.message}`);
      // Set jobs to empty array on error
      setJobs([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // const onRefresh = useCallback(() => {
  //   setRefreshing(true);
  //   fetchJobs();
  // }, []);

  // // Fetch jobs when screen comes into focus and set up auto-refresh
  // useFocusEffect(
  //   useCallback(() => {
  //     console.log('ProfileJobStatus screen focused, fetching jobs...');

  //     // Initial fetch
  //     try {
  //       fetchJobs();
  //     } catch (error) {
  //       console.error('Error in initial fetchJobs:', error);
  //     }

  //     // Set up auto-refresh every 15 seconds
  //     let intervalId;
  //     try {
  //       intervalId = setInterval(() => {
  //         console.log('Auto-refreshing job status...');
  //         try {
  //           fetchJobs();
  //         } catch (refreshError) {
  //           console.error('Error in auto-refresh fetchJobs:', refreshError);
  //         }
  //       }, 15000); // 15 seconds
  //     } catch (intervalError) {
  //       console.error('Error setting up interval:', intervalError);
  //     }

  //     // Clean up interval when screen loses focus
  //     return () => {
  //       console.log('ProfileJobStatus screen unfocused, cleaning up...');
  //       if (intervalId) {
  //         try {
  //           clearInterval(intervalId);
  //           console.log('Interval cleared successfully');
  //         } catch (cleanupError) {
  //           console.error('Error clearing interval:', cleanupError);
  //         }
  //       }
  //     };
  //   }, [user])
  // );

  const handleStatusChange = (jobInfo) => {
    // Update the job in the list
    setJobs(prevJobs =>
      prevJobs.map(job =>
        job.id === jobInfo.id ? { ...job, status: jobInfo.status?.current } : job
      )
    );
  };

  // Ensure jobs is an array and limit the number of jobs to display
  const limitedJobs = Array.isArray(jobs) ? jobs.slice(0, maxItems) : [];

  // Early return for initial loading
  if (loading && !refreshing) {
    return (
      <View className="justify-center items-center bg-gray-50 p-4 rounded-lg">
        <ActivityIndicator size="small" color="#4F46E5" />
        <Text className="text-sm text-gray-600 mt-2">Loading jobs...</Text>
      </View>
    );
  }

  return (
    <View className="bg-white rounded-lg overflow-hidden border border-gray-200">
      <View className="flex-row justify-between items-center p-3 bg-gray-50 border-b border-gray-200">
        <Text className="text-lg font-semibold text-gray-800">Recent Upload Jobs</Text>
        <View className="flex-row">
          <TouchableOpacity
            className="p-2"
            onPress={onRefresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <ActivityIndicator size="small" color="#4F46E5" />
            ) : (
              <Icon name="refresh" type="font-awesome" size={16} color="#4F46E5" />
            )}
          </TouchableOpacity>
          <TouchableOpacity
            className="p-2"
            onPress={() => navigation.navigate('JobStatus')}
          >
            <Icon name="external-link" type="font-awesome" size={16} color="#4F46E5" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Content based on state */}

      {/* {error && (
        <View className="p-4 items-center">
          <Text className="text-red-600 text-sm text-center">{error}</Text>
          <TouchableOpacity
            className="mt-2 bg-red-500 px-3 py-1.5 rounded"
            onPress={fetchJobs}
          >
            <Text className="text-white text-sm">Retry</Text>
          </TouchableOpacity>
        </View>
      )} */}

      {!error && limitedJobs.length === 0 && (
        <View className="p-4 items-center">
          <Text className="text-gray-500 text-sm text-center">No upload jobs found</Text>
          <TouchableOpacity
            className="mt-2 bg-indigo-600 px-3 py-1.5 rounded"
            onPress={() => navigation.navigate('Home')}
          >
            <Text className="text-white text-sm">Create New Upload</Text>
          </TouchableOpacity>
        </View>
      )}

      {!error && limitedJobs.length > 0 && (
        <View className="p-2">
          {/* {limitedJobs.map(item => (
            <JobStatusCardWrapper
              key={`job-${item.id}`}
              jobId={item.id}
              onStatusChange={handleStatusChange}
            />
          ))} */}

          {jobs.length > maxItems && (
            <TouchableOpacity
              className="mt-2 p-3 bg-gray-100 rounded-lg items-center"
              onPress={() => navigation.navigate('JobStatus')}
            >
              <Text className="text-indigo-600 font-medium">
                View All Jobs ({jobs.length})
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

ProfileJobStatus.propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired
  }).isRequired,
  maxItems: PropTypes.number
};

export default ProfileJobStatus;
