import React from "react";
import { Modal, View, Text, TouchableOpacity } from "react-native";
import { FontAwesome } from "@expo/vector-icons";
import AnimatedLogo from "./common/AnimatedLogo";
import PropTypes from "prop-types";

const AuthorizationSuccessModal = ({ visible, onClose, onCloseAuthSteps, onBackToWizard }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-70 justify-center items-center">
        <View className="bg-white rounded-lg p-6 m-5 max-w-md">
          {/* Logo and Success Icon */}
          <View className="flex-row justify-center items-center mb-4">
            <AnimatedLogo size={40} animation="hueShift" />
            <FontAwesome name="check-circle" size={40} color="#10B981" className="ml-2" />
          </View>

          {/* Title */}
          <Text className="text-2xl font-bold text-center text-gray-800 mb-4">
            Authorization Successful!
          </Text>

          {/* Message */}
          <Text className="text-gray-600 text-center mb-6">
            Thank you for authorizing your YouTube channel! You can authorize any number of YouTube channels, even from different Google accounts.
          </Text>

          <Text className="text-gray-600 text-center mb-6">
            This gives you more flexibility and capacity for your uploads.
          </Text>

          {/* Buttons */}
          <View className="flex-row justify-center space-x-4">
            <TouchableOpacity
              onPress={() => {
                // Call onCloseAuthSteps to refresh the channels list
                if (onCloseAuthSteps) onCloseAuthSteps();
                // Close the modal
                onClose();
              }}
              className="bg-gray-200 py-2 px-4 rounded-lg"
            >
              <Text className="text-gray-800 font-medium">
                Close
              </Text>
            </TouchableOpacity>

          
          </View>
        </View>
      </View>
    </Modal>
  );
};

// Add PropTypes validation
AuthorizationSuccessModal.propTypes = {
  visible: PropTypes.bool,
  onClose: PropTypes.func,
  onCloseAuthSteps: PropTypes.func,
  onBackToWizard: PropTypes.func
};

// Default props
AuthorizationSuccessModal.defaultProps = {
  visible: false,
  onClose: () => {},
  onCloseAuthSteps: () => {},
  onBackToWizard: null
};

export default AuthorizationSuccessModal;
