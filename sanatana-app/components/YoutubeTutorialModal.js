import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Linking, View, Text, TouchableOpacity, useWindowDimensions, ActivityIndicator, Alert } from "react-native";
import { FontAwesome } from "@expo/vector-icons"; // Using Expo vector icons
import VideoTutorialPlayer from "./VideoTutorialPlayer"; // Import Video component
import SlideShowTutorial from "./not-used-now/SlideShowTutorial"; // Import SlideShow component
import Clipboard from '@react-native-clipboard/clipboard';
import Constants from "expo-constants";
import { getTutorialUrl } from "../utils/tutorialUtils";

const YoutubeTutorialModal = ({ visible, onClose }) => {
  const { width } = useWindowDimensions();
  const isDesktop = width > 768;

  const [selectedOption, setSelectedOption] = useState(isDesktop? "desktop-video": "mobile-video");
  const [loading, setLoading] = useState(true);
  const redirectUri = `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/youtube/oauth2callback`;

  // Function to handle copy to clipboard
  const copyToClipboard = () => {
    Clipboard.setString(redirectUri);
    Alert.alert("Copied to clipboard", "The authorized redirect URI has been copied.");
  };

  // YouTube video URLs (fallbacks)
  const [desktopVideoUrl, setDesktopVideoUrl] = useState("https://www.youtube.com/watch?v=zuyenAof3vE");
  const [mobileVideoUrl, setMobileVideoUrl] = useState("https://www.youtube.com/watch?v=YuuivDiZL2g");

  // Fetch tutorial URLs when component becomes visible
  useEffect(() => {
    // Only fetch when modal is visible
    if (!visible) return;

    const fetchTutorialUrls = async () => {
      try {
        setLoading(true);
        console.log('YoutubeTutorialModal: Fetching fresh tutorial URLs');

        // Generate a timestamp to force fresh data
        const timestamp = Date.now();

        // Get desktop tutorial URL
        const desktopUrl = await getTutorialUrl(
          "How to upload your YouTube credentials - Desktop",
          "https://www.youtube.com/watch?v=zuyenAof3vE",
          timestamp
        );
        setDesktopVideoUrl(desktopUrl);

        // Get mobile tutorial URL
        const mobileUrl = await getTutorialUrl(
          "How to upload your YouTube credentials - Mobile",
          "https://www.youtube.com/watch?v=YuuivDiZL2g",
          timestamp
        );
        setMobileVideoUrl(mobileUrl);
      } catch (error) {
        console.error('Error fetching tutorial URLs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTutorialUrls();
  }, [visible]); // Re-fetch when visibility changes

  // Slideshow JSON data (mobile and desktop images)
  const slideshowData = {
    mobile: [
      "https://example.com/screenshot1.jpg",
      "https://example.com/screenshot2.jpg",
      "https://example.com/screenshot3.jpg",
    ],
    desktop: [
      "https://example.com/screenshot1.jpg",
      "https://example.com/screenshot2.jpg",
    ],
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-90 justify-center items-center">


        <View className="flex-row justify-center space-x-4 mb-4 w-full">
          <TouchableOpacity
            key="desktop-video"
            onPress={() => setSelectedOption("desktop-video")}
            className={`px-3 py-1 rounded ${
              selectedOption === "desktop-video" ? "bg-blue-500" : "bg-gray-700"
            }`}
          >
            <Text className="text-white text-sm">Desktop Video</Text>
          </TouchableOpacity>
          <TouchableOpacity
            key="mobile-video"
            onPress={() => setSelectedOption("mobile-video")}
            className={`px-3 py-1 rounded ${
              selectedOption === "mobile-video" ? "bg-blue-500" : "bg-gray-700"
            }`}
          >
            <Text className="text-white text-sm">Mobile Video</Text>
          </TouchableOpacity>
        </View>


        {/* Content Area */}

          <View className="w-auto h-auto flex-grow flex items-center justify-center">
            {selectedOption === "desktop-video" ||
            selectedOption === "mobile-video" ? (
              <View className="w-auto h-auto bg-gray-800 rounded-lg">
                <VideoTutorialPlayer
                  youtubeUrl={
                    selectedOption === "desktop-video"
                      ? desktopVideoUrl
                      : mobileVideoUrl
                  }
                  loading={loading}
                />
              </View>
            ) : (
              <View className="w-full h-full bg-gray-800 rounded-lg">
                <SlideShowTutorial
                  images={
                    selectedOption.includes("mobile")
                      ? slideshowData.mobile
                      : slideshowData.desktop
                  }
                />
              </View>
            )}
          </View>
          <View className="flex justify-center items-center p-5 space-y-6">
      {/* Expand Video to Full Screen Section */}
      <View className="bg-blue-500 p-4 rounded-lg shadow-lg w-full text-center  flex flex-row items-center justify-center">
        <Text className="text-white text-sm">For better viewing,
          expand video to full screen.
          You can view on one device
          and follow on another.
        </Text>
      </View>

      {/* Go to Google Cloud Console Button */}
      <TouchableOpacity
        onPress={() => Linking.openURL("https://console.cloud.google.com/")}
        className="bg-green-500 flex flex-row items-center justify-center p-3 rounded-lg shadow-lg"
      >
        <FontAwesome name="cloud" size={20} color="white" />
        <Text className="text-white ml-2 text-sm">Go to Google Cloud Console</Text>
      </TouchableOpacity>

      {/* Authorized Redirect URI Section */}
      <View className="w-full  flex items-center justify-center">
        <Text className="text-white text-sm ">Authorized redirect URI:</Text>
        <View className="flex-row items-center mb-4">
          <Text className="text-white text-sm mr-2 bg-emerald-600 p-2 rounded-lg">Copy Authorized redirect URI</Text>
          <TouchableOpacity onPress={copyToClipboard}>
            <FontAwesome name="clipboard" size={24} color="green" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
          <View className="flex-row justify-center space-x-4 mb-4 w-full">
          {/* Close Button */}
          <TouchableOpacity onPress={onClose} className="">
            <FontAwesome name="close" size={24} color="red">
              {" "}
              Close Tutorial{" "}
            </FontAwesome>
          </TouchableOpacity>
        </View>
        </View>

    </Modal>
  );
};

export default YoutubeTutorialModal;
