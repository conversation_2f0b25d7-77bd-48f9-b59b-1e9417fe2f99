import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Linking,
  ScrollView,
  useWindowDimensions,
} from "react-native";
import { MaterialIcons, FontAwesome } from "@expo/vector-icons";
import AuthorizationStepsModal from "./AuthorizationStepsModal";
import AuthorizationSuccessModal from "./AuthorizationSuccessModal";
import PropTypes from "prop-types";

const AuthorizedChannels = ({ authorizedUrls, onCloseAuthSteps, onBackToWizard }) => {
  const [showAuthStepsModal, setShowAuthStepsModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const { width } = useWindowDimensions();
  const isDesktop = width > 768;

  if (!authorizedUrls || authorizedUrls.length === 0) {
    return (
      <View className="flex-1 justify-center items-center">
        {/* <Text className="text-gray-500">No authorized channels found.</Text> */}
      </View>
    );
  }

  return (
    <>
      <ScrollView className="">
        {authorizedUrls.map((auth, index) => (
          <View key={index} className="mb-6 p-4 bg-white shadow rounded-lg">
 
              <Text className="text-lg font-bold text-white bg-blue-400 px-3 py-2 mb-4 rounded-t-lg">
              Credentials: {auth.project_id}
            </Text>

            {/* Available Uploads */}
            <View className="mt-2 flex-row items-center">
              <MaterialIcons name="cloud-upload" size={20} color="gray" />
              <Text className="ml-2 text-gray-700">
                Available uploads today: {auth.available_uploads} (before 12AM
                PST)
              </Text>
            </View>

            {/* Authorized Channels Header */}
            <Text className="mt-4 text-lg font-bold text-white bg-gray-700 px-3 rounded-lg">
              Authorized Channels
            </Text>

            {/* Channels List */}
            {auth.channels.map((channel, idx) => (
              <View key={idx} className="mt-3 bg-gray-100 rounded-lg">
                {isDesktop ? (
                  <View className="flex-row items-center justify-between">
                    {/* Left-aligned Channel Button */}
                    <TouchableOpacity
                      className="bg-gray-200 py-2rounded-lg flex-row items-center"
                      onPress={() => Linking.openURL(channel.youtube_link)}
                    >
                      <FontAwesome name="youtube-play" size={20} color="red" />
                      <Text className="text-gray-900 font-semibold text-lg ml-2">
                        {channel.channel_name}
                      </Text>
                    </TouchableOpacity>

                    {/* Right-aligned Long Uploads Status */}
                    {channel.longUploadsStatus === "eligible" ? (
                      <View className="flex-row items-center">
                        <MaterialIcons
                          name="check-circle"
                          size={16}
                          color="green"
                        />
                        <Text className="ml-2 text-green-700">
                          Long Videos allowed
                        </Text>
                      </View>
                    ) : (
                      <TouchableOpacity
                        className="flex-row items-center"
                        onPress={() =>
                          Linking.openURL("https://www.youtube.com/verify")
                        }
                      >
                        <MaterialIcons
                          name="error-outline"
                          size={16}
                          color="gray"
                        />
                        <Text className="ml-2 text-gray-600">
                          Get Verified for Long Uploads
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>
                ) : (
                  <>
                    {/* Mobile: Channel button takes a full row */}
                    <TouchableOpacity
                      className="bg-gray-200 py-2rounded-lg flex-row items-center justify-start"
                      onPress={() => Linking.openURL(channel.youtube_link)}
                    >
                      <FontAwesome name="youtube-play" size={20} color="red" />
                      <Text className="text-gray-900 font-semibold text-lg ml-2">
                        {channel.channel_name}
                      </Text>
                    </TouchableOpacity>

                    {/* Mobile: Long Uploads Status below button */}
                    {channel.longUploadsStatus === "eligible" ? (
                      <View className="mt-2 flex-row items-center justify-end">
                        <MaterialIcons
                          name="check-circle"
                          size={16}
                          color="green"
                        />
                        <Text className="ml-2 text-green-700">
                          Long Videos allowed
                        </Text>
                      </View>
                    ) : (
                      <TouchableOpacity
                        className="mt-2 flex-row items-center"
                        onPress={() =>
                          Linking.openURL("https://www.youtube.com/verify")
                        }
                      >
                        <MaterialIcons
                          name="error-outline"
                          size={16}
                          color="gray"
                        />
                        <Text className="ml-2 text-gray-600">
                          Get Verified for Long Uploads
                        </Text>
                      </TouchableOpacity>
                    )}
                  </>
                )}
              </View>
            ))}
        
            {isDesktop ? (<>
             
            <View className="flex-row items-center justify-between">
            
              <View className="flex-row items-center">
                <TouchableOpacity
                  className="py-2 px-4 rounded-lg flex-row items-center mr-2"
                  onPress={() => {
                    setShowAuthStepsModal(true);
                  }}
                >
                  <FontAwesome name="play-circle" size={20} color="blue" />
                  <Text className="text-blue-600 font-semibold ml-2">
                    Watch Authorization Steps
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  className="bg-blue-600 py-2 px-4 rounded-lg flex-row items-center"
                  onPress={() => {
                    Linking.openURL(auth.authorization_url);
                    // Show success modal after a delay (simulating successful authorization)
                    setTimeout(() => setShowSuccessModal(true), 1000);
                  }}
                >
                  <MaterialIcons name="vpn-key" size={30} color="white" />
                  <Text className="text-xl text-white font-semibold ml-2">
                    Authorize New Channel
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            </>
          ) : (
            <>
              {/* Mobile: Project ID on top, then button below */}
              {/* <Text className="text-lg font-bold text-white bg-blue-400 px-3 py-2 rounded-t-lg">
                Credentials: {auth.project_id}
              </Text> */}
              {/* For mobile, stack the buttons vertically */}
              <TouchableOpacity
                className=" my-2 px-4 py-2 rounded-lg flex-row items-center justify-center self-end"
                onPress={() => {
                  setShowAuthStepsModal(true);
                }}
              >
                <FontAwesome name="play-circle" size={20} color="blue" />
                  <Text className="text-blue-600 font-semibold ml-2">
                    Watch Authorization Steps
                  </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-blue-600 my-2 px-4 py-2 rounded-lg flex-row items-center justify-center self-end"
                onPress={() => {
                  Linking.openURL(auth.authorization_url);
                  // Show success modal after a delay (simulating successful authorization)
                  setTimeout(() => setShowSuccessModal(true), 1000);
                }}
              >
                <MaterialIcons name="vpn-key" size={30} color="white" />
                <Text className="text-xl text-white font-semibold ml-2">
                  Authorize New Channel
                </Text>
              </TouchableOpacity>
            </>
          )}
            </View>
        ))}
      </ScrollView>

      {/* Authorization Steps Modal */}
      <AuthorizationStepsModal
        visible={showAuthStepsModal}
        onClose={() => setShowAuthStepsModal(false)}
        onAuthorized={() => {
          setShowAuthStepsModal(false);
          setTimeout(() => setShowSuccessModal(true), 500);
        }}
      />

      {/* Authorization Success Modal */}
      {/* <AuthorizationSuccessModal
        visible={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        onCloseAuthSteps={() => {
          // Call the onCloseAuthSteps prop to refresh channels
          onCloseAuthSteps();
          // Hide the success modal
          setShowSuccessModal(false);
        }}
        onBackToWizard={onBackToWizard}
      /> */}
    </>
  );
};

// Add PropTypes validation
AuthorizedChannels.propTypes = {
  authorizedUrls: PropTypes.array,
  onCloseAuthSteps: PropTypes.func,
  onBackToWizard: PropTypes.func,
};

// Default props
AuthorizedChannels.defaultProps = {
  authorizedUrls: [],
  onCloseAuthSteps: () => {},
  onBackToWizard: () => {},
};

export default AuthorizedChannels;
