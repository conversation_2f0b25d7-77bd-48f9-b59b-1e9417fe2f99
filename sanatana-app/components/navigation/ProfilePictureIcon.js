import React, { useContext } from 'react';
import { View, Image, Dimensions } from 'react-native';
import { UserContext } from '../../context/UserContext';
import PropTypes from 'prop-types';

/**
 * A component that displays the user's profile picture as a tab icon
 *
 * @param {Object} props - Component props
 * @param {string} props.color - The color of the icon (used for the border)
 * @param {number} props.size - The size of the icon
 * @param {boolean} props.focused - Whether the tab is focused
 */
const ProfilePictureIcon = ({ color, size, focused }) => {
  const { user } = useContext(UserContext);
  const { width } = Dimensions.get('window');
  const isMobile = width < 768;

  // Make the icon bigger on desktop
  const iconSize = isMobile ? size : size * 1.3;

  // If no user or no profile picture, show a default icon
  if (!user?.profile_picture) {
    // Return a default icon
    return (
      <View
        style={{
          width: iconSize,
          height: iconSize,
          borderRadius: iconSize / 2,
          backgroundColor: '#e0e0e0',
          justifyContent: 'center',
          alignItems: 'center',
          borderWidth: focused ? 2 : 0,
          borderColor: color,
        }}
      />
    );
  }

  return (
    <View
      style={{
        width: iconSize,
        height: iconSize,
        borderRadius: iconSize / 2,
        borderWidth: focused ? 2 : 0,
        borderColor: color,
        overflow: 'hidden',
      }}
    >
      <Image
        source={{ uri: user.profile_picture }}
        style={{ width: '100%', height: '100%' }}
        resizeMode="cover"
      />
    </View>
  );
};

ProfilePictureIcon.propTypes = {
  color: PropTypes.string.isRequired,
  size: PropTypes.number.isRequired,
  focused: PropTypes.bool.isRequired
};

export default ProfilePictureIcon;
