import React from 'react';
import { View, Image } from 'react-native';
import PropTypes from 'prop-types';

/**
 * A component that displays the beta logo as a tab icon
 *
 * @param {Object} props - Component props
 * @param {number} props.size - The size of the icon
 */
const BetaLogoIcon = ({ size }) => {
  return (
    <View
      style={{
        width: size * 1.4, // Make the container 40% larger than the standard size
        height: size * 1.4,
        borderRadius: (size * 1.4) / 2, // Keep it circular
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Image
        source={require('../../assets/sanatana_log-beta-3.png')}
        style={{
          width: size * 1.3, // Make the image 30% larger than the standard size
          height: size * 1.3,
          borderRadius: (size * 1.3) / 2, // Keep the image circular
        }}
        resizeMode="contain"
      />
    </View>
  );
};

BetaLogoIcon.propTypes = {
  size: PropTypes.number.isRequired
};

export default BetaLogoIcon;
