import React, { createContext, useState, useEffect } from 'react';
import Cookies from 'js-cookie';
import jwtDecode from 'jwt-decode';

export const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);

  useEffect(() => {
    const token = Cookies.get('authToken');

    if (token) {
      try {
        const decodedToken = jwtDecode(token);
        if (decodedToken.exp * 1000 > Date.now()) {
          setUser({
            email: decodedToken.email, 
            name: decodedToken.name, 
            profile_picture: decodedToken.profile_picture, 
            token
          });
        } else {
          Cookies.remove('authToken');
        }
      } catch (error) {
        Cookies.remove('authToken');
      }
    }
  }, []);

  return (
    <UserContext.Provider value={{ user, setUser }}>
      {children}
    </UserContext.Provider>
  );
};
