import React, { useEffect, useContext, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import { Platform, Dimensions } from 'react-native';
import ProfilePictureIcon from './components/navigation/ProfilePictureIcon';
import BetaLogoIcon from './components/navigation/BetaLogoIcon';
import HomeScreen from './screens/HomeScreen';
import NewUploadScreen from './screens/NewUploadScreen';
import SetupMedia from './screens/SetupMedia';
import HelpScreen from './screens/HelpScreen';
import ProfileStack from './navigation/ProfileStack';

import { UserContext, UserProvider } from './context/UserContext';
import Cookies from "js-cookie";
import { View, ActivityIndicator } from "react-native";
import "./global.css"
import Ionicons from '@expo/vector-icons/Ionicons';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import * as SplashScreen from 'expo-splash-screen';
import { MMKV } from 'react-native-mmkv';
import Constants from "expo-constants";

const storage = new MMKV(); // Using MMKV for fast storage
// Prevent splash screen from hiding automatically
SplashScreen.preventAutoHideAsync();



const AuthChecker = ({ children }) => {
  const sub_url = window.location.pathname;

  const searchString = "/authenticate/";
  let auth_email;
  if (sub_url.includes(searchString)) {
    auth_email = sub_url.split(searchString)[1]; // Extracts everything after "/authenticate/"
  }

  const { setUser } = useContext(UserContext);
  const [checkingAuth, setCheckingAuth] = useState(true);
  const [timestamp] = useState(Date.now()); // Add cache-busting timestamp

  useEffect(() => {
    console.log("Cache-busting timestamp:", timestamp);
    const fetchUserData = async (email) => {
      try {
        // Import axios directly for authentication requests to avoid caching issues
        const axios = (await import('axios')).default;

        const response = await axios.get(
          `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/get_sanatana_app_user`,
          {
            params: { email },
          }
        );

        const { refresh_token, name, profile_picture } = response.data;

        // Store JWT token in cookies (valid for 7 days)
        Cookies.set("sanatana_signed_in_email", email, { expires: 36500 });

        // Update global user context
        setUser({ email, name, profile_picture, refresh_token });


        if(auth_email){
          setCheckingAuth(false)
          window.location.href = "/Home";
          return
        }

      } catch (error) {
        console.log("Error", "Failed to authenticate user.");
        // Log more detailed error information
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error("Response data:", error.response.data);
          console.error("Response status:", error.response.status);
          console.error("Response headers:", error.response.headers);
        } else if (error.request) {
          // The request was made but no response was received
          console.error("No response received:", error.request);
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error("Error message:", error.message);
        }
        console.error(error);
      }
      setCheckingAuth(false)
      console.log("App.js --AuthChecker: Going to path", window.location.href);
    };

    if (!auth_email) {
      const sanatana_signed_in_email = Cookies.get("sanatana_signed_in_email");

      if (!sanatana_signed_in_email) {
          window.location.href = `${Constants.expoConfig.extra.SANATANA_SIGNIN_DOMAIN}`;
      }
      else
      {
           fetchUserData(sanatana_signed_in_email);
      }
      setCheckingAuth(false)

    } else {

      fetchUserData(auth_email);
      setCheckingAuth(false)
    }
  }, []);

  if (checkingAuth) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return children;
};



const  aligh_left_screenOptions=  {
  headerShown: false,
  tabBarItemStyle: {
    width: 'auto', // Make tab width fit the text
    paddingHorizontal: 30, // Optional: Adds space around the text
  },
  tabBarLabelStyle: {
    textAlign: 'center', // Centers the label inside each tab
  },
  tabBarStyle: {
    flexDirection: 'row', // Ensure tabs are laid out horizontally
    justifyContent: 'center', // Aligns tabs to the left
  },
  // Disable swipe gestures to prevent interference with text selection
  swipeEnabled: false,
}
const default_screenOptions = {
  headerShown: false,
  // No background color for selected tab
}

const isMobile = Platform.OS === 'android' || Platform.OS === 'ios' || Dimensions.get('window').width < 768;

const Tab = isMobile ? createBottomTabNavigator() : createMaterialTopTabNavigator();

// Define options for Material Top Tab Navigator
const materialTopTabOptions = {
  ...aligh_left_screenOptions,
  // Disable swipe gestures to prevent interference with text selection
  swipeEnabled: false,
  // Use default font configuration to avoid font loading issues
  tabBarLabelStyle: {
    ...aligh_left_screenOptions.tabBarLabelStyle,
    fontFamily: 'System',
  },
  // Remove the blue indicator line
  tabBarIndicatorStyle: {
    height: 0,
  },
  // Add gray background to selected tab
  tabBarItemStyle: {
    ...aligh_left_screenOptions.tabBarItemStyle,
  },
  // Style for the active tab
  tabBarActiveBackgroundColor: '#f3f4f6', // Light gray background for selected tab
};

const screenOptions = isMobile ? default_screenOptions : materialTopTabOptions;

const MobileNavigation = () => {
  // Use the regular Tab Navigator for mobile
  if (isMobile) {
    return (
      <Tab.Navigator screenOptions={screenOptions}>
        <Tab.Screen name='Home' component={HomeScreen}
         options={{
        tabBarLabel: 'Home',
        tabBarIcon: ({ size }) => (
          <BetaLogoIcon size={size} />
        ),
      }}/>
        <Tab.Screen name='SetupMedia' component={SetupMedia}
        options={{ title: 'Setup Media',
          tabBarLabel: 'Setup Media',
          tabBarIcon: ({ size, focused }) => (
            <View style={{
              backgroundColor: focused ? '#4F46E5' : '#E0E7FF',
              borderRadius: size,
              width: size * 1.2,
              height: size * 1.2,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <Ionicons name="settings" size={size * 0.7} color={focused ? 'white' : '#4F46E5'} />
            </View>
          ),
         }} />
        <Tab.Screen name='NewUpload'
        component={NewUploadScreen}
        options={{
          title: 'New Upload',
          tabBarLabel: '',
          tabBarIcon: ({ size }) => (
            <View style={{
              backgroundColor: '#8B4513', // Brown background
              borderRadius: size * 1.8, // Increased size
              width: size * 1.8, // Increased width
              height: size * 1.8, // Increased height
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 5, // Add some space from the bottom
              marginTop: 12, // Add more space from the top
              borderWidth: 3, // Slightly thicker border
              borderColor: '#FFD700', // Yellow border
            }}>
              <Ionicons name="add" size={size * 1.2} color="#00FF00" /> {/* Green plus sign, larger size */}
            </View>
          ),
        }}
        />
        <Tab.Screen name='Help' component={HelpScreen}
        options={{
          tabBarLabel: 'Help',
          tabBarIcon: ({ size, focused }) => (
            <View style={{
              backgroundColor: focused ? '#10B981' : '#D1FAE5',
              borderRadius: size,
              width: size * 1.2,
              height: size * 1.2,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <Ionicons name="help-circle" size={size * 0.7} color={focused ? 'white' : '#10B981'} />
            </View>
          ),
        }}
        />
        <Tab.Screen name='Profile' component={ProfileStack}
        options={{
          tabBarLabel: 'Profile',
          tabBarIcon: ({ color, size, focused }) => (
            <ProfilePictureIcon color={color} size={size} focused={focused} />
          ),
        }}
        />
      </Tab.Navigator>
    );
  }

  // For desktop/web, use the regular Tab Navigator but with swipe disabled
  return (
    <Tab.Navigator screenOptions={{
      ...screenOptions,
      swipeEnabled: false, // Explicitly disable swipe gestures
      tabBarLabelStyle: {
        ...screenOptions.tabBarLabelStyle,
        fontFamily: 'System', // Use system font to avoid font loading issues
      }
    }}>
      <Tab.Screen name='Home' component={HomeScreen}
       options={{
      tabBarLabel: 'Home',
      tabBarIcon: ({ size }) => (
        <BetaLogoIcon size={size} />
      ),
    }}/>
      <Tab.Screen name='SetupMedia' component={SetupMedia}
      options={{ title: 'Setup Media',
        tabBarLabel: 'Setup Media',
        tabBarIcon: ({ size, focused }) => (
          <View style={{
            backgroundColor: focused ? '#4F46E5' : '#E0E7FF',
            borderRadius: size,
            width: size * 1.2,
            height: size * 1.2,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Ionicons name="settings" size={size * 0.7} color={focused ? 'white' : '#4F46E5'} />
          </View>
        ),
       }} />
      <Tab.Screen name='Help' component={HelpScreen}
      options={{
        tabBarLabel: 'Help',
        tabBarIcon: ({ size, focused }) => (
          <View style={{
            backgroundColor: focused ? '#10B981' : '#D1FAE5',
            borderRadius: size,
            width: size * 1.2,
            height: size * 1.2,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Ionicons name="help-circle" size={size * 0.7} color={focused ? 'white' : '#10B981'} />
          </View>
        ),
      }}
      />
      <Tab.Screen name='Profile' component={ProfileStack}
      options={{
        tabBarLabel: 'Profile',
        tabBarIcon: ({ color, size, focused }) => (
          <ProfilePictureIcon color={color} size={size} focused={focused} />
        ),
      }}
      />
    </Tab.Navigator>
  );
};


const AppNavigation = () => {


  useEffect(() => {
    console.log('AppNavigation mounted');

  }, []);

  console.log('AppNavigation rendered at:', Date.now());
  console.log('AppNavigation: isMobile:', isMobile);
  if(isMobile){
    console.log('AppNavigation: Rendering MobileNavigation');
  }else{
    console.log('AppNavigation: Rendering DesktopNavigation');
  }

 return <MobileNavigation />;
};
export default function App() {
  const [appIsReady, setAppIsReady] = useState(false);
  const [initialState, setInitialState] = useState();

  useEffect(() => {
    // Register service worker for web platform
    if (Platform.OS === 'web' && 'serviceWorker' in navigator) {
      // Only register service worker in production
      // or if we're running on localhost (for testing)
      const isLocalhost = window.location.hostname === 'localhost' ||
                          window.location.hostname === '127.0.0.1';
      const isProduction = process.env.NODE_ENV === 'production';

      if (isProduction || isLocalhost) {
        window.addEventListener('load', () => {
          // Use a relative path to avoid MIME type issues
          const swPath = `${window.location.pathname.replace(/\/+$/, '')}/service-worker.js`;

          navigator.serviceWorker.register(swPath)
            .then(registration => {
              console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(error => {
              // Just log the error but don't break the app
              console.error('Service Worker registration failed:', error);
              console.log('Service Worker registration will be skipped. This is normal in development.');
            });
        });
      } else {
        console.log('Service Worker registration skipped in development mode.');
      }
    }

    // Clear expired API cache entries
    try {
      // Import apiCache dynamically to avoid circular dependencies
      import('./utils/apiCache').then(({ clearExpiredApiCache }) => {
        clearExpiredApiCache();
      }).catch(err => {
        console.error('Error clearing expired API cache:', err);
      });
    } catch (error) {
      console.error('Error importing apiCache:', error);
    }

    try {
      const savedState = storage.getString("navState");
      if (savedState) {
        setInitialState(JSON.parse(savedState));
      } else {
        setInitialState({ routes: [{ name: "Home" }] }); // Default to Home
      }
    } catch (error) {
      storage.delete("navState"); // Reset corrupt state
      setInitialState({ routes: [{ name: "Home" }] }); // Default to Home
    }

    async function prepare() {
      try {
        console.log('Preparing app and preloading assets...');

        // Import and preload all icons at once
        const { preloadIcons } = await import('./utils/iconLoader');
        await preloadIcons();

        // API cache is already cleared earlier in the effect

        // Additional async setup can go here
      } catch (e) {
        console.warn('Error during app preparation:', e);
      } finally {
        setAppIsReady(true);
        await SplashScreen.hideAsync();
        console.log('App is ready, splash screen hidden');
      }
    }

    prepare();
  }, []);

  if (!appIsReady) {
    return null; // Keep the splash screen until icons are loaded
  }

  return (
    <UserProvider>
      <NavigationContainer
        initialState={initialState || undefined} // Ensure it doesn't crash if null
        onStateChange={(state) => storage.set("navState", JSON.stringify(state))}
      >
        <AuthChecker>
          <AppNavigation />
        </AuthChecker>
      </NavigationContainer>
    </UserProvider>
  );
}

/*
usage:
npx expo start --web --port 5003
npx expo start --web --clear --port 5003 --no-minify"
npx expo start --web --host https://www.sanatanamedia.com

*/
