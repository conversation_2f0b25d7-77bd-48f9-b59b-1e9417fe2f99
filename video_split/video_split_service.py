from flask import Flask, request, jsonify
import subprocess
import os
import argparse
import json
import sys

app = Flask(__name__)

# Define the script to be called
VIDEO_SPLIT_SCRIPT = "video_split.py"

@app.route('/split_video', methods=['POST'])
def split_video():
    """API endpoint to split a video based on JSON input."""
    data = request.json

    # Validate JSON input
    if 'path' not in data or 'interval' not in data:
        return jsonify({"error": "Missing 'path' or 'interval' in request"}), 400

    video_path = data['path']
    interval = str(data['interval'])  # Convert to string for CLI

    # Check if video file exists
    if not os.path.exists(video_path):
        return jsonify({"error": f"Video file '{video_path}' not found"}), 404

    # Get output directory (same as video location)
    video_dir = os.path.dirname(video_path)
    output_dir = os.path.join(video_dir, interval)

    # If folder already exists, return existing split files
    if os.path.exists(output_dir):
        split_files = [os.path.join(output_dir, f) for f in os.listdir(output_dir) if f.endswith('.mp4')]
        return jsonify({"message": "Splitting skipped (files already exist)", "split_files": split_files})

    # Run the video splitting script
    try:
        result = subprocess.run(
            ['python', VIDEO_SPLIT_SCRIPT, video_path, interval],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True
        )
    except subprocess.CalledProcessError as e:
        return jsonify({"error": "Video splitting failed", "details": e.stderr}), 500

    # Collect generated split files
    if os.path.exists(output_dir):
        split_files = [os.path.join(output_dir, f) for f in os.listdir(output_dir) if f.endswith('.mp4')]
        return jsonify({"message": "Video split successfully", "split_files": split_files})
    else:
        return jsonify({"error": "Splitting completed but no files found"}), 500
def main():
    service_name = "VIDEO_SPLIT"
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description=f"Start the {service_name} microservice.")
    parser.add_argument('--port', type=int, help="Port to run the service on")
    args = parser.parse_args()
    
    # Determine the port: Use CLI argument > Environment variable > Default 
    PORT = args.port or int(os.getenv(service_name, 5005))

    # Get the parent directory of the current script
    parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

    # Add it to sys.path
    sys.path.append(parent_dir)
    
    from Utils.globals import save_port_in_global_ports
    
    # Write back to global ports file
    save_port_in_global_ports(service_name, PORT)
    
    print(f"Starting service {service_name} on port {PORT}...")
    app.run(host="0.0.0.0", port=PORT, threaded=True)
    
if __name__ == '__main__':
    main()
    ...
    """
        usage:
        python video_split_service.py --port 5005
        
        - If the port is specified in command line it will use that port
        - If not, it will look for environment variable, VIDEO_SPLIT,
        - Even if that is not found, then it will assign default port to 5005
        - Saves port to global ports file under key VIDEO_SPLIT, so local client applications can connect
        
    """