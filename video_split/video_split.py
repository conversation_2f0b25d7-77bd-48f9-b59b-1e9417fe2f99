# https://github.com/mtdukes/vidSplit
# Need FFMPEG for this
import subprocess
import datetime
import argparse
import math
import os

def get_video_duration(video_path):
    """Retrieve the total duration of a video file using ffprobe."""
    try:
        result = subprocess.run(
            ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', 
             '-of', 'default=noprint_wrappers=1:nokey=1', video_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return float(result.stdout.strip())  # Convert duration to float (seconds)
    except Exception as e:
        print(f"Error retrieving video duration: {e}")
        return None

def time_split(video_path, interval):
    """Split the video into smaller clips of specified interval and save them in a folder in the same location."""
    
    if not os.path.exists(video_path):
        print(f"Error: Video file '{video_path}' not found.")
        return

    # Get the directory of the input video
    video_dir = os.path.dirname(video_path)

    # Define output directory based on interval (e.g., "C:/Videos/60/")
    output_dir = os.path.join(video_dir, str(interval))
    
    # Check if folder exists, skip splitting if it does
    if os.path.exists(output_dir):
        print(f"Folder '{output_dir}' already exists. Skipping split.")
        return
    
    # Create the output folder
    os.makedirs(output_dir)
    
    video_duration = get_video_duration(video_path)
    if video_duration is None:
        print("Failed to retrieve video duration. Exiting.")
        return
    
    loop_num = math.ceil(video_duration / float(interval))
    
    for i in range(loop_num):
        position = str(datetime.timedelta(seconds=(interval * i)))  # Start time
        new_file = os.path.join(output_dir, f"split_{i}.mp4")  # Save inside the interval folder

        # Construct FFmpeg command
        command = [
            'ffmpeg', '-ss', position, '-t', str(interval), '-i', video_path,
            '-acodec', 'copy', '-vcodec', 'copy', new_file
        ]
        
        # Run FFmpeg command
        try:
            subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, text=True)
            print(f"Created: {new_file}")
        except subprocess.CalledProcessError as e:
            print(f"Error splitting video at {position}: {e.stderr}")
def main():
    parser = argparse.ArgumentParser(description='Split video into smaller clips and store them in an interval-based folder.')
    parser.add_argument('path', help='Path to the video file (.mp4)')
    parser.add_argument('interval', type=int, help='Clip duration in seconds')

    args = parser.parse_args()
    
    time_split(args.path, args.interval)

if __name__ == '__main__':
    #main()
    ...

    """
    python video_split.py "C:/Users/<USER>/Downloads/SanantaDharma/Science Behind Vedic Culture.mp4" 60 
    
    60 - for 1 minute -- FB Reels / Story
    180 - for 3 minutes -- Youtube Shorts
    840 - for 14 minutes -- Youtube free account

    """