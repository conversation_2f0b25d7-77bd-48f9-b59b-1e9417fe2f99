# Sanatana Files Service

A dedicated microservice for handling large file uploads (up to 300MB) for the Sanatana Media platform.

## Features

- Chunked upload protocol for efficient handling of large files
- Asynchronous processing with background queue
- Progress tracking for uploads
- Retry mechanism for failed uploads
- Integration with main Sanatana service for upload tracking

## API Endpoints

### POST /upload_file

Handles file uploads with chunking support.

**Parameters:**
- `sanatana_email`: Email of the user
- `file`: The file to upload
- `chunk`: Current chunk number (0-based)
- `chunks`: Total number of chunks
- `filename`: Original filename
- `upload_id`: ID for resuming uploads (optional)

**Response:**
```json
{
  "success": true,
  "upload_id": "uuid-string",
  "db_id": 123,
  "chunk": 0,
  "chunks": 10
}
```

### GET /upload_progress/<upload_id>

Get the progress of an upload.

**Response:**
```json
{
  "filename": "example.mp4",
  "progress": 45.5,
  "status": "In Progress",
  "time_start": **********,
  "time_end": null,
  "db_id": 123,
  "folder_path": "/path/to/upload/dir"
}
```

### POST /retry_upload/<upload_id>

Retry a failed upload.

**Response:**
```json
{
  "success": true
}
```

### GET /health

Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "queue_size": 2,
  "active_uploads": 5
}
```

## Configuration

The service reads configuration from `../sanatana domains file` and expects the following keys:
- `SANATANA_FILES_LOCATION`: Path where uploaded files will be stored
- `SANATANA_SERVICE_DOMAIN`: URL of the main Sanatana service

## Running the Service

### Using Docker

```bash
docker build -t sanatana-files .
docker run -p 5001:5001 -v /path/to/uploads:/data/uploads sanatana-files
```

### Without Docker

```bash
pip install -r requirements.txt
python app.py
```
Summary of Implementation
 A comprehensive solution for handling large file uploads in the Sanatana Media platform. Here's a summary of what I've implemented:

1. Sanatana Files Service ( sanatana_files/app.py)
A dedicated microservice for handling file uploads up to 300MB
Implements chunked upload protocol for efficient handling of large files
Uses a background queue for asynchronous processing
Provides progress tracking and retry mechanisms
Integrates with the main Sanatana service for upload tracking
Key endpoints:

POST /upload_file: Handles chunked file uploads
GET /upload_progress/<upload_id>: Tracks upload progress
POST /retry_upload/<upload_id>: Retries failed uploads
GET /health: Health check endpoint
2. Main Service Update ( youtube_auth_service_update.py)
Adds a new uploads_folders table to track file uploads
Implements the /update_file_upload_info endpoint to record upload status
Adds endpoints to retrieve upload information
Key endpoints:

POST /update_file_upload_info: Records upload status
GET /get_file_upload_info/<upload_id>: Gets details of a specific upload
GET /get_user_file_uploads: Lists all uploads for a user
3. Client-Side Implementation ( sanatana-app/utils/fileUpload.js)
Provides a React Native utility for chunked file uploads
Handles progress tracking and error handling
Supports cancellation of uploads
Works on both web and native platforms
4. FileUploadInput Component Update
Integrates with the new file upload utility
Displays upload progress with a progress bar
Handles upload errors and cancellation
Stores the upload ID in the wizard data
5. Docker Compose Configuration
Sets up both services to run together
Configures networking between services
Mounts volumes for persistent storage
Architecture Benefits
This implementation follows the architecture we discussed:

Upload Service: The  sanatana_files service handles file reception and storage
Processing Service: Background processing is handled by a queue in the  sanatana_files service
Notification Service: The main service is updated via the /update_file_upload_info endpoint
The solution provides:

Resource Isolation: Large file uploads won't impact the main service
Scalability: Each service can be scaled independently
Reliability: Failed uploads can be retried
User Experience: Progress tracking keeps users informed
Deployment Instructions
Copy  sanatana domains file.example to  sanatana_domains.json and update as needed
Run docker-compose up -d to start both services
Integrate the client-side code into your React Native app
Update the SANATANA_FILES_SERVICE constant in your app configuration
This implementation provides a robust solution for handling large file uploads while keeping your main service responsive and available for critical operations.