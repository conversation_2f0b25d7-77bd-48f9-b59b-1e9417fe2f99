from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
import time
import datetime
import uuid
import requests
import logging
from werkzeug.utils import secure_filename
from pathlib import Path
import threading
import queue

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sanatana_files.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

import sys
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from Utils.globals import determine_domains_file
GLOBAL_DOMAINS_FILE = determine_domains_file()

# Load configuration
try:
    with open(f'../{GLOBAL_DOMAINS_FILE}', 'r') as f:
        config = json.load(f)
    SANATANA_FILES_LOCATION = config.get('SANATANA_FILES_LOCATION')
    SANATANA_SERVICE_DOMAIN = config.get('SANATANA_SERVICE_DOMAIN')
    
    if not SANATANA_FILES_LOCATION or not SANATANA_SERVICE_DOMAIN:
        raise ValueError("Missing required configuration values")
        
    logger.info(f"Files will be stored in: {SANATANA_FILES_LOCATION}")
    logger.info(f"Main service domain: {SANATANA_SERVICE_DOMAIN}")
    
except Exception as e:
    logger.error(f"Failed to load configuration: {str(e)}")
    raise

# Create upload directory if it doesn't exist
os.makedirs(SANATANA_FILES_LOCATION, exist_ok=True)

# Queue for background processing
upload_queue = queue.Queue()

# Dictionary to store upload progress
upload_progress = {}

def get_timestamp_folder_name():
    """Generate a timestamp folder name that's compatible with both Unix/Linux and Windows"""
    return datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

def update_upload_info(sanatana_email, folder_path, source, status, time_start, time_end=None):
    """Update the upload information in the main service"""
    try:
        url = f"{SANATANA_SERVICE_DOMAIN}/update_file_upload_info"
        payload = {
            "sanatana_email": sanatana_email,
            "folder_path": folder_path,
            "source": source,
            "status": status,
            "time_start": time_start,
            "time_end": time_end
        }
        
        response = requests.post(url, json=payload)
        
        if response.status_code == 200:
            return response.json().get('id')
        else:
            logger.error(f"Failed to update upload info: {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"Error updating upload info: {str(e)}")
        return None

def process_upload_queue():
    """Background thread to process the upload queue"""
    while True:
        try:
            # Get an item from the queue
            task = upload_queue.get()
            
            if task is None:  # Sentinel value to stop the thread
                break
                
            file_path = task['file_path']
            temp_path = task['temp_path']
            sanatana_email = task['sanatana_email']
            upload_id = task['upload_id']
            chunk_size = task['chunk_size']
            total_chunks = task['total_chunks']
            
            # Process the file (move from temp to final location)
            try:
                # Ensure the directory exists
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                # Move the file from temp to final location
                with open(file_path, 'wb') as f_out:
                    for i in range(total_chunks):
                        chunk_path = f"{temp_path}.part{i}"
                        if os.path.exists(chunk_path):
                            with open(chunk_path, 'rb') as f_in:
                                f_out.write(f_in.read())
                            # Update progress
                            upload_progress[upload_id]['progress'] = (i + 1) / total_chunks * 100
                            # Remove the chunk file
                            os.remove(chunk_path)
                
                # Update status to complete
                time_end = int(time.time())
                update_upload_info(
                    sanatana_email=sanatana_email,
                    folder_path=os.path.dirname(file_path),
                    source="File Upload",
                    status="Upload Complete",
                    time_start=upload_progress[upload_id]['time_start'],
                    time_end=time_end
                )
                
                upload_progress[upload_id]['status'] = "Complete"
                upload_progress[upload_id]['progress'] = 100
                upload_progress[upload_id]['time_end'] = time_end
                
                logger.info(f"Upload complete for {file_path}")
                
            except Exception as e:
                logger.error(f"Error processing upload: {str(e)}")
                upload_progress[upload_id]['status'] = "Failed"
                upload_progress[upload_id]['error'] = str(e)
                
                # Update status to failed
                update_upload_info(
                    sanatana_email=sanatana_email,
                    folder_path=os.path.dirname(file_path),
                    source="File Upload",
                    status=f"Upload Failed: {str(e)}",
                    time_start=upload_progress[upload_id]['time_start'],
                    time_end=int(time.time())
                )
            
            finally:
                # Clean up any remaining temp files
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                
                # Mark the task as done
                upload_queue.task_done()
                
        except Exception as e:
            logger.error(f"Error in upload queue processor: {str(e)}")

# Start the background thread
upload_thread = threading.Thread(target=process_upload_queue, daemon=True)
upload_thread.start()

@app.route('/upload_file', methods=['POST'])
def upload_file():
    """
    Handle file uploads with chunking support
    
    Expected parameters:
    - sanatana_email: Email of the user
    - file: The file to upload
    - chunk: Current chunk number (0-based)
    - chunks: Total number of chunks
    - filename: Original filename
    - upload_id: ID for resuming uploads (optional)
    """
    try:
        # Get parameters
        sanatana_email = request.form.get('sanatana_email')
        chunk = int(request.form.get('chunk', 0))
        chunks = int(request.form.get('chunks', 1))
        filename = request.form.get('filename')
        upload_id = request.form.get('upload_id')
        
        # Validate required parameters
        if not sanatana_email or not filename:
            return jsonify({"error": "Missing required parameters"}), 400
            
        # Secure the filename
        filename = secure_filename(filename)
        
        # Create user directory if it doesn't exist
        user_dir = os.path.join(SANATANA_FILES_LOCATION, sanatana_email)
        os.makedirs(user_dir, exist_ok=True)
        
        # If this is the first chunk, create a new upload record
        if chunk == 0 and not upload_id:
            # Create timestamp directory
            timestamp_dir = get_timestamp_folder_name()
            upload_dir = os.path.join(user_dir, timestamp_dir)
            os.makedirs(upload_dir, exist_ok=True)
            
            # Generate a unique ID for this upload
            upload_id = str(uuid.uuid4())
            
            # Record the start time
            time_start = int(time.time())
            
            # Update the upload info in the main service
            db_id = update_upload_info(
                sanatana_email=sanatana_email,
                folder_path=upload_dir,
                source="File Upload",
                status="Started upload",
                time_start=time_start
            )
            
            if not db_id:
                return jsonify({"error": "Failed to register upload"}), 500
                
            # Initialize progress tracking
            upload_progress[upload_id] = {
                'filename': filename,
                'progress': 0,
                'status': 'In Progress',
                'time_start': time_start,
                'time_end': None,
                'db_id': db_id,
                'folder_path': upload_dir
            }
        
        # If we're resuming an upload, verify it exists
        elif upload_id and upload_id not in upload_progress:
            return jsonify({"error": "Invalid upload ID"}), 400
            
        # Get the upload directory from progress tracking
        upload_dir = upload_progress[upload_id]['folder_path']
        
        # Define the final file path and temporary path
        file_path = os.path.join(upload_dir, filename)
        temp_path = os.path.join(upload_dir, f"{filename}.temp")
        
        # Save the chunk
        chunk_file = request.files['file']
        chunk_path = f"{temp_path}.part{chunk}"
        chunk_file.save(chunk_path)
        
        # Update progress
        upload_progress[upload_id]['progress'] = (chunk + 1) / chunks * 90  # Up to 90% for upload
        
        # If this is the last chunk, queue the file for processing
        if chunk == chunks - 1:
            upload_queue.put({
                'file_path': file_path,
                'temp_path': temp_path,
                'sanatana_email': sanatana_email,
                'upload_id': upload_id,
                'chunk_size': os.path.getsize(chunk_path),
                'total_chunks': chunks
            })
            
        return jsonify({
            "success": True,
            "upload_id": upload_id,
            "db_id": upload_progress[upload_id]['db_id'],
            "chunk": chunk,
            "chunks": chunks
        })
        
    except Exception as e:
        logger.error(f"Error handling upload: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/upload_progress/<upload_id>', methods=['GET'])
def get_upload_progress(upload_id):
    """Get the progress of an upload"""
    if upload_id not in upload_progress:
        return jsonify({"error": "Upload not found"}), 404
        
    return jsonify(upload_progress[upload_id])

@app.route('/retry_upload/<upload_id>', methods=['POST'])
def retry_upload(upload_id):
    """Retry a failed upload"""
    if upload_id not in upload_progress:
        return jsonify({"error": "Upload not found"}), 404
        
    if upload_progress[upload_id]['status'] != "Failed":
        return jsonify({"error": "Upload is not in failed state"}), 400
        
    # Reset the upload status
    upload_progress[upload_id]['status'] = "Retrying"
    upload_progress[upload_id]['error'] = None
    
    # Queue the file for processing again
    upload_queue.put({
        'file_path': os.path.join(
            upload_progress[upload_id]['folder_path'], 
            upload_progress[upload_id]['filename']
        ),
        'temp_path': os.path.join(
            upload_progress[upload_id]['folder_path'], 
            f"{upload_progress[upload_id]['filename']}.temp"
        ),
        'sanatana_email': upload_progress[upload_id]['sanatana_email'],
        'upload_id': upload_id,
        'chunk_size': 0,  # Will be recalculated
        'total_chunks': 0  # Will be recalculated
    })
    
    return jsonify({"success": True})

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "queue_size": upload_queue.qsize(),
        "active_uploads": len(upload_progress)
    })


def main():
    import argparse
    import sys
    # Service name
    service_name = "SANATANA_FILES"

    # Parse command-line arguments for port
    parser = argparse.ArgumentParser(
        description=f"Start the {service_name} microservice."
    )
    parser.add_argument("--port", type=int, help="Port to run the service on")
    args = parser.parse_args()

    # Determine the port: Use CLI argument > Environment variable > Default
    PORT = args.port or int(os.getenv(service_name, 5004))

    # Get the parent directory of the current script
    parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

    # Add it to sys.path
    sys.path.append(parent_dir)

    from Utils.globals import save_port_in_global_ports

    # Write back to global ports file
    save_port_in_global_ports(service_name, PORT)

    print(f"Starting service {service_name} on port {PORT}...")
    app.run(host="0.0.0.0", port=PORT, threaded=True)
    """
        usage:
        python sanatana_files.py --port 5004

        - Caution: this port is registered with Google Console Web Credentials. If you change it you
        must change there also

        - If the port is specified in command line it will use that port
        - If not, it will look for environment variable, SANATANA_FILES,
        - Even if that is not found, then it will assign default port to 5004
        - Saves port to global ports file under key SANATANA_FILES, so local client applications can connect

    """
if __name__ == '__main__':
   main()
