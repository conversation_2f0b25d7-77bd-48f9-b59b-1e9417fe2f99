# Reload necessary libraries
import re

# Function to extract transcription text from SRT content
def extract_transcription(srt_file_path):
    with open(srt_file_path, "r", encoding="utf-8") as file:
        srt_content = file.readlines()

    transcription = []
    for line in srt_content:
        # Ignore timestamps and numbering
        if not re.match(r"^\d+$", line.strip()) and not re.match(r"^\d{2}:\d{2}:\d{2},\d{3}", line.strip()):
            transcription.append(line.strip())

    return " ".join(transcription)

# Extract transcription from the Hindi SRT file
hindi_srt_file_path = r"C:\Users\<USER>\Downloads\Great News for Indian Society. No more, Muslim WAQF board stealing all of India's land!!.srt"
hindi_transcription = extract_transcription(hindi_srt_file_path)

# Save extracted Hindi transcription
hindi_transcription_file_path = hindi_srt_file_path + "_Extracted.txt"
with open(hindi_transcription_file_path, "w", encoding="utf-8") as file:
    file.write(hindi_transcription)

