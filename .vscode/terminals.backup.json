{"autorun": true, "terminals": [{"name": "svc", "commands": ["cd Sanatana_Service ", "source env_svc/bin/activate ", "service.sh "], "open": true, "shellPath": "bash"}, {"name": "app", "commands": ["cd sanatana-app ", "service.sh "], "open": true, "shellPath": "bash"}, {"name": "signin", "commands": ["cd Sanatana-Signin ", "service.sh "], "open": true, "shellPath": "bash"}, {"name": "scheduler", "commands": ["cd selection_jobs ", "source env_jobs/bin/activate ", "service_scheduler.sh "], "open": true, "shellPath": "bash"}, {"name": "jobstatus", "commands": ["cd selection_jobs ", "source env_jobs/bin/activate ", "service_jobstatus.sh "], "open": true, "shellPath": "bash"}, {"name": "yt_down", "commands": ["cd youtube_download ", "source env_yt_down/bin/activate ", "service.sh "], "open": true, "shellPath": "bash"}, {"name": "yt_upload", "commands": ["cd youtube_upload ", "source env_yt_upload/bin/activate ", "service.sh "], "open": true, "shellPath": "bash"}, {"name": "vidsplit", "commands": ["cd video_split ", "source env_split/bin/activate ", "service.sh "], "open": true, "shellPath": "bash"}, {"name": "files", "commands": ["cd sanatana_files ", "source env_files/bin/activate ", "service.sh "], "open": true, "shellPath": "bash"}, {"name": "admindashboard", "commands": ["cd selection_jobs ", "source env_jobs/bin/activate "], "open": true, "shellPath": "bash"}]}