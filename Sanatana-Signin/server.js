const express = require("express");
const cors = require("cors");
const { OAuth2Client } = require("google-auth-library");
const axios = require("axios"); 
const fs = require("fs");
const path = require("path");
const { minify } = require('html-minifier');

const app = express();
const CLIENT_ID = "657812484376-ti00b9vkenkc0m0s2lnrgpdpo1ko42j5.apps.googleusercontent.com";
const CLIENT_SECRET = "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";
const client = new OAuth2Client(CLIENT_ID);

app.use(express.json());
app.use(cors());
app.use(express.static("public")); // Serve frontend files

function determinePortsFile() {
    const cwd = process.cwd();
    if (cwd.toLowerCase().includes('socialmediaupload_dev')) {
      return 'global_ports.dev.json';
    } else {
      return 'global_ports.json';
    }
  }

function determineDomainsFile() {
    const cwd = process.cwd();
    if (cwd.toLowerCase().includes('socialmediaupload_dev')) {
      return 'sanatana_domains.dev.json';
    } else {
      return 'sanatana_domains.json';
    }
  }
  
const GLOBAL_PORTS_FILE = determinePortsFile();
const GLOBAL_DOMAINS_FILE = determineDomainsFile();

// Load domains from JSON (one level above)
const domainsPath = path.join(__dirname, "..", GLOBAL_DOMAINS_FILE);
let domains = {};

try {
  const rawData = fs.readFileSync(domainsPath, "utf8");
  domains = JSON.parse(rawData);
  console.log("domains:", domains)
} catch (error) {
  console.error("Failed to load sanatana_domains.json:", error);
  process.exit(1); // Stop the server if domains can't be loaded
}

const service_name = "SANATANA_SIGNIN"

// Get port from Command Line, Env Variable, or Default to 5002
const PORT = process.argv[2] || process.env[service_name] || 5002;

// Path to `global_ports.json` (one level above)
const GLOBAL_PORTS_PATH = path.join(__dirname, "..", GLOBAL_PORTS_FILE);



// Function to Update `global ports file`
function updateGlobalPorts(port) {
    let globalPorts = {};

    // Read existing JSON file if it exists
    if (fs.existsSync(GLOBAL_PORTS_PATH)) {
        try {
            globalPorts = JSON.parse(fs.readFileSync(GLOBAL_PORTS_PATH, "utf8"));
        } catch (error) {
            console.error("Failed to parse global ports file:", error);
        }
    }

    // Update the key `SANATANA_SIGNIN` with the new port
    globalPorts[service_name] = parseInt(port);

    // Write back to the file
    try {
        fs.writeFileSync(GLOBAL_PORTS_PATH, JSON.stringify(globalPorts, null, 4));
        console.log(`Updated global ports file: SANATANA_SIGNIN=${port}`);
    } catch (error) {
        console.error("Failed to update global ports file:", error);
    }
}

//Update `global ports file` before starting the server
updateGlobalPorts(PORT);

// Function to exchange access token for refresh token
async function get_auth_tokens(auth_code) {
    try {
        const response = await axios.post("https://oauth2.googleapis.com/token", null, {
            params: {
                client_id: CLIENT_ID,
                client_secret: CLIENT_SECRET,
                grant_type: "authorization_code",
                code: auth_code,
                redirect_uri: `${domains.SANATANA_SIGNIN_DOMAIN}`
            }
        });

        return response.data;
    } catch (error) {
        console.error("getRefreshToken: Failed to get refresh token:", error.response ? error.response.data : error);
        return null;
    }
}

// Serve index.html and inject domains dynamically
app.get("/", (req, res) => {
    console.log(" In the / endpoint ")
    let indexPath = path.join(__dirname, "public", "default_index.html");
    let indexHtml = fs.readFileSync(indexPath, "utf8");
  
    // Replace placeholders with actual domains
    indexHtml = indexHtml
      .replace("{{SANATANA_SERVICE_DOMAIN}}", domains.SANATANA_SERVICE_DOMAIN)
      .replace("{{SANATANA_SIGNIN_DOMAIN}}", domains.SANATANA_SIGNIN_DOMAIN)
      .replace("{{SANATANA_APP_DOMAIN}}", domains.SANATANA_APP_DOMAIN);
    
      const minifiedHtml = minify(indexHtml, {
        removeComments: true,
        collapseWhitespace: true,
        removeAttributeQuotes: true,
        minifyJS: true,
        minifyCSS: true,
      });
     // console.log("Final minifiedHtml.html: ",  minifiedHtml)
   // res.send(indexHtml);
    res.send(minifiedHtml);
  });

// Verify Google ID Token
app.post("/verify-token", async (req, res) => {
    const { id_token, auth_code } = req.body;

    try {
        
        if (!auth_code) {
            return res.status(400).json({ success: false, error: "Missing authorization code" });
        }

        const ticket = await client.verifyIdToken({
            idToken: id_token,
            audience: CLIENT_ID,
        });

        const payload = ticket.getPayload();

         // Retrieve refresh token using access token
         const auth_tokens = await get_auth_tokens(auth_code);
         
        // console.log("auth_tokens:", auth_tokens)

         if (!auth_tokens || !auth_tokens.access_token ) {
            return res.status(401).json({ success: false, error: "Failed to retrieve tokens" });
        }

        //console.log("access_token: ", auth_tokens.access_token)
       // console.log("refresh_token: ", auth_tokens.refresh_token)
        

        const userData = {
          name: payload.name,
          email: payload.email,
          picture: payload.picture,
          access_token: auth_tokens.access_token,
          refresh_token: auth_tokens.refresh_token,
        };

        try {
        // Forward user data to `insert_sanatana_app_user`
        const ret_update = await axios.post(`${domains.SANATANA_SERVICE_DOMAIN}/insert_sanatana_app_user`, userData);
    
        //console.log("ret_update Sanatana User: ",  ret_update)
         }
        catch(e)
        {
            console.log("Sanatana user update failed: ", e)
        }

        res.json({
          success: true,
          email: payload.email,
        });

    } catch (error) {
        res.status(401).json({ success: false, error: "Invalid token" });
    }
});

// Start Server
app.listen(PORT, () => console.log(`Service ${service_name} running on ${PORT}`));


/*
usage:

node server.js 5002
 Get port from Command Line, Env Variable, or Default to 5002
*/