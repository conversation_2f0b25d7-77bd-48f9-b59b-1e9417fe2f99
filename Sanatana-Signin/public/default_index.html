<!DOCTYPE html>
<html lang="en">
<head>
   
  <meta charset="UTF-8">
  <title>Sanatana Sign-In</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      margin: 0;
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .container {
      background: white;
      padding: 40px;
      border-radius: 20px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      text-align: center;
      max-width: 450px;
      width: 90%;
      animation: fadeIn 0.6s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: scale(0.95); }
      to { opacity: 1; transform: scale(1); }
    }

    .logo {
      width: 170px;
      height: 170px;
      border-radius: 50%;
      object-fit: cover;
      margin-bottom: 25px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    h1 {
      color: #333;
      font-size: 28px;
      margin-bottom: 25px;
    }
    .small-text {
      font-size: 1.5rem;
      color: #555;
    }
    .highlight {
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(-45deg, #ff4b5c, #6a4c93, #3f72af, #f49e4c);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: hueShift 6s ease infinite;
      margin: 0.2em 0;
    }

    @keyframes hueShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }
    .g_id_signin {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  </style>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
    <div class="container">
        <img src="sanatana_logo.jpg" alt="Sanatana Logo" class="logo">
        <h1>  <span class="small-text">Join</span><br/>
            <span class="highlight">Sanatana Media</span><br/>
            <span class="small-text">with Google</span></h1>
    
        <div id="g_id_onload"
             data-client_id="************-ti00b9vkenkc0m0s2lnrgpdpo1ko42j5.apps.googleusercontent.com"
             data-callback="handleCredentialResponse"
             data-scope="openid email profile https://www.googleapis.com/auth/youtube https://www.googleapis.com/auth/userinfo.email"
             data-ux_mode="popup"
             data-auto_prompt="false"
             data-access_type="offline">
        </div>
    
        <div class="g_id_signin" data-type="standard"></div>
      </div>
    <script>
     

        function handleCredentialResponse(response) {
            console.log("Encoded JWT ID token: " + response.credential);

            const googleAuthURL = `https://accounts.google.com/o/oauth2/auth?`
            + `client_id=************-ti00b9vkenkc0m0s2lnrgpdpo1ko42j5.apps.googleusercontent.com`
            + `&scope=openid email profile https://www.googleapis.com/auth/userinfo.email`
            + `&redirect_uri=${window.location.origin}`
            + `&state=${response.credential}`
            + `&response_type=code`
            + `&access_type=offline`
            + `&prompt=consent`

            window.location.href = googleAuthURL

       
        }

    // ✅ Function to extract URL parameters
    function getURLFragmentParams() {
        const params = {};
        if (window.location.hash) {
            window.location.hash.substring(1).split("&").forEach(param => {
                const [key, value] = param.split("=");
                params[key] = decodeURIComponent(value);
            });
        }
        // ✅ If no hash, check query string (?code=...)
        else if (window.location.search) {
            window.location.search.substring(1).split("&").forEach(param => {
                const [key, value] = param.split("=");
                params[key] = decodeURIComponent(value);
            });
        }
        return params;
    }

    // ✅ Check if the page was redirected with `access_token`
    window.onload = function () {
        console.log("window.onload: ")
            const params = getURLFragmentParams();

            console.log("window.onload: params:", JSON.stringify(params))

            if (params.code && params.state) {
                const auth_code = params.code
                const id_token =  params.state
                // ✅ Send tokens to the backend for verification
                fetch("/verify-token", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ auth_code: auth_code, id_token: id_token })
                })
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                       window.location.href = `{{SANATANA_APP_DOMAIN}}/authenticate/${data.email}`
                    } else {
                        alert("Login failed!");
                    }
                })
                .catch(error => console.error("Error:", error));
            }
           
        };
    </script>
</body>
</html>
