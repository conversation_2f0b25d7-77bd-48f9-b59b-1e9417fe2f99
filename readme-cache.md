# Caching System Documentation

This document provides comprehensive details about the caching mechanisms implemented in both the Sanatana Media app (React/Expo) and backend services. It explains how caching affects operations, debugging techniques, and precautions to take.

## Table of Contents

1. [Overview](#overview)
2. [Client-Side Caching (React/Expo App)](#client-side-caching-reactexpo-app)
   - [API Response Caching](#api-response-caching)
   - [Icon Preloading](#icon-preloading)
   - [Service Worker Caching](#service-worker-caching)
3. [Server-Side Caching (Backend Services)](#server-side-caching-backend-services)
   - [Main Service Caching](#main-service-caching)
   - [Job Status Service Caching](#job-status-service-caching)
4. [Debugging Caching Issues](#debugging-caching-issues)
   - [Client-Side Debugging](#client-side-debugging)
   - [Server-Side Debugging](#server-side-debugging)
5. [Disabling Caching for Debugging](#disabling-caching-for-debugging)
6. [Caching Analysis Tools](#caching-analysis-tools)
7. [Precautions and Best Practices](#precautions-and-best-practices)
8. [Frequently Asked Questions](#frequently-asked-questions)

## Overview

The caching system is designed to improve performance by:

1. Reducing network requests by caching API responses
2. Preloading icons to prevent sequential loading
3. Caching static assets via service workers
4. Adding appropriate cache headers to server responses

Each caching mechanism can be individually enabled/disabled for debugging purposes.

## Client-Side Caching (React/Expo App)

### API Response Caching

**Location**: `sanatana-app/utils/apiCache.js` and `sanatana-app/utils/cachedAxios.js`

**Description**:
The app implements a localStorage-based caching system for API responses. This reduces network requests and improves performance for frequently accessed data.

**How it works**:
- API responses are stored in localStorage with expiration times
- Subsequent requests for the same URL use cached data if available and not expired
- Cache size is limited to prevent memory issues (default: 50 entries)
- Cache entries expire after a configurable duration (default: 5 minutes)

**Configuration Options**:
```javascript
// In apiCache.js
const MAX_CACHE_SIZE = 50; // Maximum number of cached responses
const DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
```

**How to disable**:
```javascript
// When making API requests, set enableCache to false
const response = await cachedAxios.get('/some-endpoint', { enableCache: false });
```

**Endpoints that are NOT cached by default**:
- Authentication endpoints
- User profile endpoints
- Any POST, PUT, DELETE requests

### Icon Preloading

**Location**: `sanatana-app/utils/iconLoader.js`

**Description**:
Icons are preloaded at app startup to prevent sequential loading, which causes the visible delay where icons appear one after another.

**How it works**:
- A list of all used Ionicons is defined
- All icons are loaded at once during app initialization
- App only renders after icons are loaded

**How to disable**:
Comment out the preloadIcons call in App.js:
```javascript
// Comment this line to disable icon preloading
// await preloadIcons();
```

### Service Worker Caching

**Location**: `sanatana-app/web/service-worker.js`

**Description**:
A service worker caches static assets (JS, CSS, images) for offline use and faster loading.

**How it works**:
- Static assets are cached on install
- Requests for cached assets are served from cache
- Dynamic content (API responses) is not cached by the service worker

**Cache duration**:
- Static assets (JS, CSS, images): 1 week
- HTML files: 5 minutes

**How to disable**:
In App.js, comment out the service worker registration:
```javascript
// Comment these lines to disable service worker
// if (Platform.OS === 'web' && 'serviceWorker' in navigator) {
//   window.addEventListener('load', () => {
//     navigator.serviceWorker.register('/service-worker.js')
//       .then(registration => {
//         console.log('Service Worker registered with scope:', registration.scope);
//       })
//       .catch(error => {
//         console.error('Service Worker registration failed:', error);
//       });
//   });
// }
```

## Server-Side Caching (Backend Services)

### Main Service Caching

**Location**: `Sanatana_Service/root_app.py` and `Sanatana_Service/cache_middleware.py`

**Description**:
The main service adds cache headers to responses to enable browser and CDN caching.

**How it works**:
- Cache headers are added to all responses
- Different cache durations are set based on content type and endpoint
- ETag headers are added for efficient cache validation

**Cache durations**:
- Static assets (JS, CSS, images): 1 week
- GET API responses (read-only): 5 minutes
- Authentication endpoints: No caching

**How to disable**:
In `root_app.py`, comment out the `add_cache_headers` function or modify it to return responses without cache headers:
```python
@app.after_request
def add_cache_headers(response):
    # Uncomment this line to disable caching
    # response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    # return response

    # Original caching logic...
```

### Job Status Service Caching

**Location**: `selection_jobs/job_status_service.py`

**Description**:
The job status service adds cache headers to responses, with special handling for job status endpoints.

**How it works**:
- Cache headers are added to all responses
- Job status endpoints are not cached (always fresh)
- Static assets are cached aggressively

**Cache durations**:
- Static assets: 1 week
- GET API responses (except job status): 1 minute
- Job status endpoints: No caching

**How to disable**:
Similar to the main service, modify the `add_cache_headers` function in `job_status_service.py`.

## Debugging Caching Issues

### Client-Side Debugging

#### 1. Browser Developer Tools

**Network Tab**:
- Open browser developer tools (F12)
- Go to Network tab
- Look for responses with status code 304 (Not Modified) or (from cache)
- Check "Disable cache" to bypass browser cache during debugging

**Application Tab**:
- Go to Application > Storage > Local Storage
- Look for entries with keys starting with `sanatana_api_cache`
- You can delete these entries to clear the API cache

**Service Workers**:
- Go to Application > Service Workers
- You can unregister service workers here

#### 2. In-App Debugging

**Console Logging**:
The caching system logs cache hits and misses:
```
Using cached response for: /api/endpoint
Fetching from network: /api/endpoint
```

**Cache Clearing Functions**:
```javascript
// Import the cache utilities
import { clearApiCache, clearExpiredApiCache } from './utils/apiCache';

// Clear the entire API cache
clearApiCache();

// Clear only expired entries
clearExpiredApiCache();
```

### Server-Side Debugging

#### 1. Inspect Response Headers

Use browser developer tools or tools like Postman to inspect response headers:
- `Cache-Control`: Shows caching directives
- `ETag`: Used for cache validation
- `Vary`: Indicates how responses are varied

#### 2. Server Logs

The server logs cache-related information:
- Cache hits/misses
- Cache invalidation events

## Disabling Caching for Debugging

### Quick Disable Options

#### Client-Side:

1. **Disable API Caching Globally**:
   Edit `cachedAxios.js` and set the default enableCache to false:
   ```javascript
   export const createCachedAxios = (options = {}) => {
     const {
       enableCache = false, // Change this to false to disable caching
       cacheDuration = 5 * 60 * 1000,
     } = options;
     // ...
   };
   ```

2. **Disable Service Worker**:
   In Chrome DevTools > Application > Service Workers, click "Unregister"

#### Server-Side:

1. **Add No-Cache Header to All Responses**:
   In `root_app.py` and `job_status_service.py`, modify the `add_cache_headers` function:
   ```python
   @app.after_request
   def add_cache_headers(response):
       # Force no caching for all responses
       response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
       response.headers['Pragma'] = 'no-cache'
       response.headers['Expires'] = '0'
       return response
   ```

2. **Browser Force Refresh**:
   - Chrome/Firefox: Ctrl+F5 or Shift+F5
   - Safari: Cmd+Option+R

## Caching Analysis Tools

### 1. Browser Developer Tools

- **Chrome Lighthouse**: Analyzes caching effectiveness
  - Open DevTools > Lighthouse tab
  - Run an audit with "Performance" checked

- **Chrome Network Panel**:
  - Filter by "cached" to see cached resources
  - Look at "Size" column to see "(from cache)" or "304"

### 2. External Tools

- **WebPageTest** (https://www.webpagetest.org/):
  - Provides detailed caching analysis
  - Shows repeat view performance improvements

- **Postman**:
  - Good for testing API caching
  - Can inspect response headers

### 3. Custom Monitoring

The app includes a simple cache monitoring system:
```javascript
// In the browser console
window.monitorCache = {
  hits: 0,
  misses: 0,
  stats() {
    return {
      hits: this.hits,
      misses: this.misses,
      hitRate: this.hits / (this.hits + this.misses) * 100 + '%'
    };
  }
};

// Check cache performance
window.monitorCache.stats();
```

## Precautions and Best Practices

### 1. Data Freshness Considerations

**Critical data that should always be fresh**:
- Job status information
- Authentication status
- User profile data

These endpoints are configured to bypass caching by default.

**Data that can be cached safely**:
- YouTube video metadata
- Static configuration data
- Historical job data

### 2. Cache Invalidation

**When to invalidate cache**:
- After creating/updating/deleting resources
- When user logs out
- When app version changes

**How to invalidate cache**:
```javascript
// Import the cache utilities
import { clearApiCache } from './utils/apiCache';

// After a POST/PUT/DELETE operation
await axios.post('/api/resource');
clearApiCache(); // Clear the entire cache

// Or clear specific entries
localStorage.removeItem('sanatana_api_cache_/api/specific-endpoint');
```

### 3. Testing Considerations

- Always test with both empty and primed caches
- Test with network throttling to see cache benefits
- Verify that critical data is always fresh

## Troubleshooting Common Errors

### 1. `clearExpiredApiCache is not defined` Error

This error occurs when trying to use the `clearExpiredApiCache` function without properly importing it.

**Solution**:
```javascript
// Import apiCache dynamically to avoid circular dependencies
import('./utils/apiCache').then(({ clearExpiredApiCache }) => {
  clearExpiredApiCache();
}).catch(err => {
  console.error('Error clearing expired API cache:', err);
});
```

### 2. Service Worker MIME Type Error

Error: `Failed to register a ServiceWorker: The script has an unsupported MIME type ('text/html')`

This occurs when the service worker file doesn't exist at the specified location or is being served with the wrong MIME type.

**Solutions**:
1. Make sure the service worker file exists in the correct location
2. Use a relative path instead of an absolute path:
   ```javascript
   const swPath = `${window.location.pathname.replace(/\/+$/, '')}/service-worker.js`;
   navigator.serviceWorker.register(swPath);
   ```
3. Only register the service worker in production or localhost:
   ```javascript
   const isLocalhost = window.location.hostname === 'localhost';
   const isProduction = process.env.NODE_ENV === 'production';
   if (isProduction || isLocalhost) {
     // Register service worker
   }
   ```

### 3. Icon Loading Issues

If icons are still loading sequentially despite the preloading mechanism:

**Solutions**:
1. Check browser console for errors in the preloadIcons function
2. Make sure the USED_IONICONS array in iconLoader.js includes all icons used in the app
3. Try using a different preloading approach:
   ```javascript
   // Alternative icon preloading approach
   const preloadIconsAlternative = async () => {
     // Load Ionicons font
     await Font.loadAsync(Ionicons.font);

     // Force render of all icons in a hidden div
     if (typeof document !== 'undefined') {
       const iconNames = ['home', 'settings', 'help-circle', 'person'];
       const div = document.createElement('div');
       div.style.opacity = '0';
       div.style.position = 'absolute';
       div.style.pointerEvents = 'none';

       iconNames.forEach(name => {
         const span = document.createElement('span');
         span.className = `ionicon ionicon-${name}`;
         div.appendChild(span);
       });

       document.body.appendChild(div);
       setTimeout(() => document.body.removeChild(div), 1000);
     }
   };
   ```

## Frequently Asked Questions

### How do I know if something is being cached when it shouldn't be?

1. Check the Network tab in browser developer tools
2. Look for responses with status 304 or "from cache"
3. Verify the Cache-Control headers
4. Use the browser's "Disable cache" option to compare behavior

### How can I force a fresh request for a specific endpoint?

**Client-side**:
```javascript
// Method 1: Disable cache for this request
const response = await cachedAxios.get('/api/endpoint', { enableCache: false });

// Method 2: Add a timestamp to bust cache
const response = await cachedAxios.get(`/api/endpoint?_t=${Date.now()}`);
```

**Server-side**:
```python
# Add no-cache headers to specific endpoints
@app.route('/api/critical-data')
@no_cache()  # Decorator from cache_middleware.py
def get_critical_data():
    # ...
```

### What happens if the cache gets corrupted?

The caching system has several safeguards:
1. Try-catch blocks around all cache operations
2. Automatic fallback to network requests if cache read fails
3. Cache entry expiration to prevent stale data
4. Size limits to prevent memory issues

If you suspect cache corruption, you can clear it:
```javascript
localStorage.clear(); // Clear all localStorage
// or
clearApiCache(); // Clear only API cache
```

### How much data is being cached?

The API cache is limited to 50 entries by default (configurable in `apiCache.js`).
Each entry contains:
- The API response data
- Timestamp
- Expiration time

You can check the current cache size:
```javascript
// In browser console
const cacheSize = JSON.stringify(localStorage.getItem('sanatana_api_cache')).length / 1024;
console.log(`Cache size: ${cacheSize.toFixed(2)} KB`);
```

### Does caching work in development mode?

Yes, all caching mechanisms work in development mode, but:
1. Service worker caching only works in production builds
2. Browser cache may be disabled if DevTools is open with "Disable cache" checked
3. React's hot reloading may interfere with some caching behaviors

For testing caching in development:
1. Close DevTools or uncheck "Disable cache"
2. Use incognito/private browsing window
3. Use production build for service worker testing
