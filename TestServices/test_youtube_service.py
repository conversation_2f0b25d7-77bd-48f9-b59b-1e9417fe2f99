import requests
import json
import sys
import os

service_name = "YOUTUBE_SERVICE"

# Get the parent directory of the current script
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

# Add it to sys.path
sys.path.append(parent_dir)

from Utils.globals import get_service_port

# Retrieve the service port from the environment variable
PORT =  get_service_port(service_name)

# Define the base URL (update if the service runs on a different machine or port)
BASE_URL = f"http://localhost:{PORT}"

def test_upload_video(file, title, description, keywords, category, privacyStatus, email):
    """Test the /upload_video endpoint"""
    url = f"{BASE_URL}/upload_video"
    data = {
        "file": file,
        "title": title,
        "description": description,
        "keywords": keywords,
        "category": category,
        "privacyStatus": privacyStatus,
        "email": email,
    }
    response = requests.post(url, json=data)
    
    print("Upload Video Response:", response.json())
    return response.json()

def test_add_to_playlist(video_id, playlist, email):
    """Test the /add_to_playlist endpoint"""
    url = f"{BASE_URL}/add_to_playlist"
    data = {
        "video_id": video_id,
        "playlist": playlist,
        "email": email,
    }
    response = requests.post(url, json=data)
    print("Add to Playlist Response:", response.json())
    return response.json()

def test_change_visibility(video_id, privacy_status, email):
    """Test the /change_visibility endpoint"""
    url = f"{BASE_URL}/change_visibility"
    data = {
        "video_id": video_id,
        "privacy_status": privacy_status,
        "email": email,
    }
    response = requests.post(url, json=data)
    print("Change Visibility Response:", response.json())
    return response.json()

def test_prepend_description(video_id, text, email):
    """Test the /prepend_description endpoint"""
    url = f"{BASE_URL}/prepend_description"
    data = {
        "video_id": video_id,
        "text": text,
        "email": email,
    }
    response = requests.post(url, json=data)
    print("Prepend Description Response:", response.json())
    return response.json()

def test_update_playlist_links(playlist_id, email):
    """Test the /update_playlist_links endpoint"""
    url = f"{BASE_URL}/update_playlist_links"
    data = {
            "playlist_id": playlist_id, 
            "email": email,
            }
    response = requests.post(url, json=data)
    print("Update Playlist Links Response:", response.json())
    return response.json()

def test_reorder_playlist(playlist_id, sort_by, email):
    """Test the /reorder_playlist endpoint"""
    url = f"{BASE_URL}/reorder_playlist"
    data = {
        "playlist_id": playlist_id,
        "sort_by": sort_by,
        "email": email,
    }
    response = requests.post(url, json=data)
    print("Reorder Playlist Response:", response.json())
    return response.json()

if __name__ == "__main__":
    #Test Upload Video
    # upload_response = test_upload_video(
    #     file=r"C:/Users/<USER>/Downloads/YetToPost/Hinduism is in Japan/Hinduism is in Japan.mp4",
    #     title="Hinduism is in Japan",
    #     description="Hinduism is in Japan",
    #     keywords="SanatanaDharma, Hinduism, Japan, Shiva, Ganesh, Saraswati, Brahman",
    #     category="22",
    #     privacyStatus="private",
    #     email="<EMAIL>",
    # )

    # # # Extract uploaded video ID if upload was successful
    # video_id = upload_response.get("video_id")
   
    # print("Video ID: ", video_id)
    
    video_id = "F3chs34Yjc0"
    if video_id:
        ...
        # Test Change Visibility
        # test_change_visibility(video_id=video_id, 
        #                        privacy_status="public",
        #                        email="<EMAIL>")
        
        #Test Add to Playlist
        # test_add_to_playlist(video_id=video_id,
        # playlist="Hinduism Is Everywhere!",
        # email="<EMAIL>",)


        # Test Prepend Description
        # test_prepend_description(video_id=video_id, 
        # text="Sanatana Dharma is spread around the world!",
        # email="<EMAIL>",)

        # Test Update Playlist Links
        # test_update_playlist_links(playlist_id="PLhofekRUMpFyO3hcndNY2xFe6wbMMooaC",
        # email="<EMAIL>",)

        # # Test Reorder Playlist
        # test_reorder_playlist(playlist_id="PLhofekRUMpFxnGh2IF5ih_TnqCJKRkh13",
        # sort_by="upload",
        # email="<EMAIL>",)
