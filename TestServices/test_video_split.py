import requests
import json
import re
import os
import sys

service_name = "VIDEO_SPLIT"

# Get the parent directory of the current script
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

# Add it to sys.path
sys.path.append(parent_dir)

from Utils.globals import get_service_port

# Retrieve the service port from the environment variable
PORT =  get_service_port(service_name)

# Construct the API URL
API_URL = f"https://b7j82901-5005.use.devtunnels.ms/split_video"

# Sample test data
test_data = {
    "path": "C:/Users/<USER>/Downloads/SanantaDharma/Overview Of Vedic Culture/Overview Of Vedic Culture.mp4",  # Change to an existing video path
    "interval": 180
}


def extract_number(filename):
    """Extract the numerical part from filenames like 'split_10.mp4'."""
    match = re.search(r'split_(\d+)\.mp4$', filename)
    return int(match.group(1)) if match else float('inf')  # Sort unmatched files at the end

def test_split_video():
    """Send a request to the video splitting service and print the response."""
    headers = {"Content-Type": "application/json"}
    
    try:
        response = requests.post(API_URL, headers=headers, data=json.dumps(test_data))
        response_data = response.json()

        # Sort the split_files list numerically if it exists in the response
        if "split_files" in response_data and isinstance(response_data["split_files"], list):
            response_data["split_files"].sort(key=extract_number)  # Sort using extracted numbers

        print("\nAPI Response:")
        print(json.dumps(response_data, indent=4))  # Pretty print JSON response
        
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_split_video()
