import requests
import json
import time
import sys
import sys
import os

service_name = "YOUTUBE_DOWNLOAD"

# Get the parent directory of the current script
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

# Add it to sys.path
sys.path.append(parent_dir)

from Utils.globals import get_service_port

# Retrieve the service port from the environment variable
PORT =  get_service_port(service_name)

# Base URL of the service
BASE_URL = f"http://localhost:{PORT}"

def test_health():
    """Test the health check endpoint"""
    response = requests.get(f"{BASE_URL}/health")
    print("Health Check Response:", response.json())
    assert response.status_code == 200
    return response.json()

def test_video_info(url):
    """Test getting video information"""
    response = requests.get(f"{BASE_URL}/video-info", params={"url": url})
    # print("\nVideo Info Response:", json.dumps(response.json(), indent=2))
    assert response.status_code == 200
    return response.json()

def test_download(url, media_type="video", resolution=None, folder_path=None):
    """Test downloading a video or audio"""
    payload = {
        "url": url,
        "type": media_type
    }

    if resolution:
        payload["resolution"] = resolution

    if folder_path:
        payload["folder_path"] = folder_path

    print(f"\ntest_download: Downloading {media_type} with resolution {resolution}...")
    if folder_path:
        print(f"test_download: Saving to custom folder: {folder_path}")

    response = requests.post(f"{BASE_URL}/download", json=payload)
    response_data = response.json()
    print("test_download: Download Response:", json.dumps(response_data, indent=2))

    # Print the file path specifically
    if 'file_path' in response_data:
        print(f"\ntest_download: Downloaded file path: {response_data['file_path']}")

    assert response.status_code == 200
    download_id = response_data.get("download_id")

    # Check download status
    if download_id:
        time.sleep(2)  # Wait a bit for processing
        status_response = requests.get(f"{BASE_URL}/download/{download_id}")
        print("test_download: Download Status:", json.dumps(status_response.json(), indent=2))

    return response.json()

def test_cleanup():
    """Test cleaning up downloads"""
    response = requests.post(f"{BASE_URL}/cleanup")
    print("\nCleanup Response:", response.json())
    assert response.status_code == 200
    return response.json()

if __name__ == "__main__":
    import os
    # Use a test video URL or get from command line
    test_url = "https://www.youtube.com/watch?v=jbUHzLNkOiM"  # Default test video

    if len(sys.argv) > 1:
        test_url = sys.argv[1]

    print(f"Using test URL: {test_url}")
    custom_folder = r"C:\Sanatana_Downloads\test"
    print("*"*70)
    # Run tests # test_health()
    test_video_info(test_url)

    print("*"*70)
    test_download(test_url, "video", "1080", custom_folder)

    # Uncomment to test cleanup
    # test_cleanup()
