import  test_youtube_service as yt
import  test_yt_download_service as yt_down


def download_and_upload_yt(url, channel_email, playlist):
    video_id = "erxt5L9nTRw"
    playlist_id = "PLhofekRUMpFyQUW2GCvUtm99PHUlzTCL0"   
    
    info = yt_down.test_video_info(url)
    down = yt_down.test_download(url, "video", "1080", r"C:\Sanatana_Downloads\test")
    
    upload_response = yt.test_upload_video(
        file = down.get("file_path"),
        title = info.get("title"),
        description =   info.get("description"),
        keywords="SanatanaDharma, Hinduism, Japan, Shiva, Sadhguru, Is<PERSON>, Brahman",
        category="22",
        privacyStatus="private",
        email=channel_email,
    )
    

    if upload_response["status"] == "SUCCESS":
        video_id = upload_response["video_id"]
        youtube_link = upload_response["youtube_link"]
        print(f"Video uploaded successfully! ID: {video_id}")
        print(f"YouTube link: {youtube_link}")
    else:
        print(f"Upload failed: {upload_response.get('error')}")
        return
    
 
    
    visibility_response = yt.test_change_visibility(video_id=video_id, 
                            privacy_status="public",
                            email=channel_email, 
                            )
    
    if visibility_response["status"] == "SUCCESS":
        video_id = visibility_response["video_id"]
        video_privacy = visibility_response["privacy_status"]
        print(f"Changed Visibility successfully! ID: {video_id} to {video_privacy}")
    else:
        print(f"Change Visibility failed: {visibility_response.get('error')}")
        return
                   
    add_playlist_response = yt.test_add_to_playlist(video_id=video_id,
    playlist=playlist,
    email=channel_email,)
    
    if add_playlist_response["status"] == "SUCCESS":
        playlist_id = add_playlist_response["playlist_id"]
        print(f"Added video {video_id} to Playlist successfully! ID: {playlist_id}")
    else:
        print(f"Add to Playlist failed: {add_playlist_response.get('error')}")
        return

    prepend_response = yt.  test_prepend_description(video_id=video_id, 
        text="Amazing take on Sanatana Dharma by Sadhguru!",
        email=channel_email,)
    
    if prepend_response["status"] == "SUCCESS":
        video_id = prepend_response["video_id"]
        print(f"Prepended Description successfully! ID: {video_id}")
    else:
        print(f"Prepend Description failed: {prepend_response.get('error')}")
        return

    links_response = yt.test_update_playlist_links(
        playlist_id=playlist_id,
        email=channel_email,
        )
    if links_response["status"] == "SUCCESS":
        playlist_id = links_response["playlist_id"]
        print(f"Updated Playlist Links successfully! ID: {playlist_id}")
    else:
        print(f"Update Playlist Links failed: {links_response.get('error')}")
        return

   

if __name__ == "__main__":
    channel_email="<EMAIL>"
    url = "https://www.youtube.com/shorts/jr4TGTwVDjY"
    playlist = "Hinduism Is Everywhere!"
    download_and_upload_yt(url, channel_email, playlist)