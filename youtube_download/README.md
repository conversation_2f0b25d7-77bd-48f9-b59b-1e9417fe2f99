# YouTube Download Service

A Flask-based service that provides API endpoints to download videos and audio from YouTube.

## Prerequisites

- Python 3.6+
- Flask
- yt-dlp
- ffmpeg (must be installed on the system)

## Installation

1. Install ffmpeg on your system:
   - Instructions: https://phoenixnap.com/kb/ffmpeg-windows
   - Binaries location: https://www.gyan.dev/ffmpeg/builds/

2. Install Python dependencies:
   ```
   pip install flask yt-dlp
   ```

## Running the Service

```
python yt_down_service.py
```

The service will start on port 5000 by default. You can change the port by setting the `PORT` environment variable.

## API Endpoints

### Health Check
```
GET /health
```
Returns the health status of the service.

### Get Video Information
```
GET /video-info?url=<youtube_url>
```
Returns information about a YouTube video without downloading it.

### Download Video or Audio
```
POST /download
Content-Type: application/json

{
  "url": "https://www.youtube.com/watch?v=example",
  "type": "video",  // or "audio"
  "resolution": "720",  // optional, e.g., "240", "360", "720", "1080"
  "folder_path": "/path/to/save"  // optional, custom folder to save the downloaded file
}
```
Initiates a download and returns a download ID, file name, and normalized file path. If `folder_path` is provided, the file will be moved to that location after download.

### Check Download Status
```
GET /download/<download_id>
```
Checks the status of a download.

### Clean Up Downloads
```
POST /cleanup
```
Cleans up all downloads to free up disk space.

## Environment Variables

- `PORT`: The port to run the service on (default: 5000)
- `DOWNLOAD_FOLDER`: The folder to store downloads in (default: a temporary directory)

## Example Usage

### Get Video Information
```bash
curl "http://localhost:5006/video-info?url=https://www.youtube.com/watch?v=example"
```

### Download a Video
```bash
curl -X POST "http://localhost:5006/download" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.youtube.com/watch?v=example", "type": "video", "resolution": "720"}'
```

### Download Audio Only
```bash
curl -X POST "http://localhost:5006/download" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.youtube.com/watch?v=example", "type": "audio"}'
```

### Download to a Specific Folder
```bash
curl -X POST "http://localhost:5006/download" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.youtube.com/watch?v=example", "type": "video", "folder_path": "C:/Downloads/YouTube"}'
```

### Check Download Status
```bash
curl "http://localhost:5006/download/your-download-id"
```

### Clean Up Downloads
```bash
curl -X POST "http://localhost:5006/cleanup"
```
