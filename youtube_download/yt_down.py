import yt_dlp
import random
import uuid
import re

"""

Must install ffmpeg on system: 
 Instructions: 
 Windows: https://phoenixnap.com/kb/ffmpeg-windows     
 Linux: https://phoenixnap.com/kb/install-ffmpeg-ubuntu
 
 binaries location: https://www.gyan.dev/ffmpeg/builds/

"""

def get_video_audio(url, download_folder, type, resolution=None):
    unique_id = str(uuid.uuid4())  # e.g., '9f36b32e-8e1b-4d4e-b85e-bec84d21a1a2'
    filename_template = f"{unique_id}.%(ext)s"
    ydl_opts_audio = {
        'outtmpl': f'{download_folder}/{filename_template}',
        'format': 'bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'mp3',
            'preferredquality': '192',
        }],
    }
    
    format = 'bestvideo[ext=mp4][vcodec^=avc1]+bestaudio[ext=m4a][acodec^=mp4a]/mp4' # Best quality
    name_ext = "_full_resolution.%(ext)s"
    
    if resolution is not None:
        format = f'bestvideo[ext=mp4][height<={resolution}][vcodec^=avc1]+bestaudio[ext=m4a][acodec^=mp4a]/mp4'
        name_ext = f"_{resolution}.%(ext)s"

    ydl_opts_video = {
        'outtmpl': f'{download_folder}/{filename_template}{name_ext}',
        'format': format,
        'merge_output_format': 'mp4',  # Ensure final format is MP4
        'postprocessors': [{
            'key': 'FFmpegVideoConvertor',
            'preferedformat': 'mp4'  # Final conversion to MP4 if necessary
        }],
        'noplaylist': True,  # To avoid downloading entire playlists accidentally
        'retries': 3,  # Retry in case of temporary failure
       
    }
    
    # Use the audio options if 'type' is audio
    if type == "audio":
        ydl_opts = ydl_opts_audio
    else: 
        ydl_opts = ydl_opts_video
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            # Attempt to download with the specified resolution
            ydl.download([url])
        except yt_dlp.utils.DownloadError as e:
            print(f"Download error: {e}")
            # If download fails, attempt to download the best available resolution
            print("Attempting to download in the best available resolution...")
            # Modify the format to the best available if specific resolution fails
            ydl_opts['format'] = 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/mp4'
            with yt_dlp.YoutubeDL(ydl_opts) as fallback_ydl:
                fallback_ydl.download([url])

def get_video_info(url):
    print(f"get_video_info START =========================================")
    ydl_opts = {
        'cookies': 'cookies.rakesh_testone.txt',
    'quiet': True,
    'skip_download': True,
    'forcejson': True,
    'extract_flat': True,
    }
    video_info = {    
                        "title" : None,
                        "duration" : None,
                        "uploader" : None,
                        "uploader_id" :None,
                        "upload_date" : None,
                        "license" :None,
                        "is_live" : None,
                        "view_count" : None,
                        "categories" : None,
                        "tags" : None,
                        "age_limit" : None,
                        "availability" :None,
                        "webpage_url" : None,
                        "description" : None,
                        "like_count" : None,
                        "channel_url" : None,
                        "subtitles" : None,
                    
                        }
   
    def extract_youtube_error(message):
        if "[youtube]" in message:
            return message.split("[youtube]", 1)[1].strip()
        return message.strip()
    
    try:
        # with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl  = yt_dlp.YoutubeDL(ydl_opts) 

                if "https://www.facebook.com" in url:
                    info = ydl.extract_info(url, download=False)
                    url = extract_facebook_reel_id(info)
                
                if "https://www.tiktok.com" in url:
                    info = ydl.extract_info(url, download=False)
                    url, success = extract_tiktok_url(info)
                    if not success:
                        print(f"Failed to extract TikTok URL: {url}")
                        

                info = ydl.extract_info(url, download=False)    
                
                title = info.get('title')
                description = info.get('description')
                uploader = info.get('uploader')
                if "facebook.com" in url:
                    title = fix_facebook_title(title) 
                    uploader = ""

                if "tiktok.com" in url:
                    title = fix_tiktok_title(title)
                    description = fix_tiktok_title(description, desc=True)  
                
                if "x.com" in url:
                    title = title.split(" - ")[1]  
                
                if "instagram.com" in url:
                    title = description[:100]

               

                video_info = {    
                    "download_url" : url,
                    "title" : title[:70],
                    "duration" : info.get('duration') ,        # in seconds
                    "uploader" : uploader,
                    "uploader_id" : info.get('uploader_id'),
                    "upload_date" : info.get('upload_date') ,  # Format: YYYYMMDD
                    "license" : info.get('license'),
                    "is_live" : info.get('is_live'),
                    "view_count" : info.get('view_count'),
                    "categories" : info.get('categories'),
                    "tags" : info.get('tags'),
                    "age_limit" : info.get('age_limit'),
                    "availability" : info.get('availability'),
                    "webpage_url" : info.get('webpage_url'),
                    "description" : description,
                    "like_count" : info.get('like_count'),
                    "channel_url" : info.get('channel_url'),
                    "subtitles" : info.get('subtitles'),
                
                    }
                
                print(f"get_video_info: info: {video_info}")
                print(f"get_video_info END =========================================")
            # print("Video info:", video_info)
                return video_info
    except yt_dlp.utils.DownloadError as e:
        print(f"get_video_info: Exception: yt_dlp error: {e}")
        video_info["title"] =  "Error: Type: 1; Not a video or downlaodable URL or NOT YET SUPPORTED"
        video_info["description"] = "Download Error"
        
        return video_info

    except SystemExit as e:
        print("get_video_info: Exception: yt_dlp triggered sys.exit():", e)
        video_info["title"] =  "Error: Type: 2; Not a video or downlaodable URL or NOT YET SUPPORTED"
        video_info["description"] = "SystemExit Error"
        return video_info

    except Exception as e:
        print(f"get_video_info: Exception: {e}")
        video_info["title"] =  "Error: Type: 3; Not a video or downlaodable URL or NOT YET SUPPORTED"
        video_info["description"] = "Unknown Error"

        return video_info
    
def fix_tiktok_title(title,  desc=False):
    # Step 1: Remove special TikTok prefixes if present
    stitch_pattern = r'^(#stitch with @\S+|#duet with @\S+|Replying to @\S+)\s+'
    title = re.sub(stitch_pattern, '', title, flags=re.IGNORECASE)

    # Step 2: Limit to 300 characters
    title = title[:300]

    if desc:
        return title
    # Step 3: Remove all hashtags and mentions
    title = re.sub(r'[#@]\w+', '', title)

    # Step 4: Collapse multiple spaces and trim
    title = re.sub(r'\s+', ' ', title).strip()

    return title

def fix_facebook_title(title):
    # Step 1: Extract text between the first pair of '|' symbols
    parts = title.split('|')
    if len(parts) >= 3:
        title = parts[1].strip()
    else:
        # If there aren't two |, fallback to full title
        title = title.strip()

    # Step 2: Remove all hashtags (including ones with Unicode hashtags)
    title = re.sub(r'#\w+', '', title)

    # Step 3: Clean up extra spaces
    title = re.sub(r'\s+', ' ', title).strip()
    
    return title


def extract_tiktok_url(info):
    url = info.get("url") or ""
    
    # Strip query parameters (anything after '?')
    base_url = url.split('?', 1)[0]

    # Validate TikTok video pattern: must have /@username/video/video_id
    if "/@".lower() in base_url.lower() and "/video/" in base_url.lower():
        return base_url, True
    else:
        print(f"Invalid TikTok video URL: {url}")
        return base_url, False
      
    
def extract_facebook_reel_id(info):
  
        try:
           
            real_url = info.get("url", "")
            
            # Match pattern like: /reel/123456789012345?...
            match = re.search(r'/reel/(\d+)', real_url)
            if match:
                video_id = match.group(1)
                print(f"Extracted video ID: {video_id}")
                return f"https://m.facebook.com/watch/?v={video_id}"
            else:
                print("Could not find /reel/<id> in URL.")

            real_url = info.get("original_url", "")
            
            # Match pattern like: /reel/123456789012345?...
            match = re.search(r'/reel/(\d+)', real_url)
            if match:
                video_id = match.group(1)
                print(f"Extracted video ID: {video_id}")
                return f"https://m.facebook.com/watch/?v={video_id}"
            else:
                print("Could not find /reel/<id> in URL.")

        except Exception as e:
            print(f"Error extracting info: {e}")
    
        return None

def get_video(url, download_folder, resolution = None):
    print("Inside get_video method")
    video_info =  get_video_info(url)
    url = video_info.get("download_url")
    get_video_audio(url, download_folder, "video", resolution)

def get_audio(url, download_folder, resolution = None):
    get_video_audio(url, download_folder, "audio", resolution)
  
def download_audio_only():
   
    single_video_or_playlist_url = "https://www.youtube.com/watch?v=97uVDPOibYw"
  
    download_folder = r"~/Downloads/"
 
    #set type="audio" for mp3 download
    get_audio(single_video_or_playlist_url, download_folder)
 
def download_video_only(url = None):
    if not url:
        url = "https://www.youtube.com/watch?v=XoX0g5oenDI"
  
    download_folder = r"~/Downloads/"
 
    #  set type="video". Optionally set resolution to 240, 360, 720, or 1080 or any other. If you don't set, it will download at highest
    get_video(url, download_folder, resolution="1080")

def download_video_info(url = None):
    #url = "https://www.youtube.com/watch?v=ec9rj79uYkI"
    if not url:
        url = "https://www.youtube.com/watch?v=ec9rj79uYkI"
        url = "https://www.tiktok.com/@rushinarasima/video/7511763839399267630?is_from_webapp=1&sender_device=pc&web_id=7419119180278400543"
   
    video_info = get_video_info(url)
    print(f"Title: {video_info.get('title')}")
    print(f"Description: {video_info.get('description')}")
    print(f"Duration: {video_info.get('duration')} seconds")
    print(f"Uploader: {video_info.get('uploader')} ({video_info.get('uploader_id')})")
    print(f"Upload date: {video_info.get('upload_date')}")
    print(f"License: {video_info.get('license')}")
    print(f"Categories: {video_info.get('categories')}")
    print(f"Tags: {video_info.get('tags')}")
    print(f"Age Limit: {video_info.get('age_limit')}")


print("testing ....")
# Example usage
if __name__ == "__main__":
    print("yt_down main gaurd")
   
    #facebook
    #fixed: 
    #url = "https://www.facebook.com/share/r/1Mwwpaftim/"
    
    #url = "https://www.facebook.com/stories/122101552124441416/UzpfSVNDOjEwMTg3MDcyMzA0NjA2Mjg=/?view_single=1"
    url = "https://www.facebook.com/stories/1562535777139916"
    url = "https://m.facebook.com/watch/?v=1562535777139916"
    url = "https://www.facebook.com/share/r/1BB22yKizD/"
    #url = "https://m.facebook.com/watch/?v=731153429237704"
    # "https://www.facebook.com/reel/653667984335491"
    #url = "https://m.facebook.com/watch/?v=653667984335491"
    #download_video_info(url)
    #download_video_only(url)
    
    #tiktok
    url = "https://www.tiktok.com/@rushinarasima/video/7511509226762620202"
    url = "https://www.tiktok.com/@rushinarasima/video/7511763839399267630?is_from_webapp=1&sender_device=pc&web_id=7419119180278400543"
    url = "https://www.tiktok.com/@rushinarasima/video/7510667671868968238?is_from_webapp=1&sender_device=pc&web_id=7419119180278400543"
    url= "https://www.tiktok.com/t/ZTjqG6fJ9/"
    url= "https://www.tiktok.com/t/ZTjbu9nqd/"
    url="https://www.tiktok.com/t/ZTjbuEcfE/"
    #download_video_info(url)
    #download_video_only(url)

    #instagram
    url = "https://www.instagram.com/p/DKY0LsEJSCy/"
    url = "https://www.instagram.com/reel/DKMiQKQPqkP/?utm_source=ig_web_copy_link"
    url = "https://www.instagram.com/reel/DKZT01dzJ5K/?utm_source=ig_web_copy_link"
    url = "https://www.instagram.com/reel/DJJuYylyuM1/?utm_source=ig_web_copy_link"
    #download_video_info(url)
    #download_video_only(url)

    #x
    #url = "https://x.com/TheDanishsakhi/status/1929979287970689328"
    url = "https://x.com/DrLoupis__/status/1929457298507788725"
    url = "https://x.com/BDHerzinger/status/1929896248305152368"
    #download_video_info(url)
    #download_video_only(url)

    #bluesk
    url = "https://bsky.app/profile/thetnholler.bsky.social/post/3lqqlda2jf22i"

    #download_video_info(url)
    #download_video_only(url)

    #linkedin
    url = "https://www.linkedin.com/posts/anujmagazine_this-is-not-a-post-about-ai-this-is-a-post-ugcPost-7335649833468272640-xzWh?utm_source=social_share_send&utm_medium=member_desktop_web&rcm=ACoAABOGZjoB6HmF2lr28mbUpWm5fuz98rXR21s"
    #download_video_info(url)
    #download_video_only(url)
    
    #Web link
    url = "https://sample-videos.com/video321/mp4/720/big_buck_bunny_720p_1mb.mp4"
    #download_video_info(url)
    #download_video_only(url)


    