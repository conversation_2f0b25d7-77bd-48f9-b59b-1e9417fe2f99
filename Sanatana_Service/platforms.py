from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import os
import sqlite3
import requests
import time
import json
import urllib.parse

from datetime import datetime, timedelta, timezone
import pytz
import math


@app.route('/get_destinations/<sanatana_email>', methods=['GET'])
def get_destinations(sanatana_email):
    try:
        # Load destinations.json file
        destinations_path = os.path.join(os.path.dirname(__file__), 'assets', 'destinations.json')
        with open(destinations_path, 'r') as f:
            destinations = json.load(f)

        # Connect to database
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # Process each destination
        for dest in destinations:
            dest_id = dest.get('id', '')

            # Extract platform from id (text before underscore if one exists)
            platform = dest_id.split('_')[0] if '_' in dest_id else dest_id

           
            dest['authorized'] = False

            # Query credentials table for this platform and email
            cursor.execute(
                "SELECT client_id FROM credentials WHERE sanatana_email = ? AND platform = ?",
                (sanatana_email, platform)
            )
            cred_results = cursor.fetchall()

            # Loop through all matching credentials
            for cred_row in cred_results:
                client_id = cred_row[0]

                # Query users table to check if user is authorized
                cursor.execute(
                    "SELECT * FROM users WHERE sanatana_email = ? AND client_id = ?",
                    (sanatana_email, client_id)
                )
                user_result = cursor.fetchone()

                # If user record exists, set authorized to true and exit the loop
                if user_result:
                    dest['authorized'] = True
                    break  # Exit the loop once we find an authorized credential

        conn.close()
        return jsonify(destinations)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

