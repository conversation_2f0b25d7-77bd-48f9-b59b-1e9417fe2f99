"""
Cache middleware for Flask applications.
This module provides functions to add cache headers to Flask responses.
"""

from functools import wraps
from flask import request, make_response

def add_cache_headers(max_age=0, stale_while_revalidate=0, private=False):
    """
    Decorator to add cache headers to a Flask route.
    
    Args:
        max_age (int): Maximum age in seconds for the cache (default: 0)
        stale_while_revalidate (int): Time in seconds during which a stale response is acceptable (default: 0)
        private (bool): Whether the response should be private (not cached by CDNs) (default: False)
    
    Returns:
        function: Decorated function
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            response = make_response(f(*args, **kwargs))
            
            # Set Cache-Control header
            cache_control = []
            if private:
                cache_control.append('private')
            else:
                cache_control.append('public')
            
            cache_control.append(f'max-age={max_age}')
            
            if stale_while_revalidate > 0:
                cache_control.append(f'stale-while-revalidate={stale_while_revalidate}')
            
            response.headers['Cache-Control'] = ', '.join(cache_control)
            
            # Add Vary header to properly handle cached responses
            response.headers['Vary'] = 'Accept-Encoding, User-Agent'
            
            return response
        return decorated_function
    return decorator

def no_cache():
    """
    Decorator to prevent caching of a Flask route.
    
    Returns:
        function: Decorated function
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            response = make_response(f(*args, **kwargs))
            response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            return response
        return decorated_function
    return decorator

def cache_for_day():
    """
    Decorator to cache a Flask route for 1 day.
    
    Returns:
        function: Decorated function
    """
    return add_cache_headers(max_age=86400, stale_while_revalidate=3600)

def cache_for_hour():
    """
    Decorator to cache a Flask route for 1 hour.
    
    Returns:
        function: Decorated function
    """
    return add_cache_headers(max_age=3600, stale_while_revalidate=600)

def cache_for_minute():
    """
    Decorator to cache a Flask route for 1 minute.
    
    Returns:
        function: Decorated function
    """
    return add_cache_headers(max_age=60, stale_while_revalidate=300)

def cache_for_week():
    """
    Decorator to cache a Flask route for 1 week.
    
    Returns:
        function: Decorated function
    """
    return add_cache_headers(max_age=604800, stale_while_revalidate=86400)
