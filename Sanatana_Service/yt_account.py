from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import os
import sqlite3
import requests
import time
import json
import urllib.parse
import sys

from datetime import datetime, timedelta, timezone

service_name = "YOUTUBE_SERVICE"


# Get the parent directory of the current script
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

# Add it to sys.path
sys.path.append(parent_dir)

from Utils.globals import get_service_port

# Retrieve the service port from the environment variable
PORT =  get_service_port(service_name)

# Define the base URL (update if the service runs on a different machine or port)
BASE_URL = f"http://localhost:{PORT}"

# Make POST request to the service
def check_long_video_status(channel_email):
    # Define the URL for the service
    url = f"{BASE_URL}/long_video_status"
    
    # Define the payload to send in the POST request
    payload = {'email': channel_email}
    
    data = None
    # Send POST request to the endpoint
    response = requests.post(url, json=payload)
    
   
    if response.status_code == 200:
       data = response.json()
    else:
        response.raise_for_status()  # Raise an exception for any HTTP errors

    if data.get('long_uploads') is True:
        #print("The channel is qualified for long uploads.")
        return True
    else:
        #print("The channel is not qualified for long uploads.")
        return False


def update_long_uploads_status(email):
    try:
        # Connect to the SQLite database
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # Define the SQL query to update the longUploadsStatus
        query = """
        UPDATE user_channels
        SET longUploadsStatus = 'eligible'
        WHERE email = ?
        """
        
        # Execute the update query with the provided email
        cursor.execute(query, (email,))
        
        # Commit the changes to the database
        conn.commit()
        
        # Check how many rows were affected
        if cursor.rowcount > 0:
            print(f"Successfully updated the longUploadsStatus for {email}.")
        else:
            print(f"No user found with email {email}.")
        
    except sqlite3.Error as e:
        print(f"An error occurred: {e}")
    finally:
        # Close the database connection
        conn.close()

if __name__ == '__main__':
    channel_email = "<EMAIL>"
    print("Testing long video status: ", check_long_video_status(channel_email))
    update_long_uploads_status(channel_email)