import os
import sqlite3
import sys

import sqlite3
 
# Get the parent directory of the current script
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

# Add it to sys.path
sys.path.append(parent_dir)
from Sanatana_Service.db import DATABASE

def get_channel_email_by_channel_id(channel_id):
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        cursor.execute('SELECT email FROM user_channels WHERE channel_id = ?', (channel_id,))
        result = cursor.fetchone()

        conn.close()

        if result:
            return result[0]  # email
        else:
            return None  # Not found

    except sqlite3.Error as e:
        print(f"get_channel_email_by_channel_id: Database error: {e}")
        return None