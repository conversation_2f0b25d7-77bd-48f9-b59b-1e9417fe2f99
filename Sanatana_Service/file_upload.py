from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import os
import sqlite3
import requests
import time
import json
import urllib.parse

from datetime import datetime, timedelta, timezone
import pytz
import math

# Add this endpoint to handle file upload information updates
@app.route("/update_file_upload_info", methods=["POST"])
def update_file_upload_info():
    """
    Update file upload information in the database.

    Expected JSON payload:
    {
        "sanatana_email": "<EMAIL>",
        "folder_path": "/path/to/folder",
        "source": "File Upload",
        "status": "Started upload" or "Upload Complete" or "Upload Failed: error message",
        "time_start": timestamp,
        "time_end": timestamp or null
    }

    Returns:
    {
        "id": row_id,
        "message": "Upload information updated successfully"
    }
    """
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = [
            "sanatana_email",
            "folder_path",
            "source",
            "status",
            "time_start",
        ]
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400

        sanatana_email = data["sanatana_email"]
        folder_path = data["folder_path"]
        source = data["source"]
        status = data["status"]
        time_start = data["time_start"]
        time_end = data.get("time_end")  # Optional

        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # Check if a record with the same folder_path exists
        cursor.execute(
            "SELECT id FROM uploads_folders WHERE sanatana_email = ? AND folder_path = ?",
            (sanatana_email, folder_path),
        )
        existing_record = cursor.fetchone()

        if existing_record:
            # Update existing record
            row_id = existing_record[0]

            update_query = """
            UPDATE uploads_folders
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            """
            params = [status]

            # Add time_end to update if provided
            if time_end is not None:
                update_query += ", time_end = ?"
                params.append(time_end)

            update_query += " WHERE id = ?"
            params.append(row_id)

            cursor.execute(update_query, params)

        else:
            # Insert new record
            cursor.execute(
                """
                INSERT INTO uploads_folders
                (sanatana_email, folder_path, source, status, time_start, time_end)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (sanatana_email, folder_path, source, status, time_start, time_end),
            )
            row_id = cursor.lastrowid

        conn.commit()

        return jsonify(
            {"id": row_id, "message": "Upload information updated successfully"}
        ), 200

    except Exception as e:
        print(f"update_file_upload_info: Error updating upload information: {str(e)}")
        return jsonify({"error": str(e)}), 500

    finally:
        if "conn" in locals():
            conn.close()


# Add this endpoint to get file upload information
@app.route("/get_file_upload_info/<int:upload_id>", methods=["GET"])
def get_file_upload_info(upload_id):
    """
    Get file upload information from the database.

    Returns:
    {
        "id": row_id,
        "sanatana_email": "<EMAIL>",
        "folder_path": "/path/to/folder",
        "source": "File Upload",
        "status": "Started upload" or "Upload Complete" or "Upload Failed: error message",
        "time_start": timestamp,
        "time_end": timestamp or null,
        "created_at": timestamp,
        "updated_at": timestamp
    }
    """
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT id, sanatana_email, folder_path, source, status, time_start, time_end,
                   created_at, updated_at
            FROM uploads_folders
            WHERE id = ?
            """,
            (upload_id,),
        )

        row = cursor.fetchone()

        if not row:
            return jsonify({"error": "Upload not found"}), 404

        upload_info = {
            "id": row[0],
            "sanatana_email": row[1],
            "folder_path": row[2],
            "source": row[3],
            "status": row[4],
            "time_start": row[5],
            "time_end": row[6],
            "created_at": row[7],
            "updated_at": row[8],
        }

        return jsonify(upload_info), 200

    except Exception as e:
        print(f"update_file_upload_info: Error retrieving upload information: {str(e)}")
        return jsonify({"error": str(e)}), 500

    finally:
        if "conn" in locals():
            conn.close()


# Add this endpoint to get all file uploads for a user
@app.route("/get_user_file_uploads", methods=["GET"])
def get_user_file_uploads():
    """
    Get all file uploads for a user.

    Query parameters:
    - sanatana_email: Email of the user
    - limit: Maximum number of records to return (default: 100)
    - offset: Number of records to skip (default: 0)

    Returns:
    {
        "uploads": [
            {
                "id": row_id,
                "sanatana_email": "<EMAIL>",
                "folder_path": "/path/to/folder",
                "source": "File Upload",
                "status": "Started upload" or "Upload Complete" or "Upload Failed: error message",
                "time_start": timestamp,
                "time_end": timestamp or null,
                "created_at": timestamp,
                "updated_at": timestamp
            },
            ...
        ],
        "total": total_count
    }
    """
    try:
        sanatana_email = request.args.get("sanatana_email")
        limit = int(request.args.get("limit", 100))
        offset = int(request.args.get("offset", 0))

        if not sanatana_email:
            return jsonify({"error": "sanatana_email parameter is required"}), 400

        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # Get total count
        cursor.execute(
            "SELECT COUNT(*) FROM uploads_folders WHERE sanatana_email = ?",
            (sanatana_email,),
        )
        total_count = cursor.fetchone()[0]

        # Get uploads with pagination
        cursor.execute(
            """
            SELECT id, sanatana_email, folder_path, source, status, time_start, time_end,
                   created_at, updated_at
            FROM uploads_folders
            WHERE sanatana_email = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
            """,
            (sanatana_email, limit, offset),
        )

        rows = cursor.fetchall()

        uploads = []
        for row in rows:
            uploads.append(
                {
                    "id": row[0],
                    "sanatana_email": row[1],
                    "folder_path": row[2],
                    "source": row[3],
                    "status": row[4],
                    "time_start": row[5],
                    "time_end": row[6],
                    "created_at": row[7],
                    "updated_at": row[8],
                }
            )

        return jsonify({"uploads": uploads, "total": total_count}), 200

    except Exception as e:
        print(f"update_file_upload_info: Error retrieving user uploads: {str(e)}")
        return jsonify({"error": str(e)}), 500

    finally:
        if "conn" in locals():
            conn.close()

