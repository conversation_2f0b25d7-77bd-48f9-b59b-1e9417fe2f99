from flask import Flask, request, after_this_request
from flask_cors import CORS
import time

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

""" Important for FUTURE
If you want to allow only http://localhost:5003 instead of all domains, update CORS like this:
CORS(app, resources={r"/*": {"origins": "http://localhost:5003"}})
"""

# Add cache headers to all responses
@app.after_request
def add_cache_headers(response):
    # Disable caching for all responses
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response
    # # Get the path from the request
    # path = request.path

    # # Set default cache control header
    # response.headers['Cache-Control'] = 'no-cache'

    # # Add Vary header to properly handle cached responses
    # response.headers['Vary'] = 'Accept-Encoding, User-Agent'

    # # Add ETag header based on response content and timestamp
    # response.headers['ETag'] = f'"{hash(response.get_data())}-{int(time.time())}"'

    # # Cache static assets aggressively
    # if path.endswith(('.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.woff2', '.ttf', '.eot')):
    #     # Cache for 1 week
    #     response.headers['Cache-Control'] = 'public, max-age=604800, stale-while-revalidate=86400'

    # # Cache API responses that are read-only (GET requests)
    # elif request.method == 'GET' and not any(x in path for x in ['/authenticate', '/login', '/signin', '/token']):
    #     # Cache for 5 minutes, but allow stale responses while revalidating
    #     response.headers['Cache-Control'] = 'public, max-age=300, stale-while-revalidate=600'

    # return response

