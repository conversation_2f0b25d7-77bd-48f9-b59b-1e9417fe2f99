# Caching System Documentation for Sanatana Media Backend Services

This document provides detailed information about the caching mechanisms implemented in the Sanatana Media backend services, how they affect operations, debugging techniques, and precautions to take.

## Table of Contents

1. [Overview](#overview)
2. [Caching Mechanisms](#caching-mechanisms)
   - [HTTP Cache Headers](#http-cache-headers)
   - [ETag Support](#etag-support)
   - [Conditional Requests](#conditional-requests)
3. [Service-Specific Caching](#service-specific-caching)
   - [Main Service (Sanatana_Service)](#main-service-sanatana_service)
   - [Job Status Service](#job-status-service)
   - [YouTube Download Service](#youtube-download-service)
4. [How Caching Affects Operations](#how-caching-affects-operations)
5. [Debugging Caching Issues](#debugging-caching-issues)
6. [Disabling Caching](#disabling-caching)
7. [Caching Analysis Tools](#caching-analysis-tools)
8. [Precautions and Best Practices](#precautions-and-best-practices)
9. [Frequently Asked Questions](#frequently-asked-questions)

## Overview

The Sanatana Media backend services implement HTTP-based caching mechanisms to improve performance and reduce server load. These mechanisms include:

1. **HTTP Cache Headers**: Instruct browsers and CDNs how to cache responses
2. **ETag Support**: Enable efficient cache validation
3. **Conditional Requests**: Reduce bandwidth by returning 304 Not Modified when appropriate

These mechanisms work together to provide a robust caching system that balances performance with data freshness.

## Caching Mechanisms

### HTTP Cache Headers

**Location**: `Sanatana_Service/root_app.py` and `selection_jobs/job_status_service.py`

**Description**:
HTTP cache headers instruct browsers, CDNs, and other intermediaries how to cache responses.

**Key Headers**:
- `Cache-Control`: Specifies caching directives
- `ETag`: Provides a version identifier for the resource
- `Vary`: Indicates how responses are varied (e.g., by Accept-Encoding)

**Example Headers**:
```
Cache-Control: public, max-age=300, stale-while-revalidate=600
ETag: "1234567890-1609459200"
Vary: Accept-Encoding, User-Agent
```

**Cache-Control Directives**:
- `public`: Response can be cached by browsers and intermediaries
- `private`: Response can only be cached by browsers
- `max-age`: Maximum time in seconds to cache the response
- `stale-while-revalidate`: Time in seconds during which a stale response can be used while revalidating
- `no-cache`: Response must be validated before using
- `no-store`: Response must not be stored in any cache

### ETag Support

**Location**: `Sanatana_Service/root_app.py` and `selection_jobs/job_status_service.py`

**Description**:
ETags provide a way to validate if a cached response is still valid without transferring the entire response.

**How it works**:
1. Server includes an ETag header in the response
2. Client stores the ETag with the cached response
3. On subsequent requests, client includes If-None-Match header with the stored ETag
4. If the resource hasn't changed, server returns 304 Not Modified without the response body
5. If the resource has changed, server returns 200 OK with the new response and a new ETag

**Implementation**:
```python
@app.after_request
def add_cache_headers(response):
    # Add ETag header based on response content and timestamp
    response.headers['ETag'] = f'"{hash(response.get_data())}-{int(time.time())}"'
    # ...
```

### Conditional Requests

**Description**:
Conditional requests allow clients to ask the server if a cached response is still valid.

**Key Headers**:
- `If-None-Match`: Contains the ETag from a previous response
- `If-Modified-Since`: Contains the Last-Modified date from a previous response

**How it works**:
1. Client includes conditional headers in the request
2. Server checks if the resource has changed
3. If not changed, server returns 304 Not Modified
4. If changed, server returns 200 OK with the new response

## Service-Specific Caching

### Main Service (Sanatana_Service)

**Location**: `Sanatana_Service/root_app.py` and `Sanatana_Service/cache_middleware.py`

**Caching Strategy**:
- Static assets: Aggressive caching (1 week)
- GET API responses: Moderate caching (5 minutes)
- Authentication endpoints: No caching
- User-specific data: Private caching with short duration

**Cache Configuration**:
```python
@app.after_request
def add_cache_headers(response):
    # Get the path from the request
    path = request.path
    
    # Set default cache control header
    response.headers['Cache-Control'] = 'no-cache'
    
    # Add Vary header to properly handle cached responses
    response.headers['Vary'] = 'Accept-Encoding, User-Agent'
    
    # Add ETag header based on response content and timestamp
    response.headers['ETag'] = f'"{hash(response.get_data())}-{int(time.time())}"'
    
    # Cache static assets aggressively
    if path.endswith(('.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.woff2', '.ttf', '.eot')):
        # Cache for 1 week
        response.headers['Cache-Control'] = 'public, max-age=604800, stale-while-revalidate=86400'
    
    # Cache API responses that are read-only (GET requests)
    elif request.method == 'GET' and not any(x in path for x in ['/authenticate', '/login', '/signin', '/token']):
        # Cache for 5 minutes, but allow stale responses while revalidating
        response.headers['Cache-Control'] = 'public, max-age=300, stale-while-revalidate=600'
    
    return response
```

**Decorator-Based Caching**:
The `cache_middleware.py` file provides decorators for fine-grained control:
```python
@app.route('/api/static-data')
@cache_for_day()
def get_static_data():
    # This response will be cached for 1 day
    # ...

@app.route('/api/user-profile')
@no_cache()
def get_user_profile():
    # This response will not be cached
    # ...
```

### Job Status Service

**Location**: `selection_jobs/job_status_service.py`

**Caching Strategy**:
- Static assets: Aggressive caching (1 week)
- GET API responses (except job status): Short caching (1 minute)
- Job status endpoints: No caching

**Cache Configuration**:
```python
@app.after_request
def add_cache_headers(response):
    # Get the path from the request
    path = request.path
    
    # Set default cache control header
    response.headers['Cache-Control'] = 'no-cache'
    
    # Add Vary header to properly handle cached responses
    response.headers['Vary'] = 'Accept-Encoding, User-Agent'
    
    # Add ETag header based on response content and timestamp
    response.headers['ETag'] = f'"{hash(response.get_data())}-{int(time.time())}"'
    
    # Cache static assets aggressively
    if path.endswith(('.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.woff2', '.ttf', '.eot')):
        # Cache for 1 week
        response.headers['Cache-Control'] = 'public, max-age=604800, stale-while-revalidate=86400'
    
    # Cache API responses that are read-only (GET requests) and not job status
    elif request.method == 'GET' and not path.startswith('/api/jobs/status/'):
        # Cache for 1 minute, but allow stale responses while revalidating
        response.headers['Cache-Control'] = 'public, max-age=60, stale-while-revalidate=300'
    
    return response
```

### YouTube Download Service

**Location**: Not yet implemented in the caching system

**Recommended Caching Strategy**:
- Video metadata: Moderate caching (1 hour)
- Download endpoints: No caching
- Static assets: Aggressive caching (1 week)

**Suggested Implementation**:
```python
@app.after_request
def add_cache_headers(response):
    path = request.path
    
    # Set default cache control header
    response.headers['Cache-Control'] = 'no-cache'
    
    # Add Vary header
    response.headers['Vary'] = 'Accept-Encoding, User-Agent'
    
    # Add ETag header
    response.headers['ETag'] = f'"{hash(response.get_data())}-{int(time.time())}"'
    
    # Cache static assets
    if path.endswith(('.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.woff2', '.ttf', '.eot')):
        response.headers['Cache-Control'] = 'public, max-age=604800, stale-while-revalidate=86400'
    
    # Cache video metadata
    elif path.startswith('/video-info'):
        response.headers['Cache-Control'] = 'public, max-age=3600, stale-while-revalidate=7200'
    
    return response
```

## How Caching Affects Operations

### Positive Effects

1. **Reduced Server Load**:
   - Fewer requests for static assets
   - Less CPU and memory usage
   - Lower bandwidth consumption

2. **Improved Response Times**:
   - Cached responses are served faster
   - Conditional requests reduce payload size
   - CDNs can serve cached content from edge locations

3. **Better Scalability**:
   - Caching reduces the load on backend services
   - More concurrent users can be supported

4. **Reduced Costs**:
   - Lower bandwidth usage
   - Fewer compute resources needed

### Potential Issues

1. **Stale Data**:
   - Cached responses might not reflect the latest server state
   - Users might see outdated information

2. **Cache Inconsistency**:
   - Different users might see different versions of the same resource
   - Cache invalidation across multiple services can be challenging

3. **Debugging Complexity**:
   - Harder to trace issues when responses might be cached
   - Behavior differences between development and production

4. **Cache Busting Challenges**:
   - When deploying updates, cached resources need to be invalidated
   - Requires careful versioning or cache control

## Debugging Caching Issues

### Server-Side Debugging

**Logging Cache Headers**:
```python
@app.after_request
def log_cache_headers(response):
    app.logger.debug(f"Cache-Control: {response.headers.get('Cache-Control')}")
    app.logger.debug(f"ETag: {response.headers.get('ETag')}")
    return response
```

**Analyzing Conditional Requests**:
```python
@app.before_request
def log_conditional_headers():
    if 'If-None-Match' in request.headers:
        app.logger.debug(f"Conditional request with ETag: {request.headers.get('If-None-Match')}")
```

**Temporarily Disabling Caching**:
```python
@app.after_request
def disable_caching_for_debugging(response):
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response
```

### Client-Side Debugging

**Using curl**:
```bash
# Make a request and show headers
curl -v https://service.sanatanamedia.com/api/endpoint

# Make a conditional request with ETag
curl -v -H "If-None-Match: \"1234567890-1609459200\"" https://service.sanatanamedia.com/api/endpoint
```

**Using Postman**:
1. Send a request to the endpoint
2. Observe the Cache-Control and ETag headers in the response
3. Send the same request again with the "Send and Download" option
4. Check if you get a 304 Not Modified response

## Disabling Caching

### Main Service

**Temporary Disable**:
Edit `root_app.py` and modify the `add_cache_headers` function:
```python
@app.after_request
def add_cache_headers(response):
    # Disable caching for all responses
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response
```

**Selective Disable**:
Use the `no_cache` decorator from `cache_middleware.py`:
```python
from cache_middleware import no_cache

@app.route('/api/endpoint')
@no_cache()
def get_data():
    # This endpoint will not be cached
    # ...
```

### Job Status Service

Similar to the main service, edit the `add_cache_headers` function in `job_status_service.py`.

### Client-Side Cache Busting

**URL Parameters**:
Add a timestamp or random value to the URL:
```
https://service.sanatanamedia.com/api/endpoint?_t=1609459200
```

**Headers**:
Add cache-busting headers to the request:
```
Cache-Control: no-cache
Pragma: no-cache
```

## Caching Analysis Tools

### 1. HTTP Analysis Tools

- **curl**: Command-line tool for HTTP requests
  ```bash
  curl -v https://service.sanatanamedia.com/api/endpoint
  ```

- **Postman**: GUI tool for API testing
  - Shows response headers
  - Can save and compare responses

- **Charles Proxy** or **Fiddler**: HTTP debugging proxies
  - Intercept and analyze HTTP traffic
  - Modify requests and responses

### 2. Server Monitoring

- **Flask Debug Toolbar**: Shows request/response details
- **Application Performance Monitoring (APM)** tools:
  - New Relic
  - Datadog
  - Prometheus + Grafana

### 3. Custom Monitoring

Add custom monitoring to track cache effectiveness:
```python
# In a middleware or decorator
def track_cache_effectiveness(response):
    if response.status_code == 304:
        # Cache hit (304 Not Modified)
        cache_hits.inc()
    else:
        # Cache miss (200 OK)
        cache_misses.inc()
    return response
```

## Precautions and Best Practices

### 1. Data Freshness Considerations

**Critical data that should always be fresh**:
- Job status information
- Authentication status
- User profile data

**Data that can be cached safely**:
- Static assets
- Configuration data
- Historical data

### 2. Cache Invalidation

**When to invalidate cache**:
- After updating resources
- When deploying new versions
- When critical data changes

**How to invalidate cache**:
- Change resource URLs (e.g., add version parameter)
- Use short cache durations for dynamic content
- Implement purge mechanisms for CDNs

### 3. Testing Considerations

- Test with both empty and primed caches
- Verify conditional requests work correctly
- Check cache behavior across different environments

### 4. Security Considerations

- Don't cache sensitive data
- Use `private` directive for user-specific data
- Be careful with Vary header to prevent cache poisoning

## Frequently Asked Questions

### How do I know if my responses are being cached?

1. Check the Cache-Control header in the response
2. Look for 304 Not Modified responses
3. Monitor server logs for conditional requests
4. Use browser developer tools to see if resources are served from cache

### How can I force a response to not be cached?

```python
@app.route('/api/sensitive-data')
def get_sensitive_data():
    response = make_response(...)
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response
```

### What's the difference between no-cache and no-store?

- `no-cache`: Response can be stored but must be validated before use
- `no-store`: Response must not be stored in any cache

### How do I handle user-specific data?

Use the `private` directive and include user-specific information in the Vary header:
```python
response.headers['Cache-Control'] = 'private, max-age=60'
response.headers['Vary'] = 'Accept-Encoding, User-Agent, Authorization'
```

### How do I implement cache busting for static assets?

1. **Filename versioning**:
   ```
   style.css -> style.v1.css
   ```

2. **Query parameters**:
   ```
   style.css?v=1
   ```

3. **Content-based hashing**:
   ```
   style.css -> style.a1b2c3d4.css
   ```

### How do ETags work with conditional requests?

1. Server sends response with ETag:
   ```
   ETag: "1234567890-1609459200"
   ```

2. Client stores the ETag and makes a conditional request:
   ```
   If-None-Match: "1234567890-1609459200"
   ```

3. Server checks if the resource has changed:
   - If not changed: Returns 304 Not Modified (no body)
   - If changed: Returns 200 OK with new content and new ETag

### What's the purpose of the Vary header?

The Vary header tells caches that the response varies based on certain request headers. This prevents serving the wrong cached response to different clients.

Example:
```
Vary: Accept-Encoding, User-Agent
```

This means the response might be different for:
- Different Accept-Encoding values (e.g., gzip vs. no compression)
- Different User-Agent values (e.g., mobile vs. desktop)
