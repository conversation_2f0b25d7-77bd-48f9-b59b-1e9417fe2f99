from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import os
import sqlite3
import requests
import time
import json
import urllib.parse

from datetime import datetime, timedelta, timezone
import pytz
import math

GOOGLE_API_KEY = "AIzaSyBLotitbfYWRXAKa_EsShAyy6ilF8WOW9Q"

@app.route("/gfolder_list_files", methods=["GET"])
def gfolder_list_files():
    """API endpoint to get sorted file names and IDs from a Google Drive folder."""
    folder_id = request.args.get("folder_id")
    if not folder_id:
        return jsonify({"error": "folder_id is required"}), 400

    files = get_drive_files(folder_id)
    return jsonify(files)



def get_drive_files(folder_id):
    """Fetches file IDs and names from a Google Drive folder, sorted by name."""
    url = f"https://www.googleapis.com/drive/v3/files"
    params = {
        "q": f"'{folder_id}' in parents and trashed=false",
        "key": GOOGLE_API_KEY,
        "fields": "files(id, name)",
    }

    response = requests.get(url, params=params)
    if response.status_code != 200:
        return {"error": "Failed to fetch files", "status": response.status_code}

    files = response.json().get("files", [])
    # Sort files by name
    sorted_files = sorted(files, key=lambda x: x["name"].lower())

    return sorted_files
