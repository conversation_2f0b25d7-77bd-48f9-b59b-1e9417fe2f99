import os
import json

def determine_database_path():
    home_dir = os.path.expanduser('~')
    cwd = os.getcwd()
    
    if 'socialmediaupload_dev'.lower() in cwd.lower():
        return os.path.join(home_dir, 'sanatana_db', 'sanatana_media_dev.db')
    else:
        return os.path.join(home_dir, 'sanatana_db', 'sanatana_media.db')    

def determine_ports_file():
    cwd = os.getcwd()
    if 'socialmediaupload_dev'.lower() in cwd.lower():
        return 'global_ports.dev.json'
    else:
        return 'global_ports.json'
    
def determine_domains_file():
    cwd = os.getcwd()
    if 'socialmediaupload_dev'.lower() in cwd.lower():
        return 'sanatana_domains.dev.json'
    else:
        return 'sanatana_domains.json'
    
def save_port_in_global_ports(service_name, port):
    # Define JSON file path (one directory level above)
    GLOBAL_PORTS_FILE = determine_ports_file()
    global_ports_file = os.path.join(os.path.dirname(os.getcwd()), GLOBAL_PORTS_FILE)

    # Load existing ports if file exists
    if os.path.exists(global_ports_file):
        with open(global_ports_file, "r") as f:
            global_ports = json.load(f)
    else:
        global_ports = {}

    # Update or create VIDEO_SPLIT_PORT entry
    global_ports[service_name] = port

    # Write back to global ports file
    with open(global_ports_file, "w") as f:
        json.dump(global_ports, f, indent=4)

    print(f"{service_name} set to {port} and saved in {global_ports_file}")
    
def get_service_port(service_name):
    
    GLOBAL_PORTS_FILE = determine_ports_file()
    # Define JSON file path (one directory level above)
    global_ports_file = os.path.join(os.path.dirname(os.getcwd()), GLOBAL_PORTS_FILE)

    port = None
    # Check if the file exists
    if os.path.exists(global_ports_file):
        with open(global_ports_file, "r") as f:
            global_ports = json.load(f)
            port =  global_ports.get(service_name)
            print(f"Success port for service {service_name} is {port}") 

    # Ensure we have a valid port
    if not port:
        print(f"Error: {service_name} not found in {global_ports_file}. Make sure the service is running.")
        exit(1)
    
    return port