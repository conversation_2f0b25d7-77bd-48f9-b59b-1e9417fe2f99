import argparse
import httplib2
from googleapiclient.discovery import build
from oauth2client.file import Storage
from oauth2client.client import flow_from_clientsecrets
from oauth2client.tools import run_flow
import requests
import os
import sys
import json
import google.auth.transport.requests
import google.oauth2.credentials


# YouTube API scope for managing playlists
YOUTUBE_SCOPE = "https://www.googleapis.com/auth/youtube"

# YouTube API details
YOUTUBE_API_SERVICE_NAME = "youtube"
YOUTUBE_API_VERSION = "v3"

service_name = "SANATANA_SERVICE"
# Get the parent directory of the current script
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))


# Add it to sys.path
sys.path.append(parent_dir)

from Utils.globals import get_service_port, determine_domains_file

# Retrieve the service port from the environment variable
PORT = get_service_port(service_name)

GLOBAL_DOMAINS_FILE = determine_domains_file()

# Load SANATANA_SERVICE_DOMAIN from global ports file
global_config_path = os.path.join(
    os.path.dirname(__file__), "..", GLOBAL_DOMAINS_FILE
)
with open(global_config_path, "r") as f:
    global_config = json.load(f)

SANATANA_SERVICE_DOMAIN = global_config.get("SANATANA_SERVICE_DOMAIN", "")


def get_authenticated_service(channel_email, needed_quota):
    """Retrieve an access token and the correct YouTube channel ID for a user."""
    print("channel_email to get authenticated: ", channel_email)
    response = requests.get(
        f"{SANATANA_SERVICE_DOMAIN}/youtube/refresh_token/{channel_email}",
        params={"needed_quota": needed_quota},
    )

    if response.status_code != 200:
        return {
            "status": "FAILED",
            "data": f"Failed to fetch access token: {response.json()}",
        }

    data = response.json()

    if("status" in data and data["status"] != "SUCCESS"):
        return {"status": "FAILED", "data": data}
    
    if "error" in data:
        return {"status": "FAILED", "data": data}

 
    
   # print("get_authenticated_service: data", data)

    access_token = data["access_token"]
    client_id = data["client_id"]
    channels = data["channels"]
    # print("YoutubeAuth: get_authenticated_service: response:", json.dumps(data, indent=4))

    if not access_token or not client_id:
        return {"status": "FAILED", "data": data}

    # Use google-auth library for authentication
    credentials = google.oauth2.credentials.Credentials(access_token)
    youtube = build(
        YOUTUBE_API_SERVICE_NAME, YOUTUBE_API_VERSION, credentials=credentials
    )

    return {"status": "SUCCESS", "client_id": client_id, 
            "youtube": youtube, "channels": channels}
