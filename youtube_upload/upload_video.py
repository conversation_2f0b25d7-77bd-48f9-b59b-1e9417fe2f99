import http.client
import httplib2
import os
import random
import sys
import time
import requests

import argparse
from googleapiclient.errors import Http<PERSON>rror
from googleapiclient.http import MediaFileUpload
from youtube_access import get_youtube_access
# Explicitly tell the underlying HTTP transport library not to retry, since
# we are handling retry logic ourselves.
httplib2.RETRIES = 1

# Maximum number of times to retry before giving up.
MAX_RETRIES = 10

# Always retry when these exceptions are raised.
RETRIABLE_EXCEPTIONS = (
    httplib2.HttpLib2Error,
    IOError,
    http.client.NotConnected,
    http.client.IncompleteRead,
    http.client.ImproperConnectionState,
    http.client.CannotSendRequest,
    http.client.CannotSendHeader,
    http.client.ResponseNotReady,
    http.client.BadStatusLine,
)

# Always retry when an googleapiclient.errors.HttpError with one of these status
# codes is raised.
RETRIABLE_STATUS_CODES = [500, 502, 503, 504]


VALID_PRIVACY_STATUSES = ("public", "private", "unlisted")

def get_tags (options):
    tags = options.keywords.split(",") if options.keywords else None
    if tags:
        tags = [tag.lower() for tag in tags]
        tags = list(dict.fromkeys(tag.strip() for tag in tags))  # Remove duplicates & strip spaces
        total_length = 0
        filtered_tags = []
        for tag in tags:
            if total_length + len(tag) + 1 > 500:  # +1 for comma separator
                break
            filtered_tags.append(tag)
            total_length += len(tag) + 1  # Include comma in count
        tags =  filtered_tags  # Assign filtered tags
    return tags

def initialize_upload(youtube, options):
    tags =  get_tags (options)

    # Truncate title to 70 characters - optimal Youtube title length
    truncated_title = options.title[:70]

     # Truncate description to 4500 characters - optimal, so we can add more to it later
    truncated_description= options.description[:4500]

    body = {
        "snippet": {
            "title": truncated_title,
            "description": truncated_description,
            "tags": tags,
            "categoryId": options.category,
        },
        "status": {"privacyStatus": options.privacyStatus},
    }

    insert_request = youtube.videos().insert(
        part=",".join(body.keys()),
        body=body,
        media_body=MediaFileUpload(options.file, chunksize=-1, resumable=True),
    )

    # Get the video ID from the upload process
    video_id = resumable_upload(insert_request)
    return video_id


def resumable_upload(insert_request):
    response = None
    error = None
    retry = 0
    video_id = None

    while response is None:
        try:
            print("Uploading file...")
            _, response = insert_request.next_chunk()  # Use _ for unused status variable
            if response is not None:
                if "id" in response:
                    video_id = response['id']
                    print(f"Video id: '{video_id}' - was successfully uploaded.")
                else:
                    sys.exit(
                        f"The upload failed with an unexpected response: {response}"
                    )
        except HttpError as e:
            if e.resp.status in RETRIABLE_STATUS_CODES:
                error = f"A retriable HTTP error {e.resp.status} occurred:\n{e.content}"
            else:
                raise
        except RETRIABLE_EXCEPTIONS as e:
            error = f"A retriable error occurred: {e}"

        if error:
            print(error)
            retry += 1
            if retry > MAX_RETRIES:
                sys.exit("No longer attempting to retry.")

            max_sleep = 2**retry
            sleep_seconds = random.random() * max_sleep
            print(f"Sleeping {sleep_seconds:.2f} seconds and then retrying...")
            time.sleep(sleep_seconds)

    return video_id


# Define quota needed at module level
QUOTA_NEEDED = 1600

def upload_video_method(file, title="Test Title", description="Test Description", category="22", keywords="", privacyStatus="public", email=None):
    """Method to upload a video to YouTube without using command line arguments"""
    import os
    from googleapiclient.errors import HttpError

    if not os.path.exists(file):
        return {"status": "ERROR", "error": "Please specify a valid file."}

    if not email:
        return {"status": "ERROR", "error": "Please specify an email of channel."}

    # Create an object to hold the arguments
    class Args:
        pass

    args = Args()
    args.file = file
    args.title = title[:70]  # Ensure title is within 70 chars
    args.description = description
    args.category = category
    args.keywords = keywords
    args.privacyStatus = privacyStatus
    args.email = email

    # Get YouTube access
    access = get_youtube_access(email, QUOTA_NEEDED)

    if access["status"] != "SUCCESS":
        return {"status": "ERROR", "error": access["error"]}

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        # Get the video ID from the upload process
        video_id = initialize_upload(youtube, args)

        # Update quota usage
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": QUOTA_NEEDED})

        # Return success with the video ID
        return {
            "status": "SUCCESS",
            "video_id": video_id,
            "youtube_link": f"https://www.youtube.com/watch?v={video_id}"
        }
    except HttpError as e:
        error_message = f"An HTTP error {e.resp.status} occurred:\n{e.content}"
        return {"status": "ERROR", "error": error_message}


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--file", required=True, help="Video file to upload")
    parser.add_argument("--title", help="Video title", default="Test Title")
    parser.add_argument(
        "--description", help="Video description", default="Test Description"
    )
    parser.add_argument(
        "--category",
        default="22",
        help="Numeric video category. See https://developers.google.com/youtube/v3/docs/videoCategories/list",
    )
    parser.add_argument(
        "--keywords", help="Video keywords, comma separated", default=""
    )
    parser.add_argument(
        "--privacyStatus",
        choices=VALID_PRIVACY_STATUSES,
        default=VALID_PRIVACY_STATUSES[0],
        help="Video privacy status.",
    )
    parser.add_argument("--email", required=True)

    args = parser.parse_args()

    if not os.path.exists(args.file):
        sys.exit("Please specify a valid file using the --file= parameter.")

    if not args.email:
        sys.exit("Please specify a email of channel")

    ########################################################################
    ########################################################################

    # Get YouTube access
    access = get_youtube_access(args.email, QUOTA_NEEDED)

    if (access["status"] != "SUCCESS"):
        print("FAILED:", access["error"])
        sys.exit(f"FAILED: {access['error']}")

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        initialize_upload(youtube, args)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": QUOTA_NEEDED})
    except HttpError as e:
        print(f"An HTTP error {e.resp.status} occurred:\n{e.content}")

if __name__ == "__main__":
    #main()
    ...

    """
    Sample Execute:

    python upload_video.py --file="Sadhguru Mother Tongue is Telugu.mp4" ^
                       --title="Sadhguru Mother Tongue is Telugu" ^
                       --description="Sadhguru is Telugu man" ^
                       --keywords="SanatanaDharma, Hinduism, Sadhguru, Telugu" ^
                       --category="22" ^
                       --privacyStatus="private" ^
                       --email=<EMAIL>
    """
