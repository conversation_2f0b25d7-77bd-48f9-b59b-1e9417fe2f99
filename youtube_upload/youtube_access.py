import os
import json
from YoutubeAuth import get_authenticated_service

import sys
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from Utils.globals import determine_domains_file
GLOBAL_DOMAINS_FILE = determine_domains_file()

# Load configuration once at module level
global_config_path = os.path.join(os.path.dirname(__file__), '..', GLOBAL_DOMAINS_FILE)
with open(global_config_path, 'r') as f:
    global_config = json.load(f)

SANATANA_SERVICE_DOMAIN = global_config.get("SANATANA_SERVICE_DOMAIN", "")

def get_youtube_access(email, quota_needed):
    """
    Get authenticated YouTube service and client_id
    
    Args:
        email (str): The email of the channel
        quota_needed (int): The quota needed for the operation
        
    Returns:
        dict: A dictionary containing:
            - status: "SUCCESS" or "ERROR"
            - youtube: The authenticated YouTube service (if successful)
            - client_id: The client ID (if successful)
            - error: Error message (if failed)
            - sanatana_service_domain: The Sanatana service domain
    """
    ret = get_authenticated_service(email, quota_needed)
    
    if ret["status"] != "SUCCESS":
        return {
            "status": "ERROR", 
            "error": ret["data"],
            "sanatana_service_domain": SANATANA_SERVICE_DOMAIN
        }
    
    return {
        "status": "SUCCESS",
        "youtube": ret["youtube"],
        "client_id": ret["client_id"],
        "sanatana_service_domain": SANATANA_SERVICE_DOMAIN,
        "channels": ret["channels"],
    }
