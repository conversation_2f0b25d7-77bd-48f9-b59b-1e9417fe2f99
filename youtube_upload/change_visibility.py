
import argparse
import os
import sys
import requests
from googleapiclient.errors import HttpError
from youtube_access import get_youtube_access

VALID_PRIVACY_STATUSES = ("public", "private", "unlisted")

def update_video_privacy(youtube, video_id, privacy_status):
    """Update the privacy status of a YouTube video."""
    if privacy_status not in VALID_PRIVACY_STATUSES:
        raise ValueError(
            f"Invalid privacy status: {privacy_status}. Must be one of {VALID_PRIVACY_STATUSES}."
        )

    request_body = {"id": video_id, "status": {"privacyStatus": privacy_status}}

    youtube.videos().update(part="status", body=request_body).execute()

    print(f"Updated video {video_id} privacy status to {privacy_status}")


# Define quota needed at module level
QUOTA_NEEDED = 50

def change_visibility_method(video_id, privacy_status, email):
    """Method to change a video's privacy status without using command line arguments"""
    from googleapiclient.errors import HttpError

    if not video_id:
        return {"status": "ERROR", "error": "Please specify a video ID."}

    if not privacy_status or privacy_status not in VALID_PRIVACY_STATUSES:
        return {"status": "ERROR", "error": f"Invalid privacy status. Must be one of {VALID_PRIVACY_STATUSES}."}

    if not email:
        return {"status": "ERROR", "error": "Please specify an email of channel."}

    # Get YouTube access
    access = get_youtube_access(email, QUOTA_NEEDED)

    if access["status"] != "SUCCESS":
        return {"status": "ERROR", "error": access["error"]}

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        update_video_privacy(youtube, video_id, privacy_status)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": QUOTA_NEEDED})
        return {"status": "SUCCESS", "video_id": video_id, "privacy_status": privacy_status}
    except HttpError as e:
        error_message = f"An HTTP error {e.resp.status} occurred:\n{e.content}"
        return {"status": "ERROR", "error": error_message}

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--video_id", required=True, help="YouTube video ID")
    parser.add_argument(
        "--privacy_status",
        required=True,
        choices=VALID_PRIVACY_STATUSES,
        help="New privacy status (public, private, unlisted)",
    )
    parser.add_argument("--email", required=True)

    args = parser.parse_args()

    import sys
    if not args.email:
        sys.exit("Please specify a email of channel")

    ########################################################################
    ########################################################################

    # Get YouTube access
    access = get_youtube_access(args.email, QUOTA_NEEDED)

    if (access["status"] != "SUCCESS"):
        print("FAILED:", access["error"])
        sys.exit(f"FAILED: {access['error']}")

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        update_video_privacy(youtube, args.video_id, args.privacy_status)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": QUOTA_NEEDED})

    except HttpError as e:
        print(f"An HTTP error {e.resp.status} occurred:\n{e.content}")

    """
    Usage:
    python change_visibility.py --video_id="jvJ-gRVHjyw" --privacy_status="public" --email=<EMAIL>

    """
