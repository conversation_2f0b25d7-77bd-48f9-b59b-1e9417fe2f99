import argparse
import os
import sys
import requests
from googleapiclient.errors import HttpError
from youtube_access import get_youtube_access

total_quota_used = 0

def update_quota_used(quota):
    global total_quota_used  # Declare the global variable
    total_quota_used += quota  # Modify the global variable
    print("Updated quota used: ", total_quota_used)

def get_or_create_playlist(youtube, playlist_name):
    """Check if a playlist exists, and create it if not."""
    response = (
        youtube.playlists().list(part="id,snippet", mine=True, maxResults=50).execute()
    )
    update_quota_used(1)

    for playlist in response.get("items", []):
        if playlist["snippet"]["title"] == playlist_name:
            print(f"Found existing playlist: {playlist_name}")
            return playlist["id"]

    # Create a new playlist if it doesn't exist
    playlist_body = {
        "snippet": {
            "title": playlist_name,
            "description": f"Playlist for {playlist_name}",
        },
        "status": {"privacyStatus": "public"},
    }

    playlist_response = (
        youtube.playlists().insert(part="snippet,status", body=playlist_body).execute()
    )
    update_quota_used(50)

    print(f"Created new playlist: {playlist_name}")
    return playlist_response["id"]


def add_video_to_playlist(youtube, video_id, playlist_id):
    """Add a video to the specified playlist."""
    playlist_item_body = {
        "snippet": {
            "playlistId": playlist_id,
            "resourceId": {"kind": "youtube#video", "videoId": video_id},
        }
    }

    youtube.playlistItems().insert(part="snippet", body=playlist_item_body).execute()

    update_quota_used(50)

    print(f"Added video {video_id} to playlist {playlist_id}")


# Define quota needed at module level
QUOTA_NEEDED = 50

def add_to_playlist_method(video_id, playlist, email):
    """Method to add a video to a playlist without using command line arguments"""
    from googleapiclient.errors import HttpError

    # Reset the global quota counter
    global total_quota_used
    total_quota_used = 0

    if not video_id:
        return {"status": "ERROR", "error": "Please specify a video ID."}

    if not playlist:
        return {"status": "ERROR", "error": "Please specify a playlist name."}

    if not email:
        return {"status": "ERROR", "error": "Please specify an email of channel."}

    # Get YouTube access
    access = get_youtube_access(email, QUOTA_NEEDED)

    if access["status"] != "SUCCESS":
        return {"status": "ERROR", "error": access["error"]}

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        playlist_id = get_or_create_playlist(youtube, playlist)
        add_video_to_playlist(youtube, video_id, playlist_id)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": total_quota_used})
        return {"status": "SUCCESS", "playlist_id": playlist_id}
    except HttpError as e:
        error_message = f"An HTTP error {e.resp.status} occurred:\n{e.content}"
        return {"status": "ERROR", "error": error_message}

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--video_id", required=True, help="YouTube video ID")
    parser.add_argument("--playlist", required=True, help="Playlist name")
    parser.add_argument("--email", required=True)

    args = parser.parse_args()

    import sys
    if not args.email:
        sys.exit("Please specify a email of channel")

    ########################################################################
    ########################################################################



    # Get YouTube access
    access = get_youtube_access(args.email, QUOTA_NEEDED)

    if (access["status"] != "SUCCESS"):
        print("FAILED:", access["error"])
        sys.exit(f"FAILED: {access['error']}")

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        playlist_id = get_or_create_playlist(youtube, args.playlist)
        add_video_to_playlist(youtube, args.video_id, playlist_id)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": total_quota_used})

    except HttpError as e:
        print(f"An HTTP error {e.resp.status} occurred:\n{e.content}")

    """
    Usage:
    python add_to_playlist.py --video_id="jvJ-gRVHjyw" --playlist="Sadhguru Videos" --email=<EMAIL>

    """
