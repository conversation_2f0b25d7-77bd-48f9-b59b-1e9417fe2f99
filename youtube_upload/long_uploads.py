
import argparse
import os
import sys
import requests
from googleapiclient.errors import HttpError
from youtube_access import get_youtube_access
import json

# Define quota needed at module level
QUOTA_NEEDED = 1
def check_channel_verification_and_long_uploads(youtube, channel_id):
   
    # Fetch the channel information
    request = youtube.channels().list(part="snippet,contentDetails,statistics", id=channel_id)
    response = request.execute()

    # Extract relevant data
    channel_info = response.get('items', [])[0]
    is_verified = channel_info['snippet']['verified'] if 'verified' in channel_info['snippet'] else False
    can_upload_long_videos = channel_info['contentDetails']['relatedPlaylists']['uploads'] is not None

    return is_verified, can_upload_long_videos

def get_long_video_status(email):
    """Method to change a video's privacy status without using command line arguments"""
    from googleapiclient.errors import HttpError

    if not email:
        return {"status": "ERROR", "error": "Please specify an email of channel."}

    # Get YouTube access
    access = get_youtube_access(email, QUOTA_NEEDED)

    if access["status"] != "SUCCESS":
        return {"status": "ERROR", "error": access["error"]}

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]
    channels = access["channels"]
    channel_id = channels[0]['channel_id']
    print("channel_id", channel_id)
    
    try:
        verified, long_uploads = check_channel_verification_and_long_uploads(youtube, channel_id)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": QUOTA_NEEDED})
        return {"status": "SUCCESS", "verified": verified, "long_uploads": long_uploads}
    
    except HttpError as e:
        error_message = f"An HTTP error {e.resp.status} occurred:\n{e.content}"
        return {"status": "ERROR", "error": error_message}

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--email", required=True)

    args = parser.parse_args()

    import sys
    if not args.email:
        sys.exit("Please specify a email of channel")

    ########################################################################
    ########################################################################

    status =  get_long_video_status(args.email)
    print("Status: ", json.dumps(status))

if __name__ == "__main__":
    #main()
    ... 

    """
    Usage:
    python long_uploads .py  --email=<EMAIL>

    """
