import argparse
import os
import sys
import requests
from googleapiclient.errors import HttpError
from youtube_access import get_youtube_access

def get_video_metadata(youtube, video_id):
    """Fetch the current video title, description, and category ID."""
    response = youtube.videos().list(part="snippet", id=video_id).execute()

    items = response.get("items", [])
    if not items:
        raise ValueError(f"Video with ID {video_id} not found.")

    snippet = items[0]["snippet"]
    return snippet["title"], snippet["description"], snippet["categoryId"]


def update_video_description(youtube, video_id, text_to_prepend):
    """Prepend text to the video description while keeping the existing title and category ID."""
    current_title, current_description, category_id = get_video_metadata(
        youtube, video_id
    )
    new_description = f"{text_to_prepend}\n\n{current_description}"

    request_body = {
        "id": video_id,
        "snippet": {
            "title": current_title,  # Keep the original title
            "description": new_description,
            "categoryId": category_id,  # Keep the original category
        },
    }

    youtube.videos().update(part="snippet", body=request_body).execute()

    print(f"Updated video {video_id} description successfully.")


# Define quota needed at module level
QUOTA_NEEDED = 50

def prepend_description_method(video_id, text, email):
    """Method to prepend text to a video description without using command line arguments"""
    from googleapiclient.errors import HttpError

    if not video_id:
        return {"status": "ERROR", "error": "Please specify a video ID."}

    if not text:
        return {"status": "ERROR", "error": "Please specify text to prepend."}

    if not email:
        return {"status": "ERROR", "error": "Please specify an email of channel."}

    # Get YouTube access
    access = get_youtube_access(email, QUOTA_NEEDED)

    if access["status"] != "SUCCESS":
        return {"status": "ERROR", "error": access["error"]}

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        update_video_description(youtube, video_id, text)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": QUOTA_NEEDED})
        return {"status": "SUCCESS", "video_id": video_id}
    except HttpError as e:
        error_message = f"An HTTP error {e.resp.status} occurred:\n{e.content}"
        return {"status": "ERROR", "error": error_message}

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--video_id", required=True, help="YouTube video ID")
    parser.add_argument(
        "--text", required=True, help="Text to prepend to the description"
    )
    parser.add_argument("--email", required=True)

    args = parser.parse_args()

    import sys
    if not args.email:
        sys.exit("Please specify a email of channel")

    ########################################################################
    ########################################################################

    # Get YouTube access
    access = get_youtube_access(args.email, QUOTA_NEEDED)

    if (access["status"] != "SUCCESS"):
        print("FAILED:", access["error"])
        sys.exit(f"FAILED: {access['error']}")

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        update_video_description(youtube, args.video_id, args.text)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": QUOTA_NEEDED})
    except HttpError as e:
        print(f"An HTTP error {e.resp.status} occurred:\n{e.content}")

if __name__ == "__main__":
    #main()
    ...


"""

Usage:
python prepend_description.py --video_id="3wl4CHbzees" --text="Make sure to Watch Part 1" --email=<EMAIL>

"""
