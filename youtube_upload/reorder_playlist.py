import argparse
import os
import sys
import requests
from googleapiclient.errors import HttpError
from youtube_access import get_youtube_access

total_quota_used = 0

def update_quota_used(quota):
    global total_quota_used  # Declare the global variable
    total_quota_used += quota  # Modify the global variable
    print("Updated quota used: ", total_quota_used)

def get_playlist_videos(youtube, playlist_id):
    """Retrieve all videos in the playlist, including their playlist item IDs and positions."""
    videos = []
    next_page_token = None

    while True:
        response = (
            youtube.playlistItems()
            .list(
                part="snippet",
                playlistId=playlist_id,
                maxResults=50,
                pageToken=next_page_token,
            )
            .execute()
        )
        update_quota_used(1)

        for item in response["items"]:
            videos.append(
                {
                    "playlistItemId": item["id"],  # Needed for reordering
                    "videoId": item["snippet"]["resourceId"]["videoId"],
                    "title": item["snippet"]["title"],
                    "position": item["snippet"]["position"],  # Current position
                }
            )

        next_page_token = response.get("nextPageToken")
        if not next_page_token:
            break

    return videos


def reorder_playlist(youtube, playlist_id, sort_by="title"):
    """Reorder a playlist by updating positions instead of removing videos."""
    videos = get_playlist_videos(youtube, playlist_id)

    # Sorting logic
    if sort_by == "title":
        videos.sort(key=lambda x: x["title"].lower())  # Alphabetical order

    print(f"Reordering {len(videos)} videos in playlist {playlist_id}...")

    # Step 1: Update each video's position safely
    for new_position, video in enumerate(videos):
        if video["position"] != new_position:  # Only move if position changed
            request_body = {
                "id": video["playlistItemId"],
                "snippet": {
                    "playlistId": playlist_id,
                    "position": new_position,  # Move to new position
                },
            }
            youtube.playlistItems().update(part="snippet", body=request_body).execute()
            update_quota_used(50)
            print(f"Moved: {video['title']} to position {new_position}")

    print("Playlist reordering complete.")


# Define quota needed at module level
QUOTA_NEEDED = 50

def reorder_playlist_method(playlist_id, sort_by="title", email=None):
    """Method to reorder a playlist without using command line arguments"""
    from googleapiclient.errors import HttpError

    # Reset the global quota counter
    global total_quota_used
    total_quota_used = 0

    if not playlist_id:
        return {"status": "ERROR", "error": "Please specify a playlist ID."}

    if sort_by not in ["title", "upload"]:
        return {"status": "ERROR", "error": "Sort by must be either 'title' or 'upload'."}

    if not email:
        return {"status": "ERROR", "error": "Please specify an email of channel."}

    # Get YouTube access
    access = get_youtube_access(email, QUOTA_NEEDED)

    if access["status"] != "SUCCESS":
        return {"status": "ERROR", "error": access["error"]}

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        reorder_playlist(youtube, playlist_id, sort_by)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": total_quota_used})
        return {"status": "SUCCESS", "playlist_id": playlist_id, "sort_by": sort_by, "quota_used": total_quota_used}
    except HttpError as e:
        error_message = f"An HTTP error {e.resp.status} occurred:\n{e.content}"
        return {"status": "ERROR", "error": error_message}


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--playlist_id", required=True, help="YouTube Playlist ID")
    parser.add_argument(
        "--sort_by",
        choices=["title", "upload"],
        default="title",
        help="Sort playlist by 'title' (default) or 'upload' order.",
    )
    parser.add_argument("--email", required=True)

    args = parser.parse_args()


    import sys
    if not args.email:
        sys.exit("Please specify a email of channel")

    ########################################################################
    ########################################################################

    # Get YouTube access
    access = get_youtube_access(args.email, QUOTA_NEEDED)

    if (access["status"] != "SUCCESS"):
        print("FAILED:", access["error"])
        sys.exit(f"FAILED: {access['error']}")

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        reorder_playlist(youtube, args.playlist_id, args.sort_by)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": total_quota_used})

    except HttpError as e:
        print(f"An HTTP error {e.resp.status} occurred:\n{e.content}")
if __name__ == "__main__":
    #main()
    ... 

    """
    Usage:
    python reorder_playlist.py --playlist_id="PLhofekRUMpFxnGh2IF5ih_TnqCJKRkh13" --sort_by="upload"

    """
