import re
import argparse
import sys
import requests
from googleapiclient.errors import HttpError
from youtube_access import get_youtube_access

total_quota_used = 0

def update_quota_used(quota):
    global total_quota_used  # Declare the global variable
    total_quota_used += quota  # Modify the global variable
    print("Updated quota used: ", total_quota_used)

def get_playlist_videos(youtube, playlist_id):
    """Retrieve all videos in the playlist, including positions."""
    videos = []
    next_page_token = None

    while True:
        response = (
            youtube.playlistItems()
            .list(
                part="snippet",
                playlistId=playlist_id,
                maxResults=50,
                pageToken=next_page_token,
            )
            .execute()
        )
        update_quota_used(1)

        for item in response["items"]:
            videos.append(
                {
                    "playlistItemId": item["id"],
                    "videoId": item["snippet"]["resourceId"]["videoId"],
                    "title": item["snippet"]["title"],
                    "position": item["snippet"]["position"],
                }
            )

        next_page_token = response.get("nextPageToken")
        if not next_page_token:
            break

    # Sort by position (oldest to newest)
    videos.sort(key=lambda x: x["position"])
    return videos


def get_video_metadata(youtube, video_id):
    """Fetch the current video title, description, and category ID."""
    response = youtube.videos().list(part="snippet", id=video_id).execute()

    update_quota_used(1)

    items = response.get("items", [])
    if not items:
        raise ValueError(f"Video with ID {video_id} not found.")

    snippet = items[0]["snippet"]
    return snippet["title"], snippet["description"], snippet["categoryId"]


def update_video_description(youtube, video_id, new_description, title, category_id):
    """Update the video description while keeping title and category ID."""
    request_body = {
        "id": video_id,
        "snippet": {
            "title": title,
            "description": new_description,
            "categoryId": category_id,
        },
    }

    youtube.videos().update(part="snippet", body=request_body).execute()

    update_quota_used(50)

    print(f"Updated video {video_id} description successfully.")


def generate_video_links(videos, index, playlist_id):
    """Generate previous and next video links for the given video index."""
    prev_video_text = ""
    next_video_text = ""

    if index > 0:  # Previous video exists
        prev_video = videos[index - 1]
        prev_video_url = f"https://www.youtube.com/watch?v={prev_video['videoId']}&list={playlist_id}"
        prev_video_text = (
            f"\n\n⬅ **Previous Video:** [{prev_video['title']}]({prev_video_url})"
        )

    if index < len(videos) - 1:  # Next video exists
        next_video = videos[index + 1]
        next_video_url = f"https://www.youtube.com/watch?v={next_video['videoId']}&list={playlist_id}"
        next_video_text = (
            f"\n\n➡ **Next Video:** [{next_video['title']}]({next_video_url})"
        )

    # First video should have only "Next Video"
    if index == 0:
        return "", next_video_text
    # Last video should have only "Previous Video"
    elif index == len(videos) - 1:
        return prev_video_text, ""
    # Middle videos have both "Previous" and "Next"
    return prev_video_text, next_video_text


def clean_description(description):
    """Remove existing previous and next video links from the description."""
    # Remove previous video links - match more flexibly to catch variations
    description = re.sub(r'\n+\s*⬅\s*\*\*Previous Video:\*\*\s*\[[^\]]*\]\([^\)]*\)', '', description)
    # Remove next video links - match more flexibly to catch variations
    description = re.sub(r'\n+\s*➡\s*\*\*Next Video:\*\*\s*\[[^\]]*\]\([^\)]*\)', '', description)
    # Also try to match without emoji in case it was replaced or removed
    description = re.sub(r'\n+\s*\*\*Previous Video:\*\*\s*\[[^\]]*\]\([^\)]*\)', '', description)
    description = re.sub(r'\n+\s*\*\*Next Video:\*\*\s*\[[^\]]*\]\([^\)]*\)', '', description)
    # Clean up any double newlines that might have been created
    description = re.sub(r'\n\n\n+', '\n\n', description)
    return description.strip()

def description_needs_update(_, prev_video_text, next_video_text):
    """Check if there are navigation links to add."""
    # Always update if we have navigation links to add
    return bool(prev_video_text or next_video_text)


def update_playlist_descriptions(youtube, playlist_id):
    """Update all videos in a playlist with correct previous and next video links."""
    videos = get_playlist_videos(youtube, playlist_id)

    for index, video in enumerate(videos):
        video_id = video["videoId"]
        title, current_description, category_id = get_video_metadata(youtube, video_id)
        prev_video_text, next_video_text = generate_video_links(
            videos, index, playlist_id
        )

        # Check if update is needed
        if description_needs_update(
            current_description, prev_video_text, next_video_text
        ):
            # Log the original description length
            original_length = len(current_description)

            # Clean the description first to remove any existing navigation links
            cleaned_description = clean_description(current_description)
            cleaned_length = len(cleaned_description)

            # Log if anything was removed
            if original_length != cleaned_length:
                print(f"Removed {original_length - cleaned_length} characters of old navigation links")

            # Add the new navigation links
            new_description = cleaned_description + prev_video_text + next_video_text

            # Log the final description length
            print(f"Final description length: {len(new_description)} characters")
            update_video_description(
                youtube, video_id, new_description, title, category_id
            )
        else:
            print(f"Skipping update for {video['title']} (no navigation links to add).")


# Define quota needed at module level
QUOTA_NEEDED = 50

def update_playlist_links_method(playlist_id, email):
    """Method to update playlist links without using command line arguments"""
    from googleapiclient.errors import HttpError

    # Reset the global quota counter
    global total_quota_used
    total_quota_used = 0

    if not playlist_id:
        return {"status": "ERROR", "error": "Please specify a playlist ID."}

    if not email:
        return {"status": "ERROR", "error": "Please specify an email of channel."}

    # Get YouTube access
    access = get_youtube_access(email, QUOTA_NEEDED)

    if access["status"] != "SUCCESS":
        return {"status": "ERROR", "error": access["error"]}

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        update_playlist_descriptions(youtube, playlist_id)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": total_quota_used})
        return {"status": "SUCCESS", "playlist_id": playlist_id, "quota_used": total_quota_used}
    except HttpError as e:
        error_message = f"An HTTP error {e.resp.status} occurred:\n{e.content}"
        return {"status": "ERROR", "error": error_message}

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--playlist_id", required=True, help="YouTube Playlist ID")
    parser.add_argument("--email", required=True)

    args = parser.parse_args()

    import sys
    if not args.email:
        sys.exit("Please specify a email of channel")

    ########################################################################
    ########################################################################


    # Get YouTube access
    access = get_youtube_access(args.email, QUOTA_NEEDED)

    if (access["status"] != "SUCCESS"):
        print("FAILED:", access["error"])
        sys.exit(f"FAILED: {access['error']}")

    youtube = access["youtube"]
    client_id = access["client_id"]
    sanatana_service_domain = access["sanatana_service_domain"]

    try:
        update_playlist_descriptions(youtube, args.playlist_id)
        requests.post(f"{sanatana_service_domain}/update_quota",
                      json= {"client_id": client_id, "new_used_quota": total_quota_used})

    except HttpError as e:
        print(f"An HTTP error {e.resp.status} occurred:\n{e.content}")
        
if __name__ == "__main__":
    #main()
    ...
    

    """
    python update_playlist_links.py --playlist_id="PLhofekRUMpFxnGh2IF5ih_TnqCJKRkh13" --email=<EMAIL>
    """
