import os
import sys
import json
import logging
import sqlite3
import datetime
import requests
from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, Response
from flask_cors import CORS

parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from Utils.globals import determine_database_path
DATABASE = determine_database_path()

log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, 'admin_dashboard_service.log')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("admin_dashboard_service")

# Configuration
SCHEDULER_SERVICE_URL = "http://localhost:5007"
STATUS_SERVICE_URL = "http://localhost:5008"

# Flask app
app = Flask(__name__)
app.secret_key = os.urandom(24)
CORS(app)

# Templates directory
app.template_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
app.static_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static')

def get_db_connection():
    """Create a connection to the SQLite database"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def get_service_health():
    """Get health status from services"""
    health = {
        'scheduler': {'status': 'unknown'},
        'status_service': {'status': 'unknown'}
    }

    try:
        # Check scheduler service
        response = requests.get(f"{SCHEDULER_SERVICE_URL}/health", timeout=2)
        if response.status_code == 200:
            health['scheduler'] = response.json()
        else:
            health['scheduler'] = {'status': 'error', 'code': response.status_code}
    except requests.RequestException as e:
        health['scheduler'] = {'status': 'error', 'message': str(e)}

    try:
        # Check status service
        response = requests.get(f"{STATUS_SERVICE_URL}/health", timeout=2)
        if response.status_code == 200:
            health['status_service'] = response.json()
        else:
            health['status_service'] = {'status': 'error', 'code': response.status_code}
    except requests.RequestException as e:
        health['status_service'] = {'status': 'error', 'message': str(e)}

    return health

def get_job_stats():
    """Get job statistics"""
    try:
        response = requests.get(f"{STATUS_SERVICE_URL}/api/jobs/stats", timeout=5)
        if response.status_code == 200:
            return response.json()
        return {'error': f"Status service returned {response.status_code}"}
    except requests.RequestException as e:
        return {'error': f"Could not connect to status service: {str(e)}"}

def get_jobs(status=None, user_id=None, sanatana_email=None, limit=100):
    """Get jobs with optional filtering"""
    try:
        params = {}
        if status:
            params['status'] = status
        if user_id:
            # Get the email for this user_id
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT email FROM users WHERE id = ?", (user_id,))
            row = cursor.fetchone()
            conn.close()

            if row:
                params['sanatana_email'] = row['email']
        if sanatana_email:
            params['sanatana_email'] = sanatana_email
        if limit:
            params['limit'] = limit

        response = requests.get(f"{STATUS_SERVICE_URL}/api/jobs/list", params=params, timeout=5)
        if response.status_code == 200:
            return response.json()
        return []
    except requests.RequestException as e:
        logger.error(f"Error getting jobs: {str(e)}")
        return []

def get_job_details(job_id):
    """Get detailed information about a job"""
    try:
        response = requests.get(f"{STATUS_SERVICE_URL}/api/jobs/status/{job_id}", timeout=5)
        if response.status_code == 200:
            return response.json()
        return {'error': f"Status service returned {response.status_code}"}
    except requests.RequestException as e:
        return {'error': f"Could not connect to status service: {str(e)}"}

def get_users():
    """Get list of users"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id, email, name FROM users ORDER BY email")
        users = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return users
    except Exception as e:
        logger.error(f"Error getting users: {str(e)}")
        return []

@app.route('/')
def index():
    """Dashboard home page"""
    health = get_service_health()
    stats = get_job_stats()

    # Get recent jobs
    recent_jobs = get_jobs(limit=10)

    # Get users
    users = get_users()

    return render_template(
        'dashboard.html',
        health=health,
        stats=stats,
        recent_jobs=recent_jobs,
        users=users
    )

@app.route('/jobs')
def jobs_list():
    """List all jobs with filtering"""
    status = request.args.get('status')
    user_id = request.args.get('user_id')
    limit = request.args.get('limit', 100, type=int)

    jobs = get_jobs(status, user_id, limit)
    users = get_users()

    return render_template(
        'jobs.html',
        jobs=jobs,
        users=users,
        current_status=status,
        current_user_id=user_id,
        current_limit=limit
    )

@app.route('/jobs/<int:job_id>')
def job_details(job_id):
    """Show details for a specific job"""
    job = get_job_details(job_id)

    return render_template(
        'job_details.html',
        job=job,
        job_id=job_id
    )

@app.route('/jobs/<int:job_id>/retry', methods=['POST'])
def retry_job(job_id):
    """Retry a failed job"""
    try:
        response = requests.post(f"{SCHEDULER_SERVICE_URL}/jobs/retry/{job_id}", timeout=5)
        if response.status_code == 200:
            flash(f"Job {job_id} queued for retry", "success")
        else:
            flash(f"Error retrying job: {response.json().get('error', 'Unknown error')}", "error")
    except requests.RequestException as e:
        flash(f"Error connecting to scheduler service: {str(e)}", "error")

    return redirect(url_for('job_details', job_id=job_id))

@app.route('/jobs/<int:job_id>/cancel', methods=['POST'])
def cancel_job(job_id):
    """Cancel a queued job"""
    try:
        response = requests.post(f"{SCHEDULER_SERVICE_URL}/jobs/cancel/{job_id}", timeout=5)
        if response.status_code == 200:
            flash(f"Job {job_id} cancelled", "success")
        else:
            flash(f"Error cancelling job: {response.json().get('error', 'Unknown error')}", "error")
    except requests.RequestException as e:
        flash(f"Error connecting to scheduler service: {str(e)}", "error")

    return redirect(url_for('job_details', job_id=job_id))

@app.route('/users')
def users_list():
    """List all users"""
    users = get_users()

    # Get job counts per user
    for user in users:
        user['job_count'] = len(get_jobs(user_id=user['id'], limit=1000))

    return render_template(
        'users.html',
        users=users
    )

@app.route('/users/<int:user_id>')
def user_details(user_id):
    """Show details for a specific user"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
        user = dict(cursor.fetchone())
        conn.close()

        # Get user's jobs
        jobs = get_jobs(user_id=user_id, limit=100)

        return render_template(
            'user_details.html',
            user=user,
            jobs=jobs
        )
    except Exception as e:
        flash(f"Error getting user details: {str(e)}", "error")
        return redirect(url_for('users_list'))

@app.route('/services')
def services_status():
    """Show status of all services"""
    health = get_service_health()

    return render_template(
        'services.html',
        health=health
    )

@app.route('/api/health')
def api_health():
    """API endpoint for health check"""
    health = get_service_health()
    return jsonify(health)

@app.route('/api/stats')
def api_stats():
    """API endpoint for job statistics"""
    stats = get_job_stats()
    return jsonify(stats)

if __name__ == "__main__":
    import argparse, sys
    # Service name
    service_name = "ADMIN_DASHBOARD"

    # Parse command-line arguments for port
    arg_parser = argparse.ArgumentParser(description=f"Start the {service_name} microservice.")
    arg_parser.add_argument('--port', type=int, help="Port to run the service on")
    args = arg_parser.parse_args(sys.argv[1:])

    # Determine the port: Use CLI argument > Environment variable > Default
    PORT = args.port or int(os.getenv(service_name, 5009))

    # Get the parent directory of the current script
    parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

    # Add it to sys.path
    sys.path.append(parent_dir)

    from Utils.globals import save_port_in_global_ports

    # Write back to global ports file
    save_port_in_global_ports(service_name, PORT)

    print(f"Starting service {service_name} on port {PORT}...")
    # Start Flask app
    app.run(host='0.0.0.0', port=PORT, threaded=True)
