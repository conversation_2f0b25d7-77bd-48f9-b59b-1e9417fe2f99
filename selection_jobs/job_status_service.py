import os
import sys
import json
import logging
import sqlite3
import requests
from flask import Flask, request, jsonify, Response
from flask_cors import CORS
import datetime
import time

parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from Utils.globals import determine_database_path
DATABASE = determine_database_path()

log_dir = os.path.join(os.path.dirname(__file__), 'logs')  # or 'selection_jobs'
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, 'status_service.log')
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("job_status")

# Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Add cache headers to all responses
@app.after_request
def add_cache_headers(response):
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response
    # Get the path from the request
    # path = request.path

    # # Set default cache control header
    # response.headers['Cache-Control'] = 'no-cache'

    # # Add Vary header to properly handle cached responses
    # response.headers['Vary'] = 'Accept-Encoding, User-Agent'

    # # Add ETag header based on response content and timestamp
    # response.headers['ETag'] = f'"{hash(response.get_data())}-{int(time.time())}"'

    # # Cache static assets aggressively
    # if path.endswith(('.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.woff2', '.ttf', '.eot')):
    #     # Cache for 1 week
    #     response.headers['Cache-Control'] = 'public, max-age=604800, stale-while-revalidate=86400'

    # # Cache API responses that are read-only (GET requests) and not job status
    # elif request.method == 'GET' and not path.startswith('/api/jobs/status/'):
    #     # Cache for 1 minute, but allow stale responses while revalidating
    #     response.headers['Cache-Control'] = 'public, max-age=60, stale-while-revalidate=300'

    # return response

# Configuration

SCHEDULER_SERVICE_URL = f"http://localhost:5007"

def get_db_connection():
    """Create a connection to the SQLite database"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def get_scheduler_health():
    """Get health status from the scheduler service"""
    try:
        response = requests.get(f"{SCHEDULER_SERVICE_URL}/health", timeout=5)
        if response.status_code == 200:
            return response.json()
        return {"status": "error", "message": f"Scheduler returned status code {response.status_code}"}
    except requests.RequestException as e:
        return {"status": "error", "message": f"Could not connect to scheduler: {str(e)}"}

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    scheduler_health = get_scheduler_health()

    return jsonify({
        'status': 'healthy',
        'scheduler': scheduler_health
    })

@app.route('/api/jobs/status/<int:selection_id>', methods=['GET'])
def get_job_status(selection_id):
    """Get the status of a job"""
    try:
        # First try to get status from scheduler service
        try:
            scheduler_response = requests.get(
                f"{SCHEDULER_SERVICE_URL}/jobs/status/{selection_id}",
                timeout=5
            )
            if scheduler_response.status_code == 200:
                return jsonify(scheduler_response.json())
        except requests.RequestException:
            # If scheduler is unavailable, fall back to database
            pass

        # Get status from database
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT us.id, us.selection, us.status, us.sanatana_email, us.created_at
            FROM upload_selections us
            WHERE us.id = ?
        """, (selection_id,))

        row = cursor.fetchone()

        if not row:
            return jsonify({'error': f'Selection ID {selection_id} not found'}), 404

        # Parse status
        status_data = json.loads(row['status']) if row['status'] else {'current': 'unknown'}
        selection_data = json.loads(row['selection'])
        history = status_data.get('history', [])
        print("selection_id: histrory",len(history))
            
        # Enhance with additional information
        result = {
            'id': row['id'],
            'sanatana_email': row['sanatana_email'],
            'created_at': row['created_at'],
            'status': status_data,
            'selection': {
                'source': selection_data.get('source'),
                'title': selection_data.get('title'),
                'videoLink': selection_data.get('videoLink'),
                'scheduleDate': selection_data.get('scheduleDate'),
                'uploadWithinHour': selection_data.get('uploadWithinHour', False),
                'destinations': selection_data.get('destinations', [])
            },
            'history': history,
            'last_updated': status_data.get('last_updated'),
            'retry_count': status_data.get('retry_count', 0),
            'is_active': status_data.get('current') in ['in_progress', 'retrying'],
            'in_queue': status_data.get('current') == 'queued',
            'current': status_data.get('current', 'unknown'),
            
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error getting job status for {selection_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/api/jobs/list', methods=['GET'])
def list_jobs():
    """List all jobs with their statuses"""
    try:
        sanatana_email = request.args.get('sanatana_email')
        status = request.args.get('status')
        limit = request.args.get('limit', 100, type=int)

        # Try to get from scheduler first
        params = {}
        if sanatana_email:
            params['sanatana_email'] = sanatana_email
        if status:
            params['status'] = status
        if limit:
            params['limit'] = limit

        try:
            scheduler_response = requests.get(
                f"{SCHEDULER_SERVICE_URL}/jobs/list",
                params=params,
                timeout=5
            )
            if scheduler_response.status_code == 200:
                return jsonify(scheduler_response.json())
        except requests.RequestException:
            # If scheduler is unavailable, fall back to database
            pass

        # Get from database
        conn = get_db_connection()
        cursor = conn.cursor()

        # Build query
        query = """
            SELECT us.id, us.selection, us.status, us.sanatana_email, us.created_at
            FROM upload_selections us
        """

        params = []
        where_clauses = []

        if sanatana_email:
            where_clauses.append("us.sanatana_email = ?")
            params.append(sanatana_email)

        if status:
            where_clauses.append("us.status LIKE ?")
            params.append(f'%"current":"{status}"%')

        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)

        query += " ORDER BY us.created_at DESC LIMIT ?"
        params.append(limit)

        # Execute query
        cursor.execute(query, params)
        rows = cursor.fetchall()

        # Format results
        jobs = []
        for row in rows:
            status_data = json.loads(row['status']) if row['status'] else {'current': 'unknown'}
            selection_data = json.loads(row['selection'])

            job_info = {
                'id': row['id'],
                'sanatana_email': row['sanatana_email'],
                'created_at': row['created_at'],
                'status': status_data.get('current', 'unknown'),
                'last_updated': status_data.get('last_updated'),
                'source': selection_data.get('source'),
                'title': selection_data.get('title') or (
                    selection_data.get('useYouTubeTitle') and 'YouTube Title' or 'No Title'
                ),
                'schedule_date': selection_data.get('scheduleDate'),
                'destinations': [d.get('name') for d in selection_data.get('destinations', [])]
            }

            jobs.append(job_info)

        return jsonify(jobs)

    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/api/jobs/queue/<int:selection_id>', methods=['POST'])
def queue_job(selection_id):
    """Queue a job for immediate processing"""
    try:
        # Forward request to scheduler
        scheduler_response = requests.post(
            f"{SCHEDULER_SERVICE_URL}/jobs/queue",
            json={'selection_id': selection_id},
            timeout=5
        )

        return Response(
            scheduler_response.content,
            status=scheduler_response.status_code,
            content_type=scheduler_response.headers['Content-Type']
        )

    except requests.RequestException as e:
        logger.error(f"Error queuing job {selection_id}: {str(e)}")
        return jsonify({'error': f"Could not connect to scheduler: {str(e)}"}), 500

@app.route('/api/jobs/user/<sanatana_email>/pending', methods=['GET'])
def get_user_pending_jobs(sanatana_email):
    """Get all pending jobs for a user"""
    #print(f"Function get_user_pending_jobs called with sanatana_email: {sanatana_email}")
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
       # print("Database connection established.")

        # Get pending jobs
        query = """
        SELECT us.id, us.selection, us.status, us.created_at
        FROM upload_selections us
        WHERE us.sanatana_email = ?
        AND (
            us.status IS NULL
            OR json_extract(us.status, '$.current') IN
            ('created','queued', 'downloading','uploading',
            'completed', 'failed', 'upload_failed', 'cancelled', 'retrying',
            'scheduled', 'Youtube_Error', 'Youtube_uploadLimitExceeded', 'staged'
            )
        )
        ORDER BY us.created_at DESC;
        """
        #print(f"Executing query: {query} with sanatana_email: {sanatana_email}"
        # )
        cursor.execute(query, (sanatana_email,))

        rows = cursor.fetchall()
        #print(f"Number of pending jobs retrieved: {len(rows)}")

        # Format results
        jobs = []
        for row in rows:
           # print(f"Processing row: {row}")
            status_data = json.loads(row['status']) if row['status'] else {'current': 'unknown'}
            selection_data = json.loads(row['selection'])
            history = status_data.get('history', [])
            print("pending: histrory",len(history))
            
            job_info = {
                'id': row['id'],
                'created_at': row['created_at'],
                'status': status_data.get('current', 'unknown'),
                'last_updated': status_data.get('last_updated'),
                'source': selection_data.get('source'),
                'title': selection_data.get('title') or (
                    selection_data.get('useYouTubeTitle') and 'YouTube Title' or 'No Title'
                ),
                'schedule_date': selection_data.get('scheduleDate'),
                'destinations': [d.get('name') for d in selection_data.get('destinations', [])],
                'history':history,
                'last_updated': status_data.get('last_updated'),
                'retry_count': status_data.get('retry_count', 0),
                'is_active': status_data.get('current') in ['in_progress', 'retrying'],
                'in_queue': status_data.get('current') == 'queued',
                'current': status_data.get('current', 'unknown'),
            }
           # print(f"Constructed job_info: {job_info}")

            jobs.append(job_info)

        #print("All jobs processed successfully.")
        return jsonify(jobs)

    except Exception as e:
        print(f"An error occurred: {e}")
        return jsonify({'error': 'An internal error occurred'}), 500

    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/api/jobs/stats', methods=['GET'])
def get_job_stats():
    """Get job statistics"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get counts by status
        cursor.execute("""
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status IS NULL THEN 1 ELSE 0 END) as unknown,
                SUM(CASE WHEN status LIKE '%"current":"pending"%' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status LIKE '%"current":"queued"%' THEN 1 ELSE 0 END) as queued,
                SUM(CASE WHEN status LIKE '%"current":"processing"%' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status LIKE '%"current":"completed"%' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status LIKE '%"current":"failed"%' THEN 1 ELSE 0 END) as failed
            FROM upload_selections
        """)

        row = cursor.fetchone()

        # Get counts by date
        cursor.execute("""
            SELECT
                DATE(created_at) as date,
                COUNT(*) as count
            FROM upload_selections
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            LIMIT 30
        """)

        date_rows = cursor.fetchall()
        dates = [{'date': row['date'], 'count': row['count']} for row in date_rows]

        # Get scheduler health
        scheduler_health = get_scheduler_health()

        stats = {
            'counts': {
                'total': row['total'],
                'unknown': row['unknown'],
                'pending': row['pending'],
                'queued': row['queued'],
                'processing': row['processing'],
                'completed': row['completed'],
                'failed': row['failed']
            },
            'by_date': dates,
            'scheduler': scheduler_health
        }

        return jsonify(stats)

    except Exception as e:
        logger.error(f"Error getting job stats: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    import argparse, sys
     # Service name
    service_name = "JOB_STATUS"

    # Parse command-line arguments for port
    arg_parser = argparse.ArgumentParser(description=f"Start the {service_name} microservice.")
    arg_parser.add_argument('--port', type=int, help="Port to run the service on")
    args = arg_parser.parse_args(sys.argv[1:])

    # Determine the port: Use CLI argument > Environment variable > Default
    PORT = args.port or int(os.getenv(service_name, 5008))

    # Get the parent directory of the current script
    parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

    # Add it to sys.path
    sys.path.append(parent_dir)

    from Utils.globals import save_port_in_global_ports

    # Write back to global ports file
    save_port_in_global_ports(service_name, PORT)

    print(f"Starting service {service_name} on port {PORT}...")
    app.run(host="0.0.0.0", port=PORT, threaded=True)
