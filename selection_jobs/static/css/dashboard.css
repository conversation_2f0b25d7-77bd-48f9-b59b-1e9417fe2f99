/* Dashboard Styles */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: 600;
}

.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 12px 16px;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
}

.card-dashboard {
    transition: transform 0.2s, box-shadow 0.2s;
    border-radius: 8px;
    overflow: hidden;
}

.card-dashboard:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.status-badge {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    text-transform: capitalize;
}

.service-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-healthy {
    background-color: #28a745;
}

.status-error {
    background-color: #dc3545;
}

.status-unknown {
    background-color: #6c757d;
}

/* History Timeline */
.history-timeline {
    position: relative;
    padding-left: 20px;
}

.history-item {
    border-left: 3px solid #dee2e6;
    padding-left: 15px;
    position: relative;
    margin-bottom: 20px;
}

.history-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #6c757d;
}

.history-item.status-completed::before {
    background-color: #28a745;
}

.history-item.status-failed::before {
    background-color: #dc3545;
}

.history-item.status-processing::before,
.history-item.status-downloading::before,
.history-item.status-upload_started::before {
    background-color: #17a2b8;
}

.history-item.status-pending::before,
.history-item.status-queued::before {
    background-color: #ffc107;
}

/* Code and JSON display */
pre {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.9rem;
    border: 1px solid #dee2e6;
}

/* Tables */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Buttons */
.btn-group .btn {
    margin-right: 5px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Progress bars */
.progress {
    height: 20px;
    margin-bottom: 10px;
    border-radius: 4px;
}

.progress-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-dashboard {
        margin-bottom: 15px;
    }
    
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .table-responsive {
        border: 0;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #212529;
        color: #f8f9fa;
    }
    
    .card {
        background-color: #2c3034;
        border-color: #373b3e;
    }
    
    .card-header {
        background-color: #343a40;
        border-color: #373b3e;
    }
    
    .table {
        color: #e9ecef;
    }
    
    .table th {
        background-color: #343a40;
    }
    
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(255, 255, 255, 0.05);
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.075);
    }
    
    pre {
        background-color: #343a40;
        border-color: #495057;
        color: #e9ecef;
    }
    
    .alert-info {
        background-color: #0d3b66;
        border-color: #0c3356;
        color: #e9ecef;
    }
}
