# Upload Selection Job Processing System

This system manages the processing of upload selection jobs, handling scheduling, queuing, and execution of uploads to various platforms.

## Components

1. **Job Scheduler Service** (Port 5007)
   - Manages job scheduling and queuing
   - Processes immediate jobs
   - Schedules future jobs
   - Provides job status updates

2. **Job Status Service** (Port 5008)
   - Provides API endpoints for clients to check job status
   - Lists jobs for users
   - Provides job statistics

3. **Job Worker Module**
   - Handles the actual processing of jobs
   - Downloads content from sources
   - Uploads to destinations
   - Updates job status

4. **Database Interface**
   - Manages interactions with the SQLite database
   - Provides functions to update job status

## Installation

1. Make sure you have the required dependencies:
   ```
   pip install flask apscheduler requests flask-cors python-dateutil pytz
   ```

2. Ensure the parent directory is in your Python path:
   ```python
   import sys
   import os
   sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
   ```

## Running the Services

You can run both services using the provided script:

```
python run_services.py
```

Or run them individually:

```
python job_scheduler_service.py
python job_status_service.py
```

## Job Processing Flow

1. **Job Creation**
   - Jobs are created in the `upload_selections` table
   - Each job has a `selection` JSON field with upload details
   - Jobs can be scheduled for future execution or processed immediately

2. **Job Scheduling**
   - The scheduler checks for new jobs periodically
   - Jobs with `uploadWithinHour=true` are queued for immediate processing
   - Jobs with `scheduleDate` are scheduled for future execution
   - Jobs within 15 minutes of their schedule time are processed immediately

3. **Job Processing**
   - Jobs are processed in order of priority
   - The system limits concurrent jobs to prevent overloading
   - Each job goes through several stages:
     - Downloading content
     - Uploading to destinations
     - Updating playlists (if needed)

4. **Status Updates**
   - Job status is updated at each stage
   - Status history is maintained for tracking
   - Clients can query job status through the API

## API Endpoints

### Job Scheduler Service (Port 5007)

- `GET /health` - Check service health
- `POST /jobs/queue` - Queue a job for immediate processing
- `GET /jobs/status/<selection_id>` - Get job status
- `GET /jobs/list` - List all jobs

### Job Status Service (Port 5008)

- `GET /health` - Check service health
- `GET /api/jobs/status/<selection_id>` - Get job status
- `GET /api/jobs/list` - List all jobs
- `POST /api/jobs/queue/<selection_id>` - Queue a job
- `GET /api/jobs/user/<sanatana_email>/pending` - Get pending jobs for a user
- `GET /api/jobs/stats` - Get job statistics

## Job Status Lifecycle

1. `pending` - Job is scheduled for future execution
2. `queued` - Job is in the queue for processing
3. `processing` - Job is being processed
4. `downloading` - Content is being downloaded
5. `upload_started` - Upload has started
6. `upload_complete` - Upload has completed
7. `playlist_updated` - Playlists have been updated
8. `completed` - Job has completed successfully
9. `failed` - Job has failed

## React/Expo Integration

The system includes React components for displaying job status in the mobile app:

1. `JobStatusCard.js` - A reusable component for displaying job status
2. `JobStatusScreen.js` - A screen for displaying all jobs for a user

## Configuration

The system uses the following configuration:

- `DB_PATH` - Path to the SQLite database
- `MAX_CONCURRENT_JOBS` - Maximum number of jobs to process concurrently
- `CHECK_INTERVAL` - Seconds between checking for new jobs
- `SCHEDULE_WINDOW` - Window in seconds to pick up scheduled jobs

## Error Handling

- Jobs that fail are marked with a `failed` status
- Error details are stored in the job status history
- The system will retry failed jobs if they are queued again

## Monitoring

- The system logs all activities to log files
- Job statistics are available through the API
- Service health can be checked through the health endpoints
