import os
import sys
import json
import time
import queue
import logging
import threading
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import psutil

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import job worker
from selection_jobs.job_worker import process_selection_job

log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, 'worker_pool.log')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("worker_pool")

class WorkerPool:
    """Manages a pool of workers for processing jobs with dynamic scaling"""
    
    def __init__(self, min_workers=2, max_workers=8, cpu_threshold=70, memory_threshold=80):
        """Initialize the worker pool"""
        self.min_workers = min_workers
        self.max_workers = max_workers
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        
        # Current number of workers
        self.current_workers = min_workers
        
        # Job queue
        self.job_queue = queue.PriorityQueue()
        
        # Active jobs
        self.active_jobs = {}
        self.active_jobs_lock = threading.Lock()
        
        # Worker pool
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # Monitoring thread
        self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitor_thread.start()
        
        # Worker thread
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        
        # Stats
        self.stats = {
            'jobs_processed': 0,
            'jobs_succeeded': 0,
            'jobs_failed': 0,
            'avg_processing_time': 0,
            'total_processing_time': 0
        }
        self.stats_lock = threading.Lock()
        
        logger.info(f"Worker pool initialized with {min_workers}-{max_workers} workers")
    
    def _monitor_resources(self):
        """Monitor system resources and adjust worker count"""
        while True:
            try:
                # Get CPU and memory usage
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                # Adjust worker count based on resource usage
                if cpu_percent > self.cpu_threshold or memory_percent > self.memory_threshold:
                    # System is under heavy load, reduce workers
                    if self.current_workers > self.min_workers:
                        self.current_workers = max(self.current_workers - 1, self.min_workers)
                        logger.info(f"Reducing workers to {self.current_workers} due to high resource usage (CPU: {cpu_percent}%, Memory: {memory_percent}%)")
                else:
                    # System has capacity, increase workers if needed
                    if self.current_workers < self.max_workers and not self.job_queue.empty():
                        self.current_workers = min(self.current_workers + 1, self.max_workers)
                        logger.info(f"Increasing workers to {self.current_workers} (CPU: {cpu_percent}%, Memory: {memory_percent}%)")
                
                # Log current status
                with self.active_jobs_lock:
                    active_count = len(self.active_jobs)
                
                queue_size = self.job_queue.qsize()
                
                logger.info(f"Status: Workers={self.current_workers}, Active={active_count}, Queued={queue_size}, CPU={cpu_percent}%, Memory={memory_percent}%")
                
                # Sleep for a bit
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"Error in resource monitor: {str(e)}")
                time.sleep(60)
    
    def _worker_loop(self):
        """Main worker loop to process jobs from the queue"""
        while True:
            try:
                # Check if we can process more jobs
                with self.active_jobs_lock:
                    active_count = len(self.active_jobs)
                
                if active_count >= self.current_workers:
                    # Wait for a job to complete
                    time.sleep(1)
                    continue
                
                # Get a job from the queue
                try:
                    priority, selection_id, selection_data = self.job_queue.get(block=True, timeout=1)
                except queue.Empty:
                    # No jobs in queue
                    time.sleep(1)
                    continue
                
                # Check if job is already being processed
                with self.active_jobs_lock:
                    if selection_id in self.active_jobs:
                        logger.warning(f"Job {selection_id} is already being processed, skipping")
                        self.job_queue.task_done()
                        continue
                    
                    # Mark job as active
                    self.active_jobs[selection_id] = {
                        'start_time': time.time(),
                        'selection_data': selection_data
                    }
                
                # Submit job to executor
                self.executor.submit(self._process_job, selection_id, selection_data)
                
            except Exception as e:
                logger.error(f"Error in worker loop: {str(e)}")
                time.sleep(5)
    
    def _process_job(self, selection_id, selection_data):
        """Process a job and update stats"""
        start_time = time.time()
        
        try:
            # Process the job
            result = process_selection_job(selection_id, selection_data, self._update_job_status)
            
            # Update stats
            with self.stats_lock:
                self.stats['jobs_processed'] += 1
                
                if result:
                    self.stats['jobs_succeeded'] += 1
                else:
                    self.stats['jobs_failed'] += 1
                
                processing_time = time.time() - start_time
                self.stats['total_processing_time'] += processing_time
                self.stats['avg_processing_time'] = self.stats['total_processing_time'] / self.stats['jobs_processed']
            
            logger.info(f"Job {selection_id} completed in {processing_time:.2f} seconds with result: {result}")
            
        except Exception as e:
            logger.error(f"Error processing job {selection_id}: {str(e)}")
            
            # Update stats
            with self.stats_lock:
                self.stats['jobs_processed'] += 1
                self.stats['jobs_failed'] += 1
                
                processing_time = time.time() - start_time
                self.stats['total_processing_time'] += processing_time
                self.stats['avg_processing_time'] = self.stats['total_processing_time'] / self.stats['jobs_processed']
            
            # Update job status
            self._update_job_status(selection_id, "failed", {"message": f"Job processing error: {str(e)}"})
            
        finally:
            # Remove job from active jobs
            with self.active_jobs_lock:
                if selection_id in self.active_jobs:
                    del self.active_jobs[selection_id]
            
            # Mark job as done in queue
            self.job_queue.task_done()
    
    def _update_job_status(self, selection_id, status, details=None):
        """Update job status callback for the worker"""
        # This will be implemented by the job scheduler service
        pass
    
    def queue_job(self, selection_id, selection_data, priority=100):
        """Queue a job for processing"""
        self.job_queue.put((priority, selection_id, selection_data))
        logger.info(f"Job {selection_id} queued with priority {priority}")
        return True
    
    def cancel_job(self, selection_id):
        """Cancel a queued job"""
        # Check if job is in queue
        found = False
        temp_queue = queue.PriorityQueue()
        
        # Move all items except the one to cancel
        while not self.job_queue.empty():
            item = self.job_queue.get()
            if item[1] == selection_id:
                found = True
            else:
                temp_queue.put(item)
        
        # Restore queue
        while not temp_queue.empty():
            self.job_queue.put(temp_queue.get())
        
        # Check if job is active
        with self.active_jobs_lock:
            if selection_id in self.active_jobs:
                logger.warning(f"Job {selection_id} is already being processed, cannot cancel")
                return False
        
        if found:
            logger.info(f"Job {selection_id} cancelled")
            return True
        else:
            logger.warning(f"Job {selection_id} not found in queue")
            return False
    
    def get_stats(self):
        """Get worker pool statistics"""
        with self.stats_lock:
            stats_copy = self.stats.copy()
        
        with self.active_jobs_lock:
            active_jobs = list(self.active_jobs.keys())
            active_count = len(active_jobs)
        
        queue_size = self.job_queue.qsize()
        
        return {
            'workers': {
                'current': self.current_workers,
                'min': self.min_workers,
                'max': self.max_workers
            },
            'jobs': {
                'active': active_count,
                'queued': queue_size,
                'active_ids': active_jobs,
                'processed': stats_copy['jobs_processed'],
                'succeeded': stats_copy['jobs_succeeded'],
                'failed': stats_copy['jobs_failed'],
                'avg_time': round(stats_copy['avg_processing_time'], 2)
            },
            'resources': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent
            }
        }
    
    def shutdown(self):
        """Shutdown the worker pool"""
        logger.info("Shutting down worker pool")
        self.executor.shutdown(wait=False)
        logger.info("Worker pool shutdown complete")

# Singleton instance
_instance = None

def get_worker_pool(min_workers=2, max_workers=8, cpu_threshold=70, memory_threshold=80):
    """Get the singleton instance of the worker pool"""
    global _instance
    if _instance is None:
        _instance = WorkerPool(min_workers, max_workers, cpu_threshold, memory_threshold)
    return _instance

if __name__ == "__main__":
    # Test the worker pool
    worker_pool = get_worker_pool()
    print("Worker pool stats:", worker_pool.get_stats())
    worker_pool.shutdown()
