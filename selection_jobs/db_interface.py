import os
import sys
import json
import sqlite3
import logging
from datetime import datetime

parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from Utils.globals import determine_database_path
DATABASE = determine_database_path()

log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, 'db_interface.log')
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("db_interface")

# Configuration

def get_db_connection():
    """Create a connection to the SQLite database"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def get_selection_by_id(selection_id):
    """Get a selection by ID"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, selection, status, sanatana_email, created_at
            FROM upload_selections
            WHERE id = ?
        """, (selection_id,))

        row = cursor.fetchone()

        if not row:
            return None

        # Parse selection data
        selection_data = json.loads(row['selection'])

        # Parse status data
        status_data = json.loads(row['status']) if row['status'] else None

        return {
            'id': row['id'],
            'sanatana_email': row['sanatana_email'],
            'created_at': row['created_at'],
            'selection_data': selection_data,
            'status_data': status_data
        }

    except Exception as e:
        logger.error(f"Error getting selection {selection_id}: {str(e)}")
        return None

    finally:
        if 'conn' in locals():
            conn.close()

def get_pending_selections(limit=10):
    """Get pending selections that need to be processed"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get selections that are not yet processed
        cursor.execute("""
            SELECT id, selection, status, sanatana_email, created_at
            FROM upload_selections
            WHERE json_extract(status, '$.current') IN
            ('created', 'failed', 'upload_failed', 'Youtube_uploadLimitExceeded')
            ORDER BY created_at ASC
            LIMIT ?
        """, (limit,))

        rows = cursor.fetchall()

        results = []
        for row in rows:
            # Parse selection data
            selection_data = json.loads(row['selection'])

            # Parse status data
            status_data = json.loads(row['status']) if row['status'] else None

            results.append({
                'id': row['id'],
                'sanatana_email': row['sanatana_email'],
                'created_at': row['created_at'],
                'selection_data': selection_data,
                'status_data': status_data
            })

        return results

    except Exception as e:
        logger.error(f"Error getting pending selections: {str(e)}")
        return []

    finally:
        if 'conn' in locals():
            conn.close()

def update_selection_status(selection_id, status, details=None):
    """Update the status of a selection"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get current status
        cursor.execute("SELECT status FROM upload_selections WHERE id = ?", (selection_id,))
        row = cursor.fetchone()

        if not row:
            logger.error(f"Selection {selection_id} not found")
            return False

        # Parse existing status or create new status object
        try:
            current_status = json.loads(row['status']) if row['status'] else {}
        except json.JSONDecodeError:
            current_status = {}

        # Update status with new information
        timestamp = datetime.now().isoformat()
        current_status['current'] = status
        current_status['last_updated'] = timestamp

        if 'history' not in current_status:
            current_status['history'] = []

        history_entry = {
            'status': status,
            'timestamp': timestamp
        }

        if details:
            history_entry['details'] = details

        current_status['history'].append(history_entry)

        # Save updated status back to database
        cursor.execute(
            "UPDATE upload_selections SET status = ? WHERE id = ?",
            (json.dumps(current_status), selection_id)
        )
        conn.commit()

        logger.info(f"Updated selection {selection_id} status to {status}")
        return True

    except Exception as e:
        logger.error(f"Error updating selection status: {str(e)}")
        return False

    finally:
        if 'conn' in locals():
            conn.close()

def get_user_selections(sanatana_email, limit=50):
    """Get selections for a specific user"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, selection, status, created_at
            FROM upload_selections
            WHERE sanatana_email = ?
            ORDER BY created_at DESC
            LIMIT ?
        """, (sanatana_email, limit))

        rows = cursor.fetchall()

        results = []
        for row in rows:
            # Parse selection data
            selection_data = json.loads(row['selection'])

            # Parse status data
            status_data = json.loads(row['status']) if row['status'] else None

            results.append({
                'id': row['id'],
                'created_at': row['created_at'],
                'selection_data': selection_data,
                'status_data': status_data
            })

        return results

    except Exception as e:
        logger.error(f"Error getting user selections: {str(e)}")
        return []

    finally:
        if 'conn' in locals():
            conn.close()

def add_selection_result(selection_id, result_data):
    """Add result data to a selection"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if selection exists
        cursor.execute("SELECT id FROM upload_selections WHERE id = ?", (selection_id,))
        if not cursor.fetchone():
            logger.error(f"Selection {selection_id} not found")
            return False

        # Add result data
        cursor.execute(
            "UPDATE upload_selections SET result = ? WHERE id = ?",
            (json.dumps(result_data), selection_id)
        )
        conn.commit()

        logger.info(f"Added result data to selection {selection_id}")
        return True

    except Exception as e:
        logger.error(f"Error adding selection result: {str(e)}")
        return False

    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    # Test the functions
    print("Testing DB interface...")

    # Get pending selections
    pending = get_pending_selections(5)
    print(f"Found {len(pending)} pending selections")

    # Update a selection status (if any pending selections found)
    if pending:
        selection_id = pending[0]['id']
        print(f"Updating status for selection {selection_id}")
        update_selection_status(selection_id, "test_status", {"test": "details"})

        # Get the selection again to verify the update
        selection = get_selection_by_id(selection_id)
        print(f"Updated selection status: {json.dumps(selection['status_data'], indent=2)}")

    print("DB interface test complete")
