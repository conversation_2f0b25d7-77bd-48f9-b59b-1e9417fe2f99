import os
import sys
import json
import time
import logging
import sqlite3
import datetime
import threading
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger

parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from Utils.globals import determine_database_path
DATABASE = determine_database_path()

# Global variables for the standalone function
_queue_job_callback = None

# Standalone function to check for failed jobs
def check_for_failed_jobs_standalone():
    """Standalone function to check for failed jobs without requiring a JobRetryManager instance"""
    logger.info("Checking for failed jobs...")

    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Find jobs that have failed status
        cursor.execute("""
            SELECT id, selection, status, sanatana_email, created_at
            FROM upload_selections
            WHERE status LIKE '%"current":"failed"%'
            ORDER BY created_at DESC
        """)

        for row in cursor.fetchall():
            selection_id = row['id']
            status_data = json.loads(row['status']) if row['status'] else {}

            # Check if this job has a retry history
            retry_count = 0
            if 'retry_count' in status_data:
                retry_count = status_data['retry_count']

            # If we haven't exceeded max retries, schedule a retry
            if retry_count < MAX_RETRY_ATTEMPTS and _queue_job_callback:
                # Calculate delay based on retry count
                delay_minutes = RETRY_DELAY_MINUTES[min(retry_count, len(RETRY_DELAY_MINUTES) - 1)]

                # Update status to indicate a retry is scheduled
                retry_count += 1
                status_data['retry_count'] = retry_count
                status_data['current'] = 'retry_scheduled'
                status_data['last_updated'] = datetime.datetime.now().isoformat()

                if 'history' not in status_data:
                    status_data['history'] = []

                status_data['history'].append({
                    'status': 'retry_scheduled',
                    'timestamp': datetime.datetime.now().isoformat(),
                    'details': {
                        'retry_count': retry_count,
                        'delay_minutes': delay_minutes,
                        'message': f"Retry {retry_count}/{MAX_RETRY_ATTEMPTS} scheduled in {delay_minutes} minutes"
                    }
                })

                # Update status in database
                cursor.execute(
                    "UPDATE upload_selections SET status = ? WHERE id = ?",
                    (json.dumps(status_data), selection_id)
                )
                conn.commit()

                logger.info(f"Scheduled retry {retry_count}/{MAX_RETRY_ATTEMPTS} for job {selection_id} in {delay_minutes} minutes")

                # Queue the job with the global callback
                if _queue_job_callback:
                    # Schedule the retry after the delay - capture current values in closure
                    def create_delayed_retry(job_id, retry_num, max_retries):
                        def delayed_retry():
                            try:
                                # Update status to retrying
                                conn = sqlite3.connect(DATABASE)
                                cursor = conn.cursor()

                                # Get the latest status
                                cursor.execute("SELECT status FROM upload_selections WHERE id = ?", (job_id,))
                                row = cursor.fetchone()
                                if row and row[0]:
                                    status_data = json.loads(row[0])
                                    status_data['current'] = 'retrying'
                                    status_data['last_updated'] = datetime.datetime.now().isoformat()

                                    if 'history' not in status_data:
                                        status_data['history'] = []

                                    status_data['history'].append({
                                        'status': 'retrying',
                                        'timestamp': datetime.datetime.now().isoformat(),
                                        'details': {
                                            'message': f"Executing retry {retry_num}/{max_retries}"
                                        }
                                    })

                                    # Update status in database
                                    cursor.execute(
                                        "UPDATE upload_selections SET status = ? WHERE id = ?",
                                        (json.dumps(status_data), job_id)
                                    )
                                    conn.commit()

                                # Queue the job for processing
                                _queue_job_callback(job_id)
                                logger.info(f"Executing retry {retry_num}/{max_retries} for job {job_id}")
                            except Exception as e:
                                logger.error(f"Error during retry for job {job_id}: {str(e)}")
                        return delayed_retry

                    # Schedule the delayed retry
                    timer = threading.Timer(
                        delay_minutes * 60,
                        create_delayed_retry(selection_id, retry_count, MAX_RETRY_ATTEMPTS)
                    )
                    timer.daemon = True
                    timer.start()
            else:
                if retry_count >= MAX_RETRY_ATTEMPTS:
                    logger.info(f"Job {selection_id} has reached maximum retry attempts ({MAX_RETRY_ATTEMPTS})")
                else:
                    logger.warning(f"No queue_job_callback available for retrying job {selection_id}")

        conn.close()
    except Exception as e:
        logger.error(f"Error checking for failed jobs: {str(e)}")

# Function to set the global callback
def set_queue_job_callback(callback):
    global _queue_job_callback
    _queue_job_callback = callback

log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, 'retry_manager.log')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("retry_manager")

# Configuration
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY_MINUTES = [5, 15, 60]  # Increasing delays between retries
RETRY_CHECK_INTERVAL = 60  # Check for failed jobs every minute

class JobRetryManager:
    """Manages automatic retries for failed jobs"""

    def __init__(self, scheduler=None, queue_job_callback=None):
        """Initialize the retry manager"""
        self.scheduler = scheduler or BackgroundScheduler()
        self.queue_job_callback = queue_job_callback

        # Set the global callback
        set_queue_job_callback(queue_job_callback)

        # Start the scheduler if it's not already running
        if not self.scheduler.running:
            self.scheduler.start()

        # Schedule the retry check job using the standalone function
        self.scheduler.add_job(
            check_for_failed_jobs_standalone,  # Use the standalone function without args
            IntervalTrigger(seconds=RETRY_CHECK_INTERVAL),
            id='retry_check',
            replace_existing=True
        )

        logger.info("Job Retry Manager initialized")

    def get_db_connection(self):
        """Create a connection to the SQLite database"""
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        return conn

    def check_for_failed_jobs(self):
        """Check for failed jobs that need to be retried"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # Find failed jobs that haven't reached max retries
            cursor.execute("""
                SELECT id, selection, status
                FROM upload_selections
                WHERE status LIKE '%"current":"failed"%'
                AND status NOT LIKE '%"retry_count":3%'
                AND status NOT LIKE '%"no_retry":true%'
            """)

            failed_jobs = cursor.fetchall()
            logger.info(f"Found {len(failed_jobs)} failed jobs to check for retry")

            for job in failed_jobs:
                selection_id = job['id']
                status_data = json.loads(job['status'])

                # Get current retry count
                retry_count = status_data.get('retry_count', 0)

                # Check if we should retry
                if retry_count < MAX_RETRY_ATTEMPTS:
                    # Calculate next retry time
                    delay_minutes = RETRY_DELAY_MINUTES[min(retry_count, len(RETRY_DELAY_MINUTES) - 1)]

                    # Check if enough time has passed since the last failure
                    last_failure_time = None
                    for entry in reversed(status_data.get('history', [])):
                        if entry.get('status') == 'failed':
                            last_failure_time = datetime.datetime.fromisoformat(entry.get('timestamp'))
                            break

                    if last_failure_time:
                        time_since_failure = (datetime.datetime.now() - last_failure_time).total_seconds() / 60

                        if time_since_failure >= delay_minutes:
                            # Time to retry
                            self.retry_job(selection_id, retry_count + 1)
                        else:
                            logger.info(f"Job {selection_id} will be retried in {delay_minutes - time_since_failure:.1f} minutes")

            conn.close()

        except Exception as e:
            logger.error(f"Error checking for failed jobs: {str(e)}")

    def retry_job(self, selection_id, retry_count):
        """Retry a failed job"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # Update retry count in status
            cursor.execute("SELECT status FROM upload_selections WHERE id = ?", (selection_id,))
            row = cursor.fetchone()

            if not row:
                logger.error(f"Job {selection_id} not found")
                return

            status_data = json.loads(row['status'])
            status_data['retry_count'] = retry_count
            status_data['current'] = 'retry_queued'

            # Add to history
            timestamp = datetime.datetime.now().isoformat()
            status_data['history'].append({
                'status': 'retry_queued',
                'timestamp': timestamp,
                'details': {
                    'message': f"Automatic retry #{retry_count} queued",
                    'retry_count': retry_count
                }
            })

            # Update status in database
            cursor.execute(
                "UPDATE upload_selections SET status = ? WHERE id = ?",
                (json.dumps(status_data), selection_id)
            )
            conn.commit()
            conn.close()

            logger.info(f"Queuing retry #{retry_count} for job {selection_id}")

            # Queue the job for processing
            if self.queue_job_callback:
                self.queue_job_callback(selection_id)

        except Exception as e:
            logger.error(f"Error retrying job {selection_id}: {str(e)}")

    def mark_job_no_retry(self, selection_id):
        """Mark a job to not be retried automatically"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # Update status
            cursor.execute("SELECT status FROM upload_selections WHERE id = ?", (selection_id,))
            row = cursor.fetchone()

            if not row:
                logger.error(f"Job {selection_id} not found")
                return False

            status_data = json.loads(row['status'])
            status_data['no_retry'] = True

            # Add to history
            timestamp = datetime.datetime.now().isoformat()
            status_data['history'].append({
                'status': 'no_retry',
                'timestamp': timestamp,
                'details': {
                    'message': "Job marked to not retry automatically"
                }
            })

            # Update status in database
            cursor.execute(
                "UPDATE upload_selections SET status = ? WHERE id = ?",
                (json.dumps(status_data), selection_id)
            )
            conn.commit()
            conn.close()

            logger.info(f"Job {selection_id} marked to not retry automatically")
            return True

        except Exception as e:
            logger.error(f"Error marking job {selection_id} as no retry: {str(e)}")
            return False

    def get_retry_stats(self):
        """Get statistics about retried jobs"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # Get counts
            cursor.execute("""
                SELECT
                    COUNT(*) as total_retries,
                    SUM(CASE WHEN status LIKE '%"retry_count":1%' THEN 1 ELSE 0 END) as retry_1,
                    SUM(CASE WHEN status LIKE '%"retry_count":2%' THEN 1 ELSE 0 END) as retry_2,
                    SUM(CASE WHEN status LIKE '%"retry_count":3%' THEN 1 ELSE 0 END) as retry_3,
                    SUM(CASE WHEN status LIKE '%"no_retry":true%' THEN 1 ELSE 0 END) as no_retry,
                    SUM(CASE WHEN status LIKE '%"current":"completed"%' AND status LIKE '%"retry_count"%' THEN 1 ELSE 0 END) as successful_retries
                FROM upload_selections
                WHERE status LIKE '%"retry_count"%'
            """)

            row = cursor.fetchone()
            conn.close()

            return {
                'total_retries': row['total_retries'],
                'retry_1': row['retry_1'],
                'retry_2': row['retry_2'],
                'retry_3': row['retry_3'],
                'no_retry': row['no_retry'],
                'successful_retries': row['successful_retries'],
                'success_rate': round(row['successful_retries'] / row['total_retries'] * 100, 1) if row['total_retries'] > 0 else 0
            }

        except Exception as e:
            logger.error(f"Error getting retry stats: {str(e)}")
            return {
                'total_retries': 0,
                'retry_1': 0,
                'retry_2': 0,
                'retry_3': 0,
                'no_retry': 0,
                'successful_retries': 0,
                'success_rate': 0
            }

    def shutdown(self):
        """Shutdown the retry manager"""
        if self.scheduler and self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("Job Retry Manager shutdown")

# Singleton instance
_instance = None

def get_retry_manager(scheduler=None, queue_job_callback=None):
    """Get the singleton instance of the retry manager"""
    global _instance
    if _instance is None:
        _instance = JobRetryManager(scheduler, queue_job_callback)
    return _instance

if __name__ == "__main__":
    # Test the retry manager
    retry_manager = get_retry_manager()
    retry_manager.check_for_failed_jobs()
    print("Retry stats:", retry_manager.get_retry_stats())
    retry_manager.shutdown()
