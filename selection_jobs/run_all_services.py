import os
import sys
import subprocess
import time
import signal
import logging
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("selection_jobs/all_services.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("all_services")

# Process tracking
processes = {}

def start_service(name, script, port):
    """Start a service process"""
    try:
        # Check if port is already in use
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            logger.warning(f"Port {port} is already in use. {name} may already be running.")
            return None
        
        # Start the process
        cmd = [sys.executable, script]
        logger.info(f"Starting {name} on port {port}: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Wait a bit to make sure it starts
        time.sleep(2)
        
        if process.poll() is not None:
            # Process exited already
            stdout, stderr = process.communicate()
            logger.error(f"{name} failed to start: {stderr}")
            return None
        
        logger.info(f"{name} started with PID {process.pid}")
        return process
        
    except Exception as e:
        logger.error(f"Error starting {name}: {str(e)}")
        return None

def stop_service(name, process):
    """Stop a service process"""
    if not process:
        return
    
    try:
        logger.info(f"Stopping {name} (PID {process.pid})")
        
        # Try to terminate gracefully
        process.terminate()
        
        # Wait for process to terminate
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            # Force kill if it doesn't terminate
            logger.warning(f"{name} did not terminate gracefully, killing...")
            process.kill()
        
        logger.info(f"{name} stopped")
        
    except Exception as e:
        logger.error(f"Error stopping {name}: {str(e)}")

def start_all_services():
    """Start all services"""
    # Start scheduler service
    scheduler_process = start_service(
        "Job Scheduler Service",
        os.path.join(os.path.dirname(__file__), "job_scheduler_service.py"),
        5007
    )
    if scheduler_process:
        processes["scheduler"] = scheduler_process
    
    # Start status service
    status_process = start_service(
        "Job Status Service",
        os.path.join(os.path.dirname(__file__), "job_status_service.py"),
        5008
    )
    if status_process:
        processes["status"] = status_process
    
    # Start admin dashboard
    dashboard_process = start_service(
        "Admin Dashboard",
        os.path.join(os.path.dirname(__file__), "admin_dashboard_service.py"),
        5009
    )
    if dashboard_process:
        processes["dashboard"] = dashboard_process
    
    return len(processes) > 0

def stop_all_services():
    """Stop all services"""
    for name, process in processes.items():
        stop_service(name, process)
    
    processes.clear()

def signal_handler(sig, frame):
    """Handle signals to gracefully shut down"""
    logger.info("Received shutdown signal, stopping services...")
    stop_all_services()
    sys.exit(0)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run all job processing services")
    parser.add_argument("--scheduler-only", action="store_true", help="Run only the scheduler service")
    parser.add_argument("--status-only", action="store_true", help="Run only the status service")
    parser.add_argument("--dashboard-only", action="store_true", help="Run only the admin dashboard")
    args = parser.parse_args()
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if args.scheduler_only:
            # Start only scheduler service
            scheduler_process = start_service(
                "Job Scheduler Service",
                os.path.join(os.path.dirname(__file__), "job_scheduler_service.py"),
                5007
            )
            if scheduler_process:
                processes["scheduler"] = scheduler_process
                
        elif args.status_only:
            # Start only status service
            status_process = start_service(
                "Job Status Service",
                os.path.join(os.path.dirname(__file__), "job_status_service.py"),
                5008
            )
            if status_process:
                processes["status"] = status_process
                
        elif args.dashboard_only:
            # Start only admin dashboard
            dashboard_process = start_service(
                "Admin Dashboard",
                os.path.join(os.path.dirname(__file__), "admin_dashboard_service.py"),
                5009
            )
            if dashboard_process:
                processes["dashboard"] = dashboard_process
                
        else:
            # Start all services
            if not start_all_services():
                logger.error("Failed to start services")
                sys.exit(1)
        
        # Keep the script running
        logger.info("Services started, press Ctrl+C to stop")
        
        # Print access URLs
        if "dashboard" in processes:
            print("\n" + "=" * 60)
            print("Admin Dashboard is running at: http://localhost:5009")
            print("=" * 60 + "\n")
        
        while True:
            # Check if processes are still running
            for name, process in list(processes.items()):
                if process.poll() is not None:
                    logger.error(f"{name} has stopped unexpectedly")
                    processes.pop(name)
            
            if not processes:
                logger.error("All services have stopped")
                break
                
            time.sleep(5)
            
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    finally:
        stop_all_services()
