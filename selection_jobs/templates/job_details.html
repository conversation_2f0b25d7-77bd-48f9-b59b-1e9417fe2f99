<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Details - Job Processing Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .history-item {
            border-left: 3px solid #dee2e6;
            padding-left: 15px;
            position: relative;
            margin-bottom: 15px;
        }
        .history-item::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #6c757d;
        }
        .history-item.status-completed::before {
            background-color: #28a745;
        }
        .history-item.status-failed::before {
            background-color: #dc3545;
        }
        .history-item.status-processing::before,
        .history-item.status-downloading::before,
        .history-item.status-upload_started::before {
            background-color: #17a2b8;
        }
        .history-item.status-pending::before,
        .history-item.status-queued::before {
            background-color: #ffc107;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">Job Processing Dashboard</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/jobs">Jobs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">Users</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/services">Services</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Job #{{ job_id }} Details</h1>
            <div>
                <a href="/jobs" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Jobs
                </a>
                
                {% if job and job.status and job.status.current == 'failed' %}
                    <form action="/jobs/{{ job_id }}/retry" method="post" class="d-inline">
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-arrow-repeat"></i> Retry Job
                        </button>
                    </form>
                {% endif %}
                
                {% if job and job.status and job.status.current in ['pending', 'queued'] %}
                    <form action="/jobs/{{ job_id }}/cancel" method="post" class="d-inline">
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-x-circle"></i> Cancel Job
                        </button>
                    </form>
                {% endif %}
            </div>
        </div>
        
        {% if job and 'error' not in job %}
            <div class="row">
                <!-- Job Status -->
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <span class="badge 
                                    {% if job.status.current == 'completed' %}bg-success
                                    {% elif job.status.current == 'failed' %}bg-danger
                                    {% elif job.status.current in ['processing', 'downloading', 'upload_started'] %}bg-info
                                    {% elif job.status.current in ['pending', 'queued'] %}bg-warning text-dark
                                    {% else %}bg-secondary{% endif %}"
                                    style="font-size: 1rem; padding: 0.5rem 1rem;">
                                    {{ job.status.current }}
                                </span>
                            </div>
                            
                            <p><strong>Last Updated:</strong> {{ job.status.last_updated }}</p>
                            
                            {% if job.status.is_active %}
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i> This job is currently being processed
                                </div>
                            {% endif %}
                            
                            {% if job.status.in_queue %}
                                <div class="alert alert-warning">
                                    <i class="bi bi-hourglass-split"></i> This job is in queue
                                    {% if job.status.queue_position %}
                                        (Position: {{ job.status.queue_position }})
                                    {% endif %}
                                </div>
                            {% endif %}
                            
                            {% if job.status.retry_count %}
                                <div class="alert alert-warning">
                                    <i class="bi bi-arrow-repeat"></i> Retry attempt: {{ job.status.retry_count }} of 3
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Job Info -->
                <div class="col-md-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Job Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>ID:</strong> {{ job_id }}</p>
                                    <p><strong>User:</strong> {{ job.user_email }}</p>
                                    <p><strong>Created:</strong> {{ job.created_at }}</p>
                                    <p><strong>Source:</strong> {{ job.selection.source }}</p>
                                </div>
                                <div class="col-md-6">
                                    {% if job.selection.scheduleDate %}
                                        <p><strong>Scheduled For:</strong> {{ job.selection.scheduleDate }}</p>
                                    {% endif %}
                                    <p><strong>Upload Within Hour:</strong> {{ 'Yes' if job.selection.uploadWithinHour else 'No' }}</p>
                                    <p><strong>Title:</strong> {{ job.selection.title or 'Using source title' }}</p>
                                </div>
                            </div>
                            
                            {% if job.selection.videoLink %}
                                <div class="mt-3">
                                    <p><strong>Video Link:</strong></p>
                                    <a href="{{ job.selection.videoLink }}" target="_blank" class="d-block text-truncate">
                                        {{ job.selection.videoLink }}
                                    </a>
                                </div>
                            {% endif %}
                            
                            {% if job.selection.destinations %}
                                <div class="mt-3">
                                    <p><strong>Destinations:</strong></p>
                                    <ul class="list-group">
                                        {% for dest in job.selection.destinations %}
                                            <li class="list-group-item">
                                                <strong>{{ dest.name }}</strong>
                                                {% if dest.playlist and dest.playlist.type != 'none' %}
                                                    <div class="small text-muted">
                                                        Playlist: {{ dest.playlist.channel_name }} 
                                                        ({{ dest.playlist.type }} playlist)
                                                    </div>
                                                {% endif %}
                                            </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Status History -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Status History</h5>
                        </div>
                        <div class="card-body">
                            {% if job.status.history %}
                                <div class="history-timeline">
                                    {% for entry in job.status.history|reverse %}
                                        <div class="history-item status-{{ entry.status }}">
                                            <div class="d-flex justify-content-between">
                                                <h6>{{ entry.status }}</h6>
                                                <span class="text-muted">{{ entry.timestamp }}</span>
                                            </div>
                                            {% if entry.details %}
                                                <div class="mt-2">
                                                    {% if entry.details.message %}
                                                        <p>{{ entry.details.message }}</p>
                                                    {% endif %}
                                                    
                                                    {% if entry.details and entry.details is mapping and entry.details.keys()|list|length > 1 %}
                                                        <pre>{{ entry.details|tojson(indent=2) }}</pre>
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class="text-muted">No history available</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Raw Data -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Raw Data</h5>
                        </div>
                        <div class="card-body">
                            <ul class="nav nav-tabs" id="rawDataTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="status-tab" data-bs-toggle="tab" data-bs-target="#status" type="button" role="tab">Status</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="selection-tab" data-bs-toggle="tab" data-bs-target="#selection" type="button" role="tab">Selection</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="full-tab" data-bs-toggle="tab" data-bs-target="#full" type="button" role="tab">Full JSON</button>
                                </li>
                            </ul>
                            <div class="tab-content mt-3" id="rawDataTabsContent">
                                <div class="tab-pane fade show active" id="status" role="tabpanel">
                                    <pre>{{ job.status|tojson(indent=2) }}</pre>
                                </div>
                                <div class="tab-pane fade" id="selection" role="tabpanel">
                                    <pre>{{ job.selection|tojson(indent=2) }}</pre>
                                </div>
                                <div class="tab-pane fade" id="full" role="tabpanel">
                                    <pre>{{ job|tojson(indent=2) }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="alert alert-danger">
                {% if job and 'error' in job %}
                    {{ job.error }}
                {% else %}
                    Failed to load job details
                {% endif %}
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh the page every 10 seconds if job is active
        {% if job and job.status and job.status.current in ['pending', 'queued', 'processing', 'downloading', 'upload_started'] %}
            setTimeout(function() {
                location.reload();
            }, 10000);
        {% endif %}
    </script>
</body>
</html>
