<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Processing Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .card-dashboard {
            transition: transform 0.2s;
        }
        .card-dashboard:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .service-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-healthy {
            background-color: #28a745;
        }
        .status-error {
            background-color: #dc3545;
        }
        .status-unknown {
            background-color: #6c757d;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">Job Processing Dashboard</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/jobs">Jobs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">Users</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/services">Services</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <h1 class="mb-4">Dashboard</h1>
        
        <!-- Service Status -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Service Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>
                                <span class="service-status {% if health.scheduler.status == 'healthy' %}status-healthy{% elif health.scheduler.status == 'error' %}status-error{% else %}status-unknown{% endif %}"></span>
                                Scheduler Service
                            </span>
                            <span class="badge {% if health.scheduler.status == 'healthy' %}bg-success{% elif health.scheduler.status == 'error' %}bg-danger{% else %}bg-secondary{% endif %}">
                                {{ health.scheduler.status }}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>
                                <span class="service-status {% if health.status_service.status == 'healthy' %}status-healthy{% elif health.status_service.status == 'error' %}status-error{% else %}status-unknown{% endif %}"></span>
                                Status Service
                            </span>
                            <span class="badge {% if health.status_service.status == 'healthy' %}bg-success{% elif health.status_service.status == 'error' %}bg-danger{% else %}bg-secondary{% endif %}">
                                {{ health.status_service.status }}
                            </span>
                        </div>
                        <div class="mt-3">
                            <a href="/services" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Worker Pool</h5>
                    </div>
                    <div class="card-body">
                        {% if health.scheduler.worker_pool %}
                            <div class="row">
                                <div class="col-6">
                                    <p class="mb-1">Active Workers: <strong>{{ health.scheduler.worker_pool.workers.current }}</strong></p>
                                    <p class="mb-1">Active Jobs: <strong>{{ health.scheduler.worker_pool.jobs.active }}</strong></p>
                                    <p class="mb-1">Queued Jobs: <strong>{{ health.scheduler.worker_pool.jobs.queued }}</strong></p>
                                </div>
                                <div class="col-6">
                                    <p class="mb-1">CPU: <strong>{{ health.scheduler.worker_pool.resources.cpu_percent }}%</strong></p>
                                    <p class="mb-1">Memory: <strong>{{ health.scheduler.worker_pool.resources.memory_percent }}%</strong></p>
                                    <p class="mb-1">Avg. Job Time: <strong>{{ health.scheduler.worker_pool.jobs.avg_time }}s</strong></p>
                                </div>
                            </div>
                        {% else %}
                            <p class="text-muted">Worker pool information not available</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Job Statistics -->
        <div class="row mb-4">
            <div class="col-12">
                <h4>Job Statistics</h4>
            </div>
            
            {% if stats and stats.counts %}
                <div class="col-md-3">
                    <div class="card card-dashboard bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title">Total Jobs</h5>
                            <h2 class="display-4">{{ stats.counts.total }}</h2>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card card-dashboard bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title">Completed</h5>
                            <h2 class="display-4">{{ stats.counts.completed }}</h2>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card card-dashboard bg-warning text-dark">
                        <div class="card-body">
                            <h5 class="card-title">Pending/Queued</h5>
                            <h2 class="display-4">{{ stats.counts.pending + stats.counts.queued }}</h2>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card card-dashboard bg-danger text-white">
                        <div class="card-body">
                            <h5 class="card-title">Failed</h5>
                            <h2 class="display-4">{{ stats.counts.failed }}</h2>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="col-12">
                    <div class="alert alert-warning">
                        Job statistics not available
                    </div>
                </div>
            {% endif %}
        </div>
        
        <!-- Recent Jobs -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Recent Jobs</h5>
                        <a href="/jobs" class="btn btn-sm btn-primary">View All Jobs</a>
                    </div>
                    <div class="card-body">
                        {% if recent_jobs %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>User</th>
                                            <th>Source</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for job in recent_jobs %}
                                            <tr>
                                                <td>{{ job.id }}</td>
                                                <td>{{ job.user_email }}</td>
                                                <td>{{ job.source }}</td>
                                                <td>
                                                    <span class="badge 
                                                        {% if job.status == 'completed' %}bg-success
                                                        {% elif job.status == 'failed' %}bg-danger
                                                        {% elif job.status in ['processing', 'downloading', 'upload_started'] %}bg-info
                                                        {% elif job.status in ['pending', 'queued'] %}bg-warning text-dark
                                                        {% else %}bg-secondary{% endif %}">
                                                        {{ job.status }}
                                                    </span>
                                                </td>
                                                <td>{{ job.created_at }}</td>
                                                <td>
                                                    <a href="/jobs/{{ job.id }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">No recent jobs found</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh the page every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
