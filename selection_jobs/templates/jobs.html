<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jobs - Job Processing Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">Job Processing Dashboard</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/jobs">Jobs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">Users</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/services">Services</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <h1 class="mb-4">Jobs</h1>
        
        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Filters</h5>
            </div>
            <div class="card-body">
                <form action="/jobs" method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>Pending</option>
                            <option value="queued" {% if current_status == 'queued' %}selected{% endif %}>Queued</option>
                            <option value="processing" {% if current_status == 'processing' %}selected{% endif %}>Processing</option>
                            <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>Completed</option>
                            <option value="failed" {% if current_status == 'failed' %}selected{% endif %}>Failed</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="sanatana_email" class="form-label">User</label>
                        <select name="sanatana_email" id="sanatana_email" class="form-select">
                            <option value="">All Users</option>
                            {% for user in users %}
                                <option value="{{ user.id }}" {% if current_sanatana_email|string == user.id|string %}selected{% endif %}>
                                    {{ user.email }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="limit" class="form-label">Limit</label>
                        <select name="limit" id="limit" class="form-select">
                            <option value="10" {% if current_limit == 10 %}selected{% endif %}>10</option>
                            <option value="50" {% if current_limit == 50 %}selected{% endif %}>50</option>
                            <option value="100" {% if current_limit == 100 %}selected{% endif %}>100</option>
                            <option value="500" {% if current_limit == 500 %}selected{% endif %}>500</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Jobs List -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Jobs List</h5>
            </div>
            <div class="card-body">
                {% if jobs %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>User</th>
                                    <th>Source</th>
                                    <th>Title</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for job in jobs %}
                                    <tr>
                                        <td>{{ job.id }}</td>
                                        <td>{{ job.user_email }}</td>
                                        <td>{{ job.source }}</td>
                                        <td>{{ job.title|truncate(30) }}</td>
                                        <td>
                                            <span class="badge 
                                                {% if job.status == 'completed' %}bg-success
                                                {% elif job.status == 'failed' %}bg-danger
                                                {% elif job.status in ['processing', 'downloading', 'upload_started'] %}bg-info
                                                {% elif job.status in ['pending', 'queued'] %}bg-warning text-dark
                                                {% else %}bg-secondary{% endif %}">
                                                {{ job.status }}
                                            </span>
                                        </td>
                                        <td>{{ job.created_at }}</td>
                                        <td>{{ job.last_updated }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="/jobs/{{ job.id }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                {% if job.status == 'failed' %}
                                                    <form action="/jobs/{{ job.id }}/retry" method="post" class="d-inline">
                                                        <button type="submit" class="btn btn-sm btn-outline-success">
                                                            <i class="bi bi-arrow-repeat"></i>
                                                        </button>
                                                    </form>
                                                {% endif %}
                                                {% if job.status in ['pending', 'queued'] %}
                                                    <form action="/jobs/{{ job.id }}/cancel" method="post" class="d-inline">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="bi bi-x-circle"></i>
                                                        </button>
                                                    </form>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        No jobs found matching the current filters.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh the page every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
