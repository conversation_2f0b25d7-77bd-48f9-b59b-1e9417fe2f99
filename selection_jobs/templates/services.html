<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services - Job Processing Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .service-status {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy {
            background-color: #28a745;
        }
        .status-error {
            background-color: #dc3545;
        }
        .status-unknown {
            background-color: #6c757d;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">Job Processing Dashboard</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/jobs">Jobs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">Users</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/services">Services</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Services Status</h1>
            <button id="refreshBtn" class="btn btn-primary">
                <i class="bi bi-arrow-repeat"></i> Refresh
            </button>
        </div>
        
        <div class="row">
            <!-- Scheduler Service -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <span class="service-status {% if health.scheduler.status == 'healthy' %}status-healthy{% elif health.scheduler.status == 'error' %}status-error{% else %}status-unknown{% endif %}"></span>
                        <h5 class="card-title mb-0">Scheduler Service</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <span class="badge {% if health.scheduler.status == 'healthy' %}bg-success{% elif health.scheduler.status == 'error' %}bg-danger{% else %}bg-secondary{% endif %}">
                                {{ health.scheduler.status }}
                            </span>
                            <span class="ms-2">Port: 5007</span>
                        </div>
                        
                        {% if health.scheduler.status == 'healthy' %}
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6>Scheduled Jobs</h6>
                                    <p class="mb-1">
                                        <strong>{{ health.scheduler.scheduled_jobs }}</strong> jobs scheduled
                                    </p>
                                </div>
                                
                                {% if health.scheduler.worker_pool %}
                                    <div class="col-md-6">
                                        <h6>Worker Pool</h6>
                                        <p class="mb-1">
                                            Workers: <strong>{{ health.scheduler.worker_pool.workers.current }}/{{ health.scheduler.worker_pool.workers.max }}</strong>
                                        </p>
                                        <p class="mb-1">
                                            Active Jobs: <strong>{{ health.scheduler.worker_pool.jobs.active }}</strong>
                                        </p>
                                        <p class="mb-1">
                                            Queued Jobs: <strong>{{ health.scheduler.worker_pool.jobs.queued }}</strong>
                                        </p>
                                    </div>
                                {% endif %}
                            </div>
                            
                            {% if health.scheduler.worker_pool %}
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h6>Resources</h6>
                                        <div class="progress mb-2" style="height: 20px;">
                                            <div class="progress-bar {% if health.scheduler.worker_pool.resources.cpu_percent > 80 %}bg-danger{% elif health.scheduler.worker_pool.resources.cpu_percent > 60 %}bg-warning{% else %}bg-success{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ health.scheduler.worker_pool.resources.cpu_percent }}%;" 
                                                aria-valuenow="{{ health.scheduler.worker_pool.resources.cpu_percent }}" 
                                                aria-valuemin="0" 
                                                aria-valuemax="100">
                                                CPU: {{ health.scheduler.worker_pool.resources.cpu_percent }}%
                                            </div>
                                        </div>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar {% if health.scheduler.worker_pool.resources.memory_percent > 80 %}bg-danger{% elif health.scheduler.worker_pool.resources.memory_percent > 60 %}bg-warning{% else %}bg-success{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ health.scheduler.worker_pool.resources.memory_percent }}%;" 
                                                aria-valuenow="{{ health.scheduler.worker_pool.resources.memory_percent }}" 
                                                aria-valuemin="0" 
                                                aria-valuemax="100">
                                                Memory: {{ health.scheduler.worker_pool.resources.memory_percent }}%
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Job Statistics</h6>
                                        <p class="mb-1">
                                            Processed: <strong>{{ health.scheduler.worker_pool.jobs.processed }}</strong>
                                        </p>
                                        <p class="mb-1">
                                            Succeeded: <strong>{{ health.scheduler.worker_pool.jobs.succeeded }}</strong>
                                        </p>
                                        <p class="mb-1">
                                            Failed: <strong>{{ health.scheduler.worker_pool.jobs.failed }}</strong>
                                        </p>
                                        <p class="mb-1">
                                            Avg. Time: <strong>{{ health.scheduler.worker_pool.jobs.avg_time }}s</strong>
                                        </p>
                                    </div>
                                </div>
                                
                                {% if health.scheduler.worker_pool.jobs.active_ids %}
                                    <div class="mb-3">
                                        <h6>Active Jobs</h6>
                                        <ul class="list-group">
                                            {% for job_id in health.scheduler.worker_pool.jobs.active_ids %}
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Job #{{ job_id }}
                                                    <a href="/jobs/{{ job_id }}" class="btn btn-sm btn-outline-primary">View</a>
                                                </li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                {% endif %}
                            {% endif %}
                            
                            {% if health.scheduler.retry_manager %}
                                <div class="mb-3">
                                    <h6>Retry Manager</h6>
                                    <p class="mb-1">
                                        Total Retries: <strong>{{ health.scheduler.retry_manager.total_retries }}</strong>
                                    </p>
                                    <p class="mb-1">
                                        Success Rate: <strong>{{ health.scheduler.retry_manager.success_rate }}%</strong>
                                    </p>
                                    <p class="mb-1">
                                        Retry Attempts: 
                                        <strong>1st:</strong> {{ health.scheduler.retry_manager.retry_1 }},
                                        <strong>2nd:</strong> {{ health.scheduler.retry_manager.retry_2 }},
                                        <strong>3rd:</strong> {{ health.scheduler.retry_manager.retry_3 }}
                                    </p>
                                </div>
                            {% endif %}
                        {% elif health.scheduler.status == 'error' %}
                            <div class="alert alert-danger">
                                {% if health.scheduler.message %}
                                    {{ health.scheduler.message }}
                                {% else %}
                                    Service is not responding properly
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="alert alert-secondary">
                                Status information not available
                            </div>
                        {% endif %}
                        
                        <div class="mt-3">
                            <a href="{{ SCHEDULER_SERVICE_URL }}/health" target="_blank" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-box-arrow-up-right"></i> View Raw Health Data
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Status Service -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <span class="service-status {% if health.status_service.status == 'healthy' %}status-healthy{% elif health.status_service.status == 'error' %}status-error{% else %}status-unknown{% endif %}"></span>
                        <h5 class="card-title mb-0">Status Service</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <span class="badge {% if health.status_service.status == 'healthy' %}bg-success{% elif health.status_service.status == 'error' %}bg-danger{% else %}bg-secondary{% endif %}">
                                {{ health.status_service.status }}
                            </span>
                            <span class="ms-2">Port: 5008</span>
                        </div>
                        
                        {% if health.status_service.status == 'healthy' %}
                            <div class="mb-3">
                                <h6>Service Information</h6>
                                <p>The status service provides API endpoints for clients to check job status and list jobs.</p>
                            </div>
                            
                            {% if health.status_service.scheduler %}
                                <div class="mb-3">
                                    <h6>Connected to Scheduler</h6>
                                    <span class="badge {% if health.status_service.scheduler.status == 'healthy' %}bg-success{% else %}bg-danger{% endif %}">
                                        {{ health.status_service.scheduler.status }}
                                    </span>
                                </div>
                            {% endif %}
                        {% elif health.status_service.status == 'error' %}
                            <div class="alert alert-danger">
                                {% if health.status_service.message %}
                                    {{ health.status_service.message }}
                                {% else %}
                                    Service is not responding properly
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="alert alert-secondary">
                                Status information not available
                            </div>
                        {% endif %}
                        
                        <div class="mt-3">
                            <a href="{{ STATUS_SERVICE_URL }}/health" target="_blank" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-box-arrow-up-right"></i> View Raw Health Data
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Admin Dashboard -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <span class="service-status status-healthy"></span>
                        <h5 class="card-title mb-0">Admin Dashboard</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <span class="badge bg-success">healthy</span>
                            <span class="ms-2">Port: 5009</span>
                        </div>
                        
                        <div class="mb-3">
                            <h6>Service Information</h6>
                            <p>The admin dashboard provides a web interface for monitoring and managing jobs.</p>
                        </div>
                        
                        <div class="mb-3">
                            <h6>Available Pages</h6>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <a href="/">Dashboard</a> - Overview of system status
                                </li>
                                <li class="list-group-item">
                                    <a href="/jobs">Jobs</a> - List and manage jobs
                                </li>
                                <li class="list-group-item">
                                    <a href="/users">Users</a> - View user information
                                </li>
                                <li class="list-group-item">
                                    <a href="/services">Services</a> - Monitor service status
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Raw Data -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Raw Health Data</h5>
                    </div>
                    <div class="card-body">
                        <pre>{{ health|tojson(indent=2) }}</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', function() {
            location.reload();
        });
        
        // Auto-refresh the page every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
