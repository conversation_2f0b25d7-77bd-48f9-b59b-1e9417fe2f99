<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Details - Job Processing Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">Job Processing Dashboard</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/jobs">Jobs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/users">Users</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/services">Services</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>User Details</h1>
            <a href="/users" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Users
            </a>
        </div>
        
        <div class="row">
            <!-- User Info -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">User Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>ID:</strong> {{ user.id }}</p>
                        <p><strong>Email:</strong> {{ user.email }}</p>
                        <p><strong>Name:</strong> {{ user.name }}</p>
                        
                        {% if user.created_at %}
                            <p><strong>Created:</strong> {{ user.created_at }}</p>
                        {% endif %}
                        
                        <div class="mt-3">
                            <a href="/jobs?sanatana_email={{ user.id }}" class="btn btn-primary">
                                <i class="bi bi-list-task"></i> View User's Jobs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- User Stats -->
            <div class="col-md-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Job Statistics</h5>
                    </div>
                    <div class="card-body">
                        {% if jobs %}
                            {% set completed_jobs = jobs|selectattr('status', 'equalto', 'completed')|list %}
                            {% set failed_jobs = jobs|selectattr('status', 'equalto', 'failed')|list %}
                            {% set pending_jobs = jobs|selectattr('status', 'in', ['pending', 'queued'])|list %}
                            {% set processing_jobs = jobs|selectattr('status', 'in', ['processing', 'downloading', 'upload_started'])|list %}
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h3>{{ jobs|length }}</h3>
                                            <p class="mb-0">Total Jobs</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3>{{ completed_jobs|length }}</h3>
                                            <p class="mb-0">Completed</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-dark">
                                        <div class="card-body text-center">
                                            <h3>{{ pending_jobs|length }}</h3>
                                            <p class="mb-0">Pending</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body text-center">
                                            <h3>{{ failed_jobs|length }}</h3>
                                            <p class="mb-0">Failed</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h6>Recent Jobs</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Source</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for job in jobs[:5] %}
                                                <tr>
                                                    <td>{{ job.id }}</td>
                                                    <td>{{ job.source }}</td>
                                                    <td>
                                                        <span class="badge 
                                                            {% if job.status == 'completed' %}bg-success
                                                            {% elif job.status == 'failed' %}bg-danger
                                                            {% elif job.status in ['processing', 'downloading', 'upload_started'] %}bg-info
                                                            {% elif job.status in ['pending', 'queued'] %}bg-warning text-dark
                                                            {% else %}bg-secondary{% endif %}">
                                                            {{ job.status }}
                                                        </span>
                                                    </td>
                                                    <td>{{ job.created_at }}</td>
                                                    <td>
                                                        <a href="/jobs/{{ job.id }}" class="btn btn-sm btn-outline-primary">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                
                                {% if jobs|length > 5 %}
                                    <div class="text-center mt-2">
                                        <a href="/jobs?sanatana_email={{ user.id }}" class="btn btn-sm btn-outline-primary">
                                            View All {{ jobs|length }} Jobs
                                        </a>
                                    </div>
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                This user has no jobs.
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
