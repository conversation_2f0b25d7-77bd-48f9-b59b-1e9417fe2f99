
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    server_names_hash_bucket_size 128;

    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    # DEV Subdomain (dev.sanatanamedia.com)
    server {
        listen 80;
        server_name dev.sanatanamedia.com;

        return 301 https://$host$request_uri;
    
        location / {
            proxy_pass http://localhost:6003;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 80;
        server_name dev-signin.sanatanamedia.com;       
        
        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:6002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 80;
        server_name dev-service.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:6000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 80;
        server_name dev-files.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:6004;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 80;
        server_name dev-jobstatus.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:6008;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
     server {
        listen 80;
        server_name dev-admindashboard.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:6009;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
    server {
        listen 80;
        server_name dev-ytdown.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:6006;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
    # SSL configs
    #
    
    server {
        listen 443 ssl;
        server_name dev.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";
    
        location / {
            proxy_pass http://localhost:6003;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
       listen 443 ssl;
        server_name dev-signin.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:6002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 443 ssl;
        server_name dev-service.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:6000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 443 ssl;
        server_name dev-files.sanatanamedia.com;
        
        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:6004;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 443 ssl;
        server_name dev-jobstatus.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";


        location / {
            proxy_pass http://localhost:6008;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

     server {
        listen 443 ssl;
        server_name dev-admindashboard.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:6009;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
    server {
        listen 443 ssl;
        server_name dev-ytdown.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:6006;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
    
    #
    #  Main domain (www.sanatanamedia.com)
    #

    # Main Subdomain (sanatanamedia.com)
    server {
        listen 80;
        server_name sanatanamedia.com www.sanatanamedia.com;

        return 301 https://$host$request_uri;
    
        location / {
            proxy_pass http://localhost:5003;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 80;
        server_name signin.sanatanamedia.com;       
        
        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:5002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 80;
        server_name service.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 80;
        server_name files.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:5004;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 80;
        server_name jobstatus.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:5008;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
     server {
        listen 80;
        server_name admindashboard.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:5009;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
    server {
        listen 80;
        server_name ytdown.sanatanamedia.com;

        return 301 https://$host$request_uri;
        location / {
            proxy_pass http://localhost:5006;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
    # SSL configs
    #
    
    server {
        listen 443 ssl;
        server_name sanatanamedia.com www.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";
    
        location / {
            proxy_pass http://localhost:5003;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
       listen 443 ssl;
        server_name signin.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:5002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 443 ssl;
        server_name service.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 443 ssl;
        server_name files.sanatanamedia.com;
        
        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:5004;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    server {
        listen 443 ssl;
        server_name jobstatus.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";


        location / {
            proxy_pass http://localhost:5008;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

     server {
        listen 443 ssl;
        server_name admindashboard.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:5009;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
    server {
        listen 443 ssl;
        server_name ytdown.sanatanamedia.com;

        ssl_certificate     "/etc/ssl/sanatanamedia/fullchain.pem";
        ssl_certificate_key "/etc/ssl/sanatanamedia/private_key.key";

        location / {
            proxy_pass http://localhost:5006;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
